<?php
/**
 * Modèle commun pour les fonctions partagées
 *
 * Ce fichier contient les fonctions d'accès aux données communes à plusieurs parties de l'application
 * pour éviter la redondance de code et faciliter la maintenance.
 */

// Inclure la connexion à la base de données
require_once __DIR__ . '/../config/db.php';

/**
 * Récupère les semestres pour le filtrage
 *
 * @param int $niveauId Optionnel - ID du niveau pour filtrer les semestres
 * @return array Tableau des semestres
 */
function getSemesters($niveauId = null) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    error_log("Récupération des semestres pour le niveau ID: " . ($niveauId ? $niveauId : "non spécifié"));

    // Vérifier d'abord si la table semestre a une colonne niveau_id ou id_niveau
    $columnsQuery = "SHOW COLUMNS FROM semestre";
    $columnsResult = mysqli_query($conn, $columnsQuery);

    $hasNiveauIdColumn = false;
    $niveauIdColumnName = '';

    if ($columnsResult) {
        while ($column = mysqli_fetch_assoc($columnsResult)) {
            if ($column['Field'] === 'niveau_id' || $column['Field'] === 'id_niveau') {
                $hasNiveauIdColumn = true;
                $niveauIdColumnName = $column['Field'];
                break;
            }
        }
    }

    error_log("Table semestre a une colonne niveau_id: " . ($hasNiveauIdColumn ? "Oui ($niveauIdColumnName)" : "Non"));

    // Si un ID de niveau est fourni, filtrer les semestres par niveau
    if ($niveauId) {
        $niveauId = mysqli_real_escape_string($conn, $niveauId);

        // Récupérer le nom du niveau pour déterminer quels semestres afficher
        $niveauQuery = "SELECT nom FROM niveaux WHERE id = '$niveauId'";
        $niveauResult = mysqli_query($conn, $niveauQuery);

        if ($niveauResult && $niveauRow = mysqli_fetch_assoc($niveauResult)) {
            $niveauNom = strtolower($niveauRow['nom']);
            error_log("Nom du niveau trouvé: " . $niveauNom);

            // Si la table semestre a une colonne niveau_id, l'utiliser pour le filtrage
            if ($hasNiveauIdColumn) {
                $query = "SELECT DISTINCT id, nom FROM semestre WHERE $niveauIdColumnName = '$niveauId' ORDER BY nom";
                error_log("Utilisation de la colonne $niveauIdColumnName pour filtrer les semestres");
            } else {
                // Sinon, déterminer les semestres à afficher en fonction du nom du niveau
                error_log("Détermination des semestres en fonction du nom du niveau: " . $niveauNom);

                if (strpos($niveauNom, 'licence 1') !== false || strpos($niveauNom, 'l1') !== false) {
                    $query = "SELECT DISTINCT id, nom FROM semestre WHERE nom IN ('S1', 'S2') ORDER BY nom";
                    error_log("Niveau Licence 1 détecté, affichage des semestres S1 et S2 uniquement");
                } elseif (strpos($niveauNom, 'licence 2') !== false || strpos($niveauNom, 'l2') !== false) {
                    $query = "SELECT DISTINCT id, nom FROM semestre WHERE nom IN ('S3', 'S4') ORDER BY nom";
                    error_log("Niveau Licence 2 détecté, affichage des semestres S3 et S4 uniquement");
                } elseif (strpos($niveauNom, 'licence 3') !== false || strpos($niveauNom, 'l3') !== false) {
                    $query = "SELECT DISTINCT id, nom FROM semestre WHERE nom IN ('S5', 'S6') ORDER BY nom";
                    error_log("Niveau Licence 3 détecté, affichage des semestres S5 et S6 uniquement");
                } elseif (strpos($niveauNom, 'master 1') !== false || strpos($niveauNom, 'm1') !== false) {
                    $query = "SELECT DISTINCT id, nom FROM semestre WHERE nom IN ('S7', 'S8') ORDER BY nom";
                    error_log("Niveau Master 1 détecté, affichage des semestres S7 et S8 uniquement");
                } elseif (strpos($niveauNom, 'master 2') !== false || strpos($niveauNom, 'm2') !== false) {
                    $query = "SELECT DISTINCT id, nom FROM semestre WHERE nom IN ('S9', 'S10') ORDER BY nom";
                    error_log("Niveau Master 2 détecté, affichage des semestres S9 et S10 uniquement");
                } else {
                    // Si le niveau n'est pas reconnu, utiliser une requête générique limitée à S1-S5
                    $query = "SELECT DISTINCT id, nom FROM semestre WHERE nom IN ('S1', 'S2', 'S3', 'S4', 'S5') ORDER BY nom";
                    error_log("Niveau non reconnu, affichage des semestres S1 à S5 uniquement par défaut");
                }
            }
        } else {
            // Si le niveau n'est pas trouvé, limiter aux semestres S1-S5 par défaut
            $query = "SELECT DISTINCT id, nom FROM semestre WHERE nom IN ('S1', 'S2', 'S3', 'S4', 'S5') ORDER BY nom";
            error_log("Niveau ID $niveauId non trouvé, affichage des semestres S1 à S5 uniquement par défaut");
        }
    } else {
        // Si aucun niveau n'est sélectionné, afficher uniquement S1 à S5 (valeurs codées en dur)
        // Utiliser DISTINCT pour éviter les doublons
        $query = "SELECT DISTINCT id, nom FROM semestre WHERE nom IN ('S1', 'S2', 'S3', 'S4', 'S5') ORDER BY nom";
        error_log("Aucun niveau sélectionné, affichage des semestres S1 à S5 uniquement (valeurs codées en dur)");
    }

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Erreur dans getSemesters: " . $error);
        mysqli_close($conn);
        return ['error' => "Erreur lors de la récupération des semestres: " . $error];
    }

    $semesters = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $semesters[] = $row;
    }

    mysqli_close($conn);
    return $semesters;
}

/**
 * Récupère tous les niveaux pour le filtrage
 *
 * @param int $cycleId Optionnel - ID du cycle pour filtrer les niveaux
 * @param int $filiereId Optionnel - ID de la filière pour filtrer les niveaux
 * @return array Tableau des niveaux
 */
function getLevels($cycleId = null, $filiereId = null) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    // Si un ID de filière est fourni, récupérer d'abord l'ID du cycle correspondant
    if ($filiereId && !$cycleId) {
        $filiereId = mysqli_real_escape_string($conn, $filiereId);
        $cycleQuery = "SELECT id_cycle FROM filiere WHERE id_filiere = '$filiereId'";
        $cycleResult = mysqli_query($conn, $cycleQuery);

        if ($cycleResult && $cycleRow = mysqli_fetch_assoc($cycleResult)) {
            $cycleId = $cycleRow['id_cycle'];
            error_log("Cycle ID $cycleId trouvé pour la filière ID $filiereId");
        } else {
            error_log("Aucun cycle trouvé pour la filière ID $filiereId");
        }
    }

    // Construire la requête de base
    if ($cycleId) {
        // Si un ID de cycle est fourni, filtrer les niveaux par cycle
        $cycleId = mysqli_real_escape_string($conn, $cycleId);
        $query = "SELECT n.id, n.nom FROM niveaux n WHERE n.cycle_id = '$cycleId' ORDER BY n.nom";
        error_log("Récupération des niveaux pour le cycle ID $cycleId");
    } else {
        // Sinon, récupérer tous les niveaux
        $query = "SELECT id, nom FROM niveaux ORDER BY nom";
        error_log("Récupération de tous les niveaux");
    }

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Erreur dans getLevels: " . $error);
        mysqli_close($conn);
        return ['error' => "Erreur lors de la récupération des niveaux: " . $error];
    }

    $levels = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $levels[] = $row;
    }

    error_log("Nombre de niveaux trouvés: " . count($levels));

    mysqli_close($conn);
    return $levels;
}

/**
 * Récupère toutes les filières pour le filtrage
 *
 * @param int $departementId Optionnel - ID du département pour filtrer les filières
 * @return array Tableau des filières
 */
function getFilieres($departementId = null) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    $query = "SELECT id_filiere, nom_filiere FROM filiere";

    // Si un ID de département est fourni, filtrer les filières par département
    if ($departementId) {
        $departementId = mysqli_real_escape_string($conn, $departementId);
        $query .= " WHERE departement_id = '$departementId'";
    }

    $query .= " ORDER BY nom_filiere";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Erreur dans getFilieres: " . $error);
        mysqli_close($conn);
        return ['error' => "Erreur lors de la récupération des filières: " . $error];
    }

    $filieres = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $filieres[] = $row;
    }

    mysqli_close($conn);
    return $filieres;
}

/**
 * Récupère tous les départements
 *
 * @return array Tableau des départements
 */
function getDepartements() {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    $query = "SELECT id, nom FROM departement ORDER BY nom";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Erreur dans getDepartements: " . $error);
        mysqli_close($conn);
        return ['error' => "Erreur lors de la récupération des départements: " . $error];
    }

    $departements = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $departements[] = $row;
    }

    mysqli_close($conn);
    return $departements;
}

/**
 * Récupère tous les cycles
 *
 * @return array Tableau des cycles
 */
function getCycles() {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    $query = "SELECT id, nom FROM cycle ORDER BY nom";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Erreur dans getCycles: " . $error);
        mysqli_close($conn);
        return ['error' => "Erreur lors de la récupération des cycles: " . $error];
    }

    $cycles = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $cycles[] = $row;
    }

    mysqli_close($conn);
    return $cycles;
}

/**
 * Récupère les types d'unités d'enseignement disponibles
 *
 * @return array Tableau des types d'UE
 */
function getUETypes() {
    return ['Cours', 'TD', 'TP'];
}

/**
 * Génère une liste d'années académiques
 *
 * @param int $count Nombre d'années à générer (par défaut 6)
 * @return array Tableau des années académiques
 */
function getAcademicYears($count = 6) {
    $academicYears = [];
    $currentYear = (int)date('Y');

    for ($i = 0; $i < $count; $i++) {
        $year = $currentYear - $i;
        $academicYears[] = $year . '-' . ($year + 1);
    }

    return $academicYears;
}

/**
 * Récupère les informations d'un enseignant par son ID
 *
 * @param int $teacherId ID de l'enseignant
 * @return array Informations de l'enseignant
 */
function getTeacherInfo($teacherId) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    // Sécuriser l'entrée
    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    $query = "SELECT id, nom, prenom, email, specialite_id FROM enseignant WHERE id = '$teacherId'";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Erreur dans getTeacherInfo: " . $error);
        mysqli_close($conn);
        return ['error' => "Erreur lors de la récupération des informations de l'enseignant: " . $error];
    }

    $teacher = mysqli_fetch_assoc($result);

    mysqli_close($conn);
    return $teacher ? $teacher : ['error' => "Enseignant non trouvé"];
}

/**
 * Récupère les informations d'une filière par son ID
 *
 * @param int $filiereId ID de la filière
 * @return array Informations de la filière
 */
function getFiliereInfo($filiereId) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    // Sécuriser l'entrée
    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    $query = "SELECT id_filiere, nom_filiere, departement_id FROM filiere WHERE id_filiere = '$filiereId'";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Erreur dans getFiliereInfo: " . $error);
        mysqli_close($conn);
        return ['error' => "Erreur lors de la récupération des informations de la filière: " . $error];
    }

    $filiere = mysqli_fetch_assoc($result);

    mysqli_close($conn);
    return $filiere ? $filiere : ['error' => "Filière non trouvée"];
}
