<?php
require_once "../model/defineGrpModel.php";
require_once "../utils/response.php";

/**
 * Get all modules for a specific filiere
 *
 * @param int $filiereId Filiere ID
 */
function getModulesByFiliereAPI($filiereId) {
    $modules = getModulesByFiliere($filiereId);

    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error']], 404);
    }

    jsonResponse(['data' => $modules], 200);
}

/**
 * Get all modules for a specific filiere and cycle
 *
 * @param int $filiereId Filiere ID
 * @param int $cycleId Cycle ID
 */
function getModulesByFiliereAndCycleAPI($filiereId, $cycleId) {
    $modules = getModulesByFiliereAndCycle($filiereId, $cycleId);

    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error']], 404);
    }

    jsonResponse(['data' => $modules], 200);
}

/**
 * Get all modules for a specific filiere and niveau
 *
 * @param int $filiereId Filiere ID
 * @param int $niveauId Niveau ID
 */
function getModulesByFiliereAndNiveauAPI($filiereId, $niveauId) {
    $modules = getModulesByFiliereAndNiveau($filiereId, $niveauId);

    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error']], 404);
    }

    jsonResponse(['data' => $modules], 200);
}

/**
 * Update the number of TD and TP groups for a module
 *
 * @param int $moduleId Module ID
 * @param array $data Request data containing nb_groupe_td and nb_groupe_tp
 */
function updateModuleGroupsAPI($moduleId, $data) {
    // Validate required fields
    if (!isset($data['nb_groupe_td']) || !isset($data['nb_groupe_tp'])) {
        jsonResponse(['error' => 'Number of TD and TP groups are required'], 400);
        return;
    }

    // Validate that the values are non-negative integers
    if (!is_numeric($data['nb_groupe_td']) || $data['nb_groupe_td'] < 0 ||
        !is_numeric($data['nb_groupe_tp']) || $data['nb_groupe_tp'] < 0) {
        jsonResponse(['error' => 'Number of groups must be non-negative integers'], 400);
        return;
    }

    $result = updateModuleGroups($moduleId, $data['nb_groupe_td'], $data['nb_groupe_tp']);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
        return;
    }

    jsonResponse([
        'success' => true,
        'message' => 'Module groups updated successfully',
        'td_units_updated' => $result['td_units_updated'] ?? 0,
        'tp_units_updated' => $result['tp_units_updated'] ?? 0
    ], 200);
}

/**
 * Update the number of TD and TP groups for all modules in a cycle
 *
 * @param int $filiereId Filiere ID
 * @param int $cycleId Cycle ID
 * @param array $data Request data containing nb_groupe_td and nb_groupe_tp
 */
function updateModuleGroupsByCycleAPI($filiereId, $cycleId, $data) {
    // Validate required fields
    if (!isset($data['nb_groupe_td']) || !isset($data['nb_groupe_tp'])) {
        jsonResponse(['error' => 'Number of TD and TP groups are required'], 400);
        return;
    }

    // Validate that the values are non-negative integers
    if (!is_numeric($data['nb_groupe_td']) || $data['nb_groupe_td'] < 0 ||
        !is_numeric($data['nb_groupe_tp']) || $data['nb_groupe_tp'] < 0) {
        jsonResponse(['error' => 'Number of groups must be non-negative integers'], 400);
        return;
    }

    $result = updateModuleGroupsByCycle($filiereId, $cycleId, $data['nb_groupe_td'], $data['nb_groupe_tp']);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
        return;
    }

    jsonResponse([
        'success' => true,
        'message' => 'Module groups updated successfully for all modules in cycle',
        'affected_modules' => $result['affected_modules'],
        'td_units_updated' => $result['td_units_updated'] ?? 0,
        'tp_units_updated' => $result['tp_units_updated'] ?? 0
    ], 200);
}

/**
 * Get all niveaux for a specific filiere
 *
 * @param int $filiereId Filiere ID
 */
function getNiveauxByFiliereAPI($filiereId) {
    $niveaux = getNiveauxByFiliere($filiereId);

    if (isset($niveaux['error'])) {
        jsonResponse(['error' => $niveaux['error']], 404);
    }

    jsonResponse(['data' => $niveaux], 200);
}

/**
 * Get all cycles for a specific filiere
 *
 * @param int $filiereId Filiere ID
 */
function getCyclesByFiliereAPI($filiereId) {
    $cycles = getCyclesByFiliere($filiereId);

    if (isset($cycles['error'])) {
        jsonResponse(['error' => $cycles['error']], 404);
    }

    jsonResponse(['data' => $cycles], 200);
}

/**
 * Get all niveaux grouped by cycle for a specific filiere
 *
 * @param int $filiereId Filiere ID
 */
function getNiveauxGroupedByCycleAPI($filiereId) {
    $niveauxByCycle = getNiveauxGroupedByCycle($filiereId);

    if (isset($niveauxByCycle['error'])) {
        jsonResponse(['error' => $niveauxByCycle['error']], 404);
    }

    jsonResponse(['data' => $niveauxByCycle], 200);
}

/**
 * Update multiple modules' groups in a batch
 *
 * @param array $data Array of module updates
 */
function updateModulesGroupsBatchAPI($data) {
    if (!isset($data['modules']) || !is_array($data['modules'])) {
        jsonResponse(['error' => 'Invalid data format. Expected array of modules.'], 400);
        return;
    }

    $results = [];
    $success = true;

    foreach ($data['modules'] as $module) {
        if (!isset($module['id']) || !isset($module['nb_groupe_td']) || !isset($module['nb_groupe_tp'])) {
            $results[] = [
                'id' => $module['id'] ?? 'unknown',
                'success' => false,
                'error' => 'Missing required fields'
            ];
            $success = false;
            continue;
        }

        // Validate that the values are non-negative integers
        if (!is_numeric($module['nb_groupe_td']) || $module['nb_groupe_td'] < 0 ||
            !is_numeric($module['nb_groupe_tp']) || $module['nb_groupe_tp'] < 0) {
            $results[] = [
                'id' => $module['id'],
                'success' => false,
                'error' => 'Number of groups must be non-negative integers'
            ];
            $success = false;
            continue;
        }

        $result = updateModuleGroups($module['id'], $module['nb_groupe_td'], $module['nb_groupe_tp']);

        if (isset($result['error'])) {
            $results[] = [
                'id' => $module['id'],
                'success' => false,
                'error' => $result['error']
            ];
            $success = false;
        } else {
            $results[] = [
                'id' => $module['id'],
                'success' => true
            ];
        }
    }

    jsonResponse([
        'success' => $success,
        'message' => $success ? 'All modules updated successfully' : 'Some modules failed to update',
        'results' => $results
    ], $success ? 200 : 207); // 207 Multi-Status
}