<?php
require_once "../config/db.php";

// Obtenir tous les membres du personnel
function getAllStaff() {
    $conn = getConnection();
    $sql = "SELECT * FROM staff";
    $result = mysqli_query($conn, $sql);

    $staff = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $staff[] = $row;
    }

    mysqli_close($conn);
    return $staff;
}

// Obtenir un membre du personnel par CNI
function getStaffByCNI($cni) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Erreur de connexion à la base de données"];
    }

    $cni = mysqli_real_escape_string($conn, $cni);
    $sql = "SELECT * FROM staff WHERE CNI = '$cni'";
    $result = mysqli_query($conn, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        $staff = mysqli_fetch_assoc($result);
        mysqli_free_result($result);
        mysqli_close($conn);
        return $staff;
    } else {
        mysqli_close($conn);
        return ["error" => "Aucun membre du personnel trouvé avec ce CNI"];
    }
}

// Insérer un nouveau membre du personnel
function insertStaff($CNI, $nom, $prenom, $phone, $sexe, $role) {
    $conn = getConnection();
    
    $sql = "INSERT INTO staff (CNI, nom, prenom, phone, sexe, role) 
            VALUES (?, ?, ?, ?, ?, ?)";
            
    $stmt = mysqli_prepare($conn, $sql);
    if (!$stmt) {
        mysqli_close($conn);
        return ['error' => 'Erreur de préparation de la requête : ' . mysqli_error($conn)];
    }
    
    mysqli_stmt_bind_param($stmt, 'ssssss', 
        $CNI, $nom, $prenom, $phone, $sexe, $role);
    
    if (!mysqli_stmt_execute($stmt)) {
        $error = mysqli_stmt_error($stmt);
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        return ['error' => 'Erreur lors de l\'insertion : ' . $error];
    }
    
    mysqli_stmt_close($stmt);
    mysqli_close($conn);
    return ['success' => true];
}

// Mettre à jour un membre du personnel
function updateStaff($CNI, $nom, $prenom, $phone, $sexe, $role) {
    $conn = getConnection();
    
    $sql = "UPDATE staff 
            SET nom = ?, prenom = ?, phone = ?, sexe = ?, role = ? 
            WHERE CNI = ?";
            
    $stmt = mysqli_prepare($conn, $sql);
    if (!$stmt) {
        mysqli_close($conn);
        return ['error' => 'Erreur de préparation de la requête : ' . mysqli_error($conn)];
    }
    
    mysqli_stmt_bind_param($stmt, 'ssssss', 
        $nom, $prenom, $phone, $sexe, $role, $CNI);
    
    if (!mysqli_stmt_execute($stmt)) {
        $error = mysqli_stmt_error($stmt);
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        return ['error' => 'Erreur lors de la mise à jour : ' . $error];
    }
    
    mysqli_stmt_close($stmt);
    mysqli_close($conn);
    return ['success' => true];
}

// Supprimer un membre du personnel
function deleteStaffByCNI($CNI) {
    $conn = getConnection();
    $CNI = mysqli_real_escape_string($conn, $CNI);

    $sql = "DELETE FROM staff WHERE CNI = '$CNI'";
    $result = mysqli_query($conn, $sql);

    mysqli_close($conn);
    return $result;
}

// Obtenir les rôles disponibles
function getAvailableRoles() {
    $conn = getConnection();
    $sql = "SELECT DISTINCT role FROM staff";
    $result = mysqli_query($conn, $sql);

    $roles = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $roles[] = $row['role'];
    }

    mysqli_close($conn);
    return $roles;
}
?> 