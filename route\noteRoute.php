<?php
// Use a more robust path resolution to find the required files
$controllerPath = __DIR__ . "/../controller/noteController.php";
$utilsPath = __DIR__ . "/../utils/response.php";

// Try different paths for controller
if (file_exists($controllerPath)) {
    require_once $controllerPath;
} else {
    $altControllerPath = __DIR__ . "/../../controller/noteController.php";
    if (file_exists($altControllerPath)) {
        require_once $altControllerPath;
    } else {
        die("Cannot find the noteController.php file.");
    }
}

// Try different paths for utils
if (file_exists($utilsPath)) {
    require_once $utilsPath;
} else {
    $altUtilsPath = __DIR__ . "/../../utils/response.php";
    if (file_exists($altUtilsPath)) {
        require_once $altUtilsPath;
    } else {
        die("Cannot find the response.php file.");
    }
}

// Désactiver l'affichage des erreurs pour éviter de renvoyer du HTML
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Définir le type de contenu comme JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Capturer toutes les erreurs
try {
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            // Get action from query parameters
            $action = isset($_GET['action']) ? $_GET['action'] : '';

            switch ($action) {
                case 'getStudentsByFilters':
                case 'get_student_grades':
                    // Get students by filters
                    $filiereId = isset($_GET['filiere']) ? $_GET['filiere'] : null;
                    $niveauId = isset($_GET['niveau']) ? $_GET['niveau'] : null;
                    $semestre = isset($_GET['semestre']) ? $_GET['semestre'] : null;
                    $session = isset($_GET['session']) ? $_GET['session'] : null;
                    $moduleId = isset($_GET['module']) ? $_GET['module'] : null;

                    if (!$moduleId) {
                        jsonResponse(['error' => 'Module ID is required', 'success' => false], 400);
                        exit;
                    }

                    getStudentsByFiltersAPI($filiereId, $niveauId, $semestre, $session, $moduleId);
                    break;

                case 'get_top_performers':
                    // Get top performing students
                    $moduleId = isset($_GET['module']) ? $_GET['module'] : null;
                    $session = isset($_GET['session']) ? $_GET['session'] : null;
                    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 3;

                    if (!$moduleId) {
                        jsonResponse(['error' => 'Module ID is required', 'success' => false], 400);
                        exit;
                    }

                    getTopNotesByModuleAPI($moduleId, $session, $limit);
                    break;

                default:
                    // Default to getting all notes
                    getAllNotesAPI();
                    break;
            }
            break;

        case 'POST':
            // Get action from query parameters, POST data, or JSON input
            $action = isset($_GET['action']) ? $_GET['action'] : '';

            // If no action in query parameters, try to get from POST or JSON
            if (empty($action)) {
                // Read raw input
                $rawInput = file_get_contents('php://input');

                // Try to decode as JSON
                $jsonData = json_decode($rawInput, true);

                if (isset($_POST['action'])) {
                    $action = $_POST['action'];
                } else if (is_array($jsonData) && isset($jsonData['action'])) {
                    $action = $jsonData['action'];
                }
            }

            error_log("Action: " . $action);

            // Check if we have POST data or JSON input
            $contentType = isset($_SERVER["CONTENT_TYPE"]) ? trim($_SERVER["CONTENT_TYPE"]) : '';

            if (strpos($contentType, 'application/json') !== false) {
                // Using JSON data
                $postData = json_decode(file_get_contents('php://input'), true);
                error_log("Received JSON data: " . print_r($postData, true));
            } else if (isset($_POST['action'])) {
                // Using form data
                $postData = $_POST;
                error_log("Received POST data: " . print_r($_POST, true));
            } else {
                // Try to get JSON data anyway as fallback
                $postData = json_decode(file_get_contents('php://input'), true);
                error_log("Fallback to raw input: " . print_r($postData, true));
            }

            switch ($action) {
                case 'saveGrades':
                    // Save grades
                    if (!isset($postData['grades']) || !is_array($postData['grades'])) {
                        jsonResponse(['error' => 'Grades data is required', 'success' => false], 400);
                        exit;
                    }

                    saveGradesAPI($postData['grades']);
                    break;

                case 'update_student_grade':
                    // Update a student's grade
                    error_log("Processing update_student_grade with data: " . file_get_contents('php://input'));

                    // Récupérer les données depuis le corps de la requête JSON
                    $jsonData = json_decode(file_get_contents('php://input'), true);
                    error_log("Decoded JSON data: " . print_r($jsonData, true));

                    // Récupérer les données soit depuis JSON soit depuis POST
                    $studentId = isset($jsonData['student_id']) ? $jsonData['student_id'] : null;
                    $moduleId = isset($jsonData['module']) ? $jsonData['module'] : null;
                    $niveauId = isset($jsonData['niveau']) ? $jsonData['niveau'] : null;
                    $filiereId = isset($jsonData['filiere']) ? $jsonData['filiere'] : null;
                    $semestre = isset($jsonData['semestre']) ? $jsonData['semestre'] : null;
                    $session = isset($jsonData['session']) ? $jsonData['session'] : null;
                    $grade = isset($jsonData['note']) ? $jsonData['note'] : null;

                    // Si les données ne sont pas dans $jsonData, essayer de les récupérer depuis $postData
                    if (!$studentId && isset($postData['student_id'])) $studentId = $postData['student_id'];
                    if (!$moduleId && isset($postData['module'])) $moduleId = $postData['module'];
                    if (!$niveauId && isset($postData['niveau'])) $niveauId = $postData['niveau'];
                    if (!$filiereId && isset($postData['filiere'])) $filiereId = $postData['filiere'];
                    if (!$semestre && isset($postData['semestre'])) $semestre = $postData['semestre'];
                    if (!$session && isset($postData['session'])) $session = $postData['session'];
                    if ($grade === null && isset($postData['note'])) $grade = $postData['note'];

                    // Si les données ne sont pas dans $postData, essayer de les récupérer directement depuis $_POST
                    if (!$studentId && isset($_POST['student_id'])) $studentId = $_POST['student_id'];
                    if (!$moduleId && isset($_POST['module'])) $moduleId = $_POST['module'];
                    if (!$niveauId && isset($_POST['niveau'])) $niveauId = $_POST['niveau'];
                    if (!$filiereId && isset($_POST['filiere'])) $filiereId = $_POST['filiere'];
                    if (!$semestre && isset($_POST['semestre'])) $semestre = $_POST['semestre'];
                    if (!$session && isset($_POST['session'])) $session = $_POST['session'];
                    if ($grade === null && isset($_POST['note'])) $grade = $_POST['note'];

                    error_log("Extracted values: studentId=$studentId, moduleId=$moduleId, niveauId=$niveauId, filiereId=$filiereId, semestre=$semestre, session=$session, grade=$grade");

                    if (!$studentId || !$moduleId || !$session || $grade === null) {
                        $errorMsg = 'Missing required parameters';
                        error_log($errorMsg);
                        jsonResponse(['error' => $errorMsg, 'success' => false, 'received' => [
                            'student_id' => $studentId,
                            'module' => $moduleId,
                            'niveau' => $niveauId,
                            'filiere' => $filiereId,
                            'semestre' => $semestre,
                            'session' => $session,
                            'note' => $grade
                        ]], 400);
                        exit;
                    }

                    updateStudentGradeAPI($studentId, $moduleId, $niveauId, $filiereId, $semestre, $session, $grade);
                    break;

                case 'saveGrades':
                    // Save multiple grades at once
                    error_log("Processing saveGrades with data: " . file_get_contents('php://input'));

                    // Récupérer les données depuis le corps de la requête JSON
                    $jsonData = json_decode(file_get_contents('php://input'), true);
                    error_log("Decoded JSON data: " . print_r($jsonData, true));

                    // Récupérer les données depuis POST ou JSON
                    $grades = isset($jsonData['grades']) ? $jsonData['grades'] : null;

                    if (!$grades || !is_array($grades)) {
                        $errorMsg = 'No grades data provided or invalid format';
                        error_log($errorMsg);
                        jsonResponse(['error' => $errorMsg, 'success' => false], 400);
                        exit;
                    }

                    $successCount = 0;
                    $errorCount = 0;

                    foreach ($grades as $grade) {
                        $studentId = isset($grade['id_etudiant']) ? $grade['id_etudiant'] : null;
                        $moduleId = isset($grade['id_module']) ? $grade['id_module'] : null;
                        $niveauId = isset($grade['id_niveau']) ? $grade['id_niveau'] : null;
                        $filiereId = isset($grade['id_filiere']) ? $grade['id_filiere'] : null;
                        $semestre = isset($grade['semestre']) ? $grade['semestre'] : null;
                        $session = isset($grade['session']) ? $grade['session'] : null;
                        $value = isset($grade['valeur']) ? $grade['valeur'] : null;

                        if (!$studentId || !$moduleId || !$session || $value === null) {
                            error_log("Missing required parameters for grade: " . print_r($grade, true));
                            $errorCount++;
                            continue;
                        }

                        $result = updateStudentGrade($studentId, $moduleId, $niveauId, $filiereId, $semestre, $session, $value);

                        if (isset($result['error'])) {
                            error_log("Error updating grade: " . $result['error']);
                            $errorCount++;
                        } else {
                            $successCount++;
                        }
                    }

                    if ($errorCount > 0) {
                        jsonResponse([
                            'message' => "Saved $successCount grades successfully, $errorCount failed",
                            'success' => $successCount > 0,
                            'successCount' => $successCount,
                            'errorCount' => $errorCount
                        ], 200);
                    } else {
                        jsonResponse([
                            'message' => "All $successCount grades saved successfully",
                            'success' => true,
                            'successCount' => $successCount
                        ], 200);
                    }
                    break;

                case 'send_to_coordinator':
                    // Send grades PDF to coordinator
                    error_log("Processing send_to_coordinator with data: " . file_get_contents('php://input'));

                    // Récupérer les données depuis le corps de la requête JSON
                    $jsonData = json_decode(file_get_contents('php://input'), true);
                    error_log("Decoded JSON data for send_to_coordinator: " . print_r($jsonData, true));

                    // Appeler la fonction API pour envoyer les notes au coordinateur
                    sendGradesToCoordinatorAPI($jsonData);
                    break;

                default:
                    // Default to creating a new note
                    createNoteAPI($postData);
                    break;
            }
            break;

        case 'PUT':
            // Update a note
            if (isset($_GET['id'])) {
                $data = json_decode(file_get_contents('php://input'), true);
                updateNoteAPI($_GET['id'], $data);
            } else {
                jsonResponse(['error' => 'Note ID is required for update', 'success' => false], 400);
            }
            break;

        case 'DELETE':
            // Delete a note
            if (isset($_GET['id'])) {
                deleteNoteAPI($_GET['id']);
            } else {
                jsonResponse(['error' => 'Note ID is required for deletion', 'success' => false], 400);
            }
            break;

        default:
            jsonResponse(['error' => 'Method not allowed', 'success' => false], 405);
            break;
    }
} catch (Exception $e) {
    // Log the error
    error_log("Error in noteRoute.php: " . $e->getMessage());

    // Return a JSON response with the error
    jsonResponse(['error' => 'Server error: ' . $e->getMessage(), 'success' => false], 500);
}
?>