# Vacataire Account Creation Feature

## Overview
This feature allows coordinators to create user accounts specifically for vacataires (part-time lecturers) within their program. The system follows the same secure account creation methodology used in the admin interface.

## Files Created/Modified

### New Files
1. **view/coordinator/create-vacataire-account.php** - Main page with form interface
2. **controller/createVacataireAccountController.php** - API controller for handling requests
3. **route/createVacataireAccountRoute.php** - Route handler for API endpoints

### Modified Files
1. **view/includes/sidebar.php** - Added navigation link for coordinators
2. **model/specialiteModel.php** - Added getSpecialitesByFiliere() function

## Features

### Security & Authentication
- ✅ Protected by `auth_check_coordinateur.php`
- ✅ Only coordinators can access the page
- ✅ Role restriction: Only 'vacataire' accounts can be created
- ✅ Session-based authentication validation

### Form Validation
- ✅ Client-side validation with real-time feedback
- ✅ Server-side validation for all inputs
- ✅ CNI format validation (5-20 alphanumeric characters)
- ✅ Email format validation
- ✅ Name length validation (2-50 characters)
- ✅ Duplicate username checking

### Account Creation Process
1. **User Input**: Coordinator fills form with vacataire details
2. **Validation**: Both client and server-side validation
3. **Account Creation**: User record created in database with 'vacataire' role
4. **Token Generation**: Secure password reset token generated (24-hour expiry)
5. **Email Notification**: Automated email sent with password initialization link
6. **Feedback**: Success/error feedback provided to coordinator

### Email Features
- ✅ Professional HTML email template
- ✅ Coordinator and program information included
- ✅ Secure password reset link (24-hour expiry)
- ✅ Clear instructions for account activation
- ✅ Branded with ENSAH styling

## Usage Instructions

### For Coordinators
1. Navigate to **Curriculum Management > Create Vacataire Accounts**
2. Fill in the required information:
   - **CNI**: Will be used as username (5-20 alphanumeric characters)
   - **Email**: Where password reset instructions will be sent
   - **First Name**: Vacataire's first name
   - **Last Name**: Vacataire's last name
   - **Program (Filière)**: Select from all available programs
   - **Specialty (Spécialité)**: Select from specialties within the chosen program
3. Click **Create Account**
4. System will:
   - Validate all inputs
   - Create the user account
   - Create the enseignant record with selected filière and specialité
   - Send email to the vacataire
   - Display success/error message

### For Vacataires
1. Check email for account creation notification
2. Click the password initialization link
3. Set a secure password
4. Login using CNI as username

## Technical Details

### Database Schema
- Uses existing `users` table with 'vacataire' role
- Uses existing `password_reset_tokens` table for secure token storage
- No additional database changes required

### API Endpoints
- **POST** `/route/createVacataireAccountRoute.php`
  - Creates vacataire account
  - Generates password reset token
  - Sends email notification

### Error Handling
- Comprehensive validation with specific error messages
- Graceful handling of database errors
- Email sending failure handling with appropriate feedback
- Network error handling on client side

### Security Measures
- Password hashing using PHP's `password_hash()`
- SQL injection prevention with prepared statements
- Input sanitization and validation
- Secure token generation using `random_bytes()`
- Session-based authentication checks

## Configuration Requirements

### Email Configuration
Ensure the following are properly configured:
- `config/mail.php` - Email settings
- `config/email_provider.php` - Email provider configuration
- `utils/emailSender.php` - Email sending functionality

### Constants
- `BASE_URL` must be properly defined in `config/constants.php`

## Testing

### Manual Testing Steps
1. Login as a coordinator
2. Navigate to the create vacataire account page
3. Test form validation with invalid inputs
4. Create a test account with valid inputs
5. Verify email is sent (check logs if in simulation mode)
6. Test the password reset link functionality

### Error Scenarios to Test
- Duplicate CNI/username
- Invalid email format
- Network connectivity issues
- Database connection problems
- Email sending failures

## Maintenance

### Monitoring
- Check email logs for delivery issues
- Monitor database for failed account creations
- Review error logs for any system issues

### Updates
- Email templates can be modified in the controller
- Validation rules can be adjusted as needed
- UI styling can be updated in the view file

## Support

For issues or questions regarding this feature:
1. Check the error logs in `/logs/php_errors.log`
2. Verify email configuration settings
3. Ensure database connectivity
4. Review session and authentication status
