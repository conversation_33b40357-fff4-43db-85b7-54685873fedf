<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faculty Management - UniAdmin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/students.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4 student-management-container">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Faculty Management</h2>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFacultyModal">
                        <i class="bi bi-plus-lg me-1"></i> Add Faculty
                    </button>
                </div>

                <!-- Search and Filters -->
                <div class="card mb-4 search-filters">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="search-box">
                                    <i class="bi bi-search"></i>
                                    <input type="text" id="cniSearch" class="form-control" placeholder="Search by CNI...">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="roleFilter">
                                    <option value="">All Roles</option>
                                    <!-- Options par défaut au cas où le JavaScript ne fonctionnerait pas -->
                                    <option value="enseignant">Enseignant</option>
                                    <option value="chef de departement">Chef de Département</option>
                                    <option value="coordinateur">Coordinateur</option>
                                    <option value="vacataire">Vacataire</option>
                                    <option value="admin">Administrateur</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="departmentFilter">
                                    <option value="">All Departments</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Faculty Table -->
                <div class="card student-table">
                    <div class="card-body">
                        <!-- Table responsive pour les écrans larges -->
                        <div class="table-responsive">
                            <table class="table" id="facultyTable">
                                <thead>
                                    <tr>
                                        <th>CNI</th>
                                        <th>First Name</th>
                                        <th>Last Name</th>
                                        <th>Email</th>
                                        <th>Gender</th>
                                        <th>Role</th>
                                        <th>Department</th>
                                        <th>Specialty</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Table content will be dynamically populated -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Cartes pour mobile -->
                        <div class="student-cards d-none" id="facultyCards">
                            <!-- Faculty cards will be dynamically populated -->
                        </div>

                        <!-- Pagination will be added here if needed -->
                        <div id="pagination" class="d-flex justify-content-center mt-4">
                            <!-- Pagination controls will be dynamically populated -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Faculty Modal -->
    <div class="modal fade" id="addFacultyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Faculty Information</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addFacultyForm">
                        <input type="hidden" name="id_enseignant" value="">
                        <input type="hidden" name="originalCni" value="">

                        <!-- Personal Information Section -->
                        <div class="form-section mb-4">
                            <h6 class="form-section-title mb-3">Personal Information</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="CNI" class="form-label">CNI</label>
                                    <input type="text" class="form-control" id="CNI" name="CNI" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="sexe" class="form-label">Gender</label>
                                    <select class="form-select" id="sexe" name="sexe" required>
                                        <option value="masculin">Male</option>
                                        <option value="féminin">Female</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="nom" class="form-label">First Name</label>
                                    <input type="text" class="form-control" id="nom" name="nom" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="prenom" class="form-label">Last Name</label>
                                    <input type="text" class="form-control" id="prenom" name="prenom" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="date_naissance" class="form-label">Date of Birth</label>
                                    <input type="date" class="form-control" id="date_naissance" name="date_naissance" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="lieu_naissance" class="form-label">Place of Birth</label>
                                    <input type="text" class="form-control" id="lieu_naissance" name="lieu_naissance" required>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information Section -->
                        <div class="form-section mb-4">
                            <h6 class="form-section-title mb-3">Contact Information</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="tele" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="tele" name="tele" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="pays" class="form-label">Country</label>
                                    <input type="text" class="form-control" id="pays" name="pays" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="ville" class="form-label">City</label>
                                    <input type="text" class="form-control" id="ville" name="ville" required>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information Section -->
                        <div class="form-section mb-4">
                            <h6 class="form-section-title mb-3">Professional Information</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="role" class="form-label">Role</label>
                                    <select class="form-select" id="role" name="role" required>
                                        <!-- Options par défaut au cas où le JavaScript ne fonctionnerait pas -->
                                        <option value="enseignant">Enseignant</option>
                                        <option value="chef de departement">Chef de Département</option>
                                        <option value="coordinateur">Coordinateur</option>
                                        <option value="vacataire">Vacataire</option>
                                        <option value="admin">Administrateur</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="department" class="form-label">Department</label>
                                    <select class="form-select" id="department" name="department" required>
                                        <option value="">Select Department</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="specialite" class="form-label">Specialty</label>
                                    <select class="form-select" id="specialite" name="specialite">
                                        <option value="">Select Specialty</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="date_debut_travail" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="date_debut_travail" name="date_debut_travail" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="profile_picture" class="form-label">Profile Picture</label>
                                    <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                                </div>
                            </div>
                        </div>

                        <!-- Account Information Section -->
                        <div class="form-section mb-4">
                            <h6 class="form-section-title mb-3">Account Information</h6>
                            <div class="row g-3">
                                <div class="col-12">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="createAccount" name="createAccount">
                                        <label class="form-check-label" for="createAccount">Create user account</label>
                                    </div>
                                    <div class="form-text text-muted">
                                        <small>
                                            <i class="bi bi-info-circle me-1"></i>
                                            If checked, a user account will be created automatically with the CNI as username.
                                            An email will be sent to the faculty member with instructions to set their password.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal-footer px-0 pb-0">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Save</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">Success</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center py-4">
                    <i class="bi bi-check-circle-fill text-success" style="font-size: 3rem;"></i>
                    <p class="mt-3 mb-0" id="successMessage">Operation completed successfully!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">Confirmation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center py-4">
                    <i class="bi bi-exclamation-triangle-fill text-warning" style="font-size: 3rem;"></i>
                    <p class="mt-3 mb-0" id="confirmMessage">Are you sure you want to delete this faculty member?</p>
                    <input type="hidden" id="deleteFacultyId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/faculty.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script>
        // Toggle between table and cards view based on screen size
        function toggleTableCardsView() {
            const tableView = document.querySelector('.table-responsive');
            const cardsView = document.getElementById('facultyCards');

            if (window.innerWidth < 768) {
                tableView.classList.add('d-none');
                cardsView.classList.remove('d-none');
            } else {
                tableView.classList.remove('d-none');
                cardsView.classList.add('d-none');
            }
        }

        // Call on page load and window resize
        document.addEventListener('DOMContentLoaded', function() {
            toggleTableCardsView();
            window.addEventListener('resize', toggleTableCardsView);
        });
    </script>
</body>
</html>
