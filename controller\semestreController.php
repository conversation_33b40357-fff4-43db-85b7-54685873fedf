<?php
require_once "../model/semestreModel.php";
require_once "../utils/response.php";

/**
 * Get all semesters or filter by niveau ID
 */
function getAllSemestresAPI() {
    // Check if niveau_id is provided for filtering
    $niveauId = isset($_GET['niveau_id']) ? $_GET['niveau_id'] : null;

    $semestres = getAllSemestres($niveauId);

    if (isset($semestres['error'])) {
        jsonResponse(['error' => $semestres['error']], 404);
    }

    jsonResponse(['data' => $semestres], 200);
}

/**
 * Get semesters by filiere and level
 */
function getSemestresByFiliereAndLevelAPI() {
    // Check if filiere_id and level_id are provided
    if (!isset($_GET['filiere_id']) || !isset($_GET['level_id'])) {
        jsonResponse(['error' => 'Missing required parameters: filiere_id and level_id'], 400);
        return;
    }

    $filiereId = $_GET['filiere_id'];
    $levelId = $_GET['level_id'];

    $semestres = getSemestresByFiliereAndLevel($filiereId, $levelId);

    if (isset($semestres['error'])) {
        jsonResponse(['error' => $semestres['error']], 404);
    }

    jsonResponse(['data' => $semestres], 200);
}
?>