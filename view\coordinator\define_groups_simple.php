<?php
// Vérifier l'authentification
require_once '../includes/auth_check_coordinateur.php';

// Récupérer les informations du coordinateur depuis la session
$userName = $_SESSION['user']['username'] ?? 'Coordinateur';
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$filiereName = $_SESSION['user']['filiere_name'] ?? 'Non spécifié';
$filiereId = $_SESSION['user']['filiere_id'] ?? 1;

// Construire le nom complet
$fullName = $prenom . ' ' . $nom;
if (trim($fullName) === '') {
    $fullName = $userName;
}

// Inclure le modèle des visites et enregistrer la visite
require_once '../../model/visitsModel.php';
recordVisit('coordinateur', 'define_groups_simple');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Définir les Groupes TD/TP - Coordinateur</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <link rel="stylesheet" href="../assets/css/define_groups.css">
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="row mb-4">
                    <div class="col">
                        <h2 class="page-title">
                            <i class="fas fa-users me-2"></i>Définir les Groupes TD/TP
                        </h2>
                        <p class="text-muted">Définissez le nombre de groupes TD et TP pour chaque cycle de la filière <?php echo htmlspecialchars($filiereName); ?></p>
                    </div>
                </div>

                <!-- Cycles Container -->
                <div id="cycles-container" data-filiere-id="<?php echo $filiereId; ?>">
                    <!-- Cycles will be populated by JavaScript -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2">Chargement des cycles...</p>
                    </div>
                </div>

                <!-- Information Alert -->
                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Information:</strong> Les valeurs définies ici seront appliquées à tous les modules du cycle correspondant.
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/define_groups_simple.js"></script>
</body>
</html>
