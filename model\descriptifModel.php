<?php
require_once "../config/db.php";

/**
 * Get all teaching units with related information
 *
 * @return array Array of teaching units with module and teacher information
 */
function getAllUniteEnseignement() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllUniteEnseignement");
        return ["error" => "Database connection error"];
    }

    $sql = "SELECT ue.*,
            m.nom as nom_module, m.volume_total, m.id_semestre, s.nom as semestre, m.is_cours, m.is_td, m.is_tp,
            f.nom_filiere,
            n.nom as niveau
            FROM uniteenseignement ue
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN semestre s ON m.id_semestre = s.id
            ORDER BY m.nom, ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllUniteEnseignement: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching teaching units: " . $error];
    }

    $unites = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $unites[] = $row;
    }

    mysqli_close($conn);
    return $unites;
}

/**
 * Get teaching units by module ID
 *
 * @param int $moduleId Module ID
 * @return array Array of teaching units for the specified module
 */
function getUniteEnseignementByModule($moduleId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getUniteEnseignementByModule");
        return ["error" => "Database connection error"];
    }

    // Validate module ID
    if (!is_numeric($moduleId)) {
        error_log("Invalid module ID in getUniteEnseignementByModule: " . $moduleId);
        mysqli_close($conn);
        return ["error" => "Invalid module ID"];
    }

    $moduleId = mysqli_real_escape_string($conn, $moduleId);

    // First check if the module exists
    $checkSql = "SELECT id FROM module WHERE id = '$moduleId'";
    $checkResult = mysqli_query($conn, $checkSql);

    if (!$checkResult) {
        $error = mysqli_error($conn);
        error_log("Error checking module existence: " . $error);
        mysqli_close($conn);
        return ["error" => "Error checking module existence: " . $error];
    }

    if (mysqli_num_rows($checkResult) == 0) {
        error_log("Module not found with ID: " . $moduleId);
        mysqli_close($conn);
        return ["error" => "Module not found with ID: " . $moduleId];
    }

    // Now get the teaching units
    $sql = "SELECT ue.*,
            m.nom as nom_module, m.volume_total, m.id_semestre, s.nom as semestre, m.is_cours, m.is_td, m.is_tp,
            f.nom_filiere,
            n.nom as niveau
            FROM uniteenseignement ue
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN semestre s ON m.id_semestre = s.id
            WHERE ue.module_id = '$moduleId'
            ORDER BY ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getUniteEnseignementByModule: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching teaching units: " . $error];
    }

    $unites = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $unites[] = $row;
    }

    mysqli_close($conn);
    return $unites;
}

/**
 * Get teaching units by filiere ID
 *
 * @param int $filiereId Filiere ID
 * @return array Array of teaching units for the specified filiere
 */
function getUniteEnseignementByFiliere($filiereId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getUniteEnseignementByFiliere");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    $sql = "SELECT ue.*,
            m.nom as nom_module, m.volume_total, m.id_semestre, s.nom as semestre, m.is_cours, m.is_td, m.is_tp,
            f.nom_filiere,
            n.nom as niveau
            FROM uniteenseignement ue
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN semestre s ON m.id_semestre = s.id
            WHERE m.filiere_id = '$filiereId'
            ORDER BY m.nom, ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getUniteEnseignementByFiliere: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching teaching units: " . $error];
    }

    $unites = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $unites[] = $row;
    }

    mysqli_close($conn);
    return $unites;
}

/**
 * Get a teaching unit by ID
 *
 * @param int $id Teaching unit ID
 * @return array|null Teaching unit data or null if not found
 */
function getUniteEnseignementById($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getUniteEnseignementById");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);

    $sql = "SELECT ue.*,
            m.nom as nom_module, m.volume_total, m.id_semestre, s.nom as semestre, m.is_cours, m.is_td, m.is_tp,
            f.nom_filiere,
            n.nom as niveau
            FROM uniteenseignement ue
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN semestre s ON m.id_semestre = s.id
            WHERE ue.id = '$id'";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getUniteEnseignementById: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching teaching unit: " . $error];
    }

    $unite = mysqli_fetch_assoc($result);
    mysqli_close($conn);

    return $unite;
}

/**
 * Create a new teaching unit
 *
 * @param array $data Teaching unit data
 * @return bool|array True on success, error array on failure
 */
function createUniteEnseignement($data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in createUniteEnseignement");
        return ["error" => "Database connection error"];
    }

    // Prepare the query
    $query = "INSERT INTO uniteenseignement (type, volume_horaire, nb_groupes, module_id)
              VALUES (?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        $error = mysqli_error($conn);
        error_log("Error preparing statement in createUniteEnseignement: " . $error);
        mysqli_close($conn);
        return ["error" => "Error preparing statement: " . $error];
    }

    // Bind parameters
    mysqli_stmt_bind_param(
        $stmt,
        "siii",
        $data['type'],
        $data['volume_horaire'],
        $data['nb_groupes'],
        $data['module_id']
    );

    // Execute the statement
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        $error = mysqli_stmt_error($stmt);
        error_log("Error executing statement in createUniteEnseignement: " . $error);
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        return ["error" => "Error creating teaching unit: " . $error];
    }

    $id = mysqli_insert_id($conn);

    // Update the module flags based on the unit type
    $moduleId = $data['module_id'];
    $unitType = $data['type'];

    $typeColumn = "";
    if ($unitType == "Cours") {
        $typeColumn = "is_cours";
    } else if ($unitType == "TD") {
        $typeColumn = "is_td";
    } else if ($unitType == "TP") {
        $typeColumn = "is_tp";
    }

    if ($typeColumn) {
        // Set the corresponding flag to 1 in the module table
        $query = "UPDATE module SET $typeColumn = 1 WHERE id = '$moduleId'";
        $updateResult = mysqli_query($conn, $query);

        if (!$updateResult) {
            $error = mysqli_error($conn);
            error_log("Error updating module flag in createUniteEnseignement: " . $error);
            // We don't return an error here because the unit was successfully created
        }
    }

    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    return ["success" => true, "id" => $id];
}

/**
 * Update a teaching unit
 *
 * @param int $id Teaching unit ID
 * @param array $data Teaching unit data
 * @return bool|array True on success, error array on failure
 */
function updateUniteEnseignement($id, $data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateUniteEnseignement");
        return ["error" => "Database connection error"];
    }

    // Prepare the query
    $query = "UPDATE uniteenseignement
              SET type = ?, volume_horaire = ?, nb_groupes = ?, module_id = ?
              WHERE id = ?";

    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        $error = mysqli_error($conn);
        error_log("Error preparing statement in updateUniteEnseignement: " . $error);
        mysqli_close($conn);
        return ["error" => "Error preparing statement: " . $error];
    }

    // Bind parameters
    mysqli_stmt_bind_param(
        $stmt,
        "siiii",
        $data['type'],
        $data['volume_horaire'],
        $data['nb_groupes'],
        $data['module_id'],
        $id
    );

    // Execute the statement
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        $error = mysqli_stmt_error($stmt);
        error_log("Error executing statement in updateUniteEnseignement: " . $error);
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        return ["error" => "Error updating teaching unit: " . $error];
    }

    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    return ["success" => true];
}

/**
 * Delete a teaching unit
 *
 * @param int $id Teaching unit ID
 * @return bool|array True on success, error array on failure
 */
function deleteUniteEnseignement($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in deleteUniteEnseignement");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);

    // First, get the unit details to know its type and module_id
    $query = "SELECT * FROM uniteenseignement WHERE id = '$id'";
    $result = mysqli_query($conn, $query);

    if (!$result || mysqli_num_rows($result) == 0) {
        $error = mysqli_error($conn);
        error_log("Error getting unit details in deleteUniteEnseignement: " . $error);
        mysqli_close($conn);
        return ["error" => "Error getting unit details: " . $error];
    }

    $unit = mysqli_fetch_assoc($result);
    $moduleId = $unit['module_id'];
    $unitType = $unit['type'];

    // Delete the unit
    $query = "DELETE FROM uniteenseignement WHERE id = '$id'";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in deleteUniteEnseignement: " . $error);
        mysqli_close($conn);
        return ["error" => "Error deleting teaching unit: " . $error];
    }

    // Check if there are any remaining units of this type for this module
    $typeColumn = "";
    if ($unitType == "Cours") {
        $typeColumn = "is_cours";
    } else if ($unitType == "TD") {
        $typeColumn = "is_td";
    } else if ($unitType == "TP") {
        $typeColumn = "is_tp";
    }

    if ($typeColumn) {
        $query = "SELECT COUNT(*) as count FROM uniteenseignement WHERE module_id = '$moduleId' AND type = '$unitType'";
        $result = mysqli_query($conn, $query);

        if ($result && $row = mysqli_fetch_assoc($result)) {
            $count = $row['count'];

            // If no units of this type remain, update the module flag
            if ($count == 0) {
                $query = "UPDATE module SET $typeColumn = 0 WHERE id = '$moduleId'";
                $result = mysqli_query($conn, $query);

                if (!$result) {
                    $error = mysqli_error($conn);
                    error_log("Error updating module flag in deleteUniteEnseignement: " . $error);
                    // We don't return an error here because the unit was successfully deleted
                }
            }
        }
    }

    mysqli_close($conn);
    return ["success" => true];
}

/**
 * Get all modules
 *
 * @param int|null $filiereId Optional filiere ID to filter by
 * @return array Array of modules
 */
function getAllModules($filiereId = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("[ERROR] Database connection error in getAllModules");
        return ["error" => "Database connection error"];
    }

    // For coordinators, use the filiere_id from the session if not explicitly provided
    if ($filiereId === null && isset($_SESSION['user']) && $_SESSION['user']['role'] === 'coordinateur' && isset($_SESSION['user']['filiere_id'])) {
        $filiereId = $_SESSION['user']['filiere_id'];
        error_log("[DEBUG] Using coordinator's filiere_id from session in getAllModules: " . $filiereId);
    } else {
        error_log("[DEBUG] getAllModules called with filiereId: " . ($filiereId ?? 'null') .
                 ", user role: " . (isset($_SESSION['user']) ? $_SESSION['user']['role'] : 'not set') .
                 ", filiere_id in session: " . (isset($_SESSION['user']['filiere_id']) ? $_SESSION['user']['filiere_id'] : 'not set'));
    }

    $sql = "SELECT m.*, f.nom_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre
            FROM module m
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id";

    // Add filiere filter if provided
    if ($filiereId !== null) {
        $filiereId = mysqli_real_escape_string($conn, $filiereId);
        $sql .= " WHERE m.filiere_id = '$filiereId'";
        error_log("[DEBUG] Filtering modules by filiere_id: " . $filiereId);
    }

    $sql .= " ORDER BY m.nom";
    error_log("[DEBUG] Executing query in getAllModules: " . $sql);

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllModules: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching modules: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $modules[] = $row;
    }

    mysqli_close($conn);
    return $modules;
}

/**
 * Get a module by ID
 *
 * @param int $id Module ID
 * @return array|null Module data or null if not found
 */
function getModuleById($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getModuleById");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);

    $sql = "SELECT m.*, f.nom_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre
            FROM module m
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id
            WHERE m.id = '$id'";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getModuleById: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching module: " . $error];
    }

    $module = mysqli_fetch_assoc($result);
    mysqli_close($conn);

    return $module;
}

/**
 * Create a new module
 *
 * @param array $data Module data
 * @return bool|array True on success, error array on failure
 */
function createModule($data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in createModule");
        return ["error" => "Database connection error"];
    }

    // Prepare the query
    $query = "INSERT INTO module (nom, volume_total, specialite_id, filiere_id, id_niveau, id_semestre, is_cours, is_td, is_tp)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        $error = mysqli_error($conn);
        error_log("Error preparing statement in createModule: " . $error);
        mysqli_close($conn);
        return ["error" => "Error preparing statement: " . $error];
    }

    // Set default values for boolean fields
    $is_cours = (isset($data['is_cours']) && $data['is_cours'] == 1) ? 1 : 0;
    $is_td = (isset($data['is_td']) && $data['is_td'] == 1) ? 1 : 0;
    $is_tp = (isset($data['is_tp']) && $data['is_tp'] == 1) ? 1 : 0;

    // Bind parameters
    mysqli_stmt_bind_param(
        $stmt,
        "siiisisii",
        $data['nom'],
        $data['volume_total'],
        $data['specialite_id'],
        $data['filiere_id'],
        $data['id_niveau'],
        $data['id_semestre'], // Changed from semestre to id_semestre
        $is_cours,
        $is_td,
        $is_tp
    );

    // Execute the statement
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        $error = mysqli_stmt_error($stmt);
        error_log("Error executing statement in createModule: " . $error);
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        return ["error" => "Error creating module: " . $error];
    }

    $id = mysqli_insert_id($conn);
    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    return ["success" => true, "id" => $id];
}

/**
 * Update a module
 *
 * @param int $id Module ID
 * @param array $data Module data
 * @return bool|array True on success, error array on failure
 */
function updateModule($id, $data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateModule");
        return ["error" => "Database connection error"];
    }

    // Prepare the query
    $query = "UPDATE module
              SET nom = ?, volume_total = ?, specialite_id = ?, filiere_id = ?,
                  id_niveau = ?, id_semestre = ?, is_cours = ?, is_td = ?, is_tp = ?
              WHERE id = ?";

    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        $error = mysqli_error($conn);
        error_log("Error preparing statement in updateModule: " . $error);
        mysqli_close($conn);
        return ["error" => "Error preparing statement: " . $error];
    }

    // Set default values for boolean fields
    $is_cours = (isset($data['is_cours']) && $data['is_cours'] == 1) ? 1 : 0;
    $is_td = (isset($data['is_td']) && $data['is_td'] == 1) ? 1 : 0;
    $is_tp = (isset($data['is_tp']) && $data['is_tp'] == 1) ? 1 : 0;

    // Bind parameters
    mysqli_stmt_bind_param(
        $stmt,
        "siiisisiii",
        $data['nom'],
        $data['volume_total'],
        $data['specialite_id'],
        $data['filiere_id'],
        $data['id_niveau'],
        $data['id_semestre'], // Changed from semestre to id_semestre
        $is_cours,
        $is_td,
        $is_tp,
        $id
    );

    // Execute the statement
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        $error = mysqli_stmt_error($stmt);
        error_log("Error executing statement in updateModule: " . $error);
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        return ["error" => "Error updating module: " . $error];
    }

    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    return ["success" => true];
}

/**
 * Delete a module
 *
 * @param int $id Module ID
 * @return bool|array True on success, error array on failure
 */
function deleteModule($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in deleteModule");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);

    // First delete all teaching units associated with this module
    $query = "DELETE FROM uniteenseignement WHERE module_id = '$id'";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error deleting teaching units in deleteModule: " . $error);
        mysqli_close($conn);
        return ["error" => "Error deleting teaching units: " . $error];
    }

    // Then delete the module
    $query = "DELETE FROM module WHERE id = '$id'";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in deleteModule: " . $error);
        mysqli_close($conn);
        return ["error" => "Error deleting module: " . $error];
    }

    mysqli_close($conn);
    return ["success" => true];
}

/**
 * Get all filieres
 *
 * @return array Array of filieres
 */
function getAllFilieres() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllFilieres");
        return ["error" => "Database connection error"];
    }

    $sql = "SELECT * FROM filiere ORDER BY nom_filiere";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllFilieres: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching filieres: " . $error];
    }

    $filieres = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $filieres[] = $row;
    }

    mysqli_close($conn);
    return $filieres;
}

/**
 * Get all specialties
 *
 * @return array Array of specialties
 */
function getAllSpecialites() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllSpecialites");
        return ["error" => "Database connection error"];
    }

    // Debug: Log the query and connection status
    error_log("Connection established in getAllSpecialites");

    // First, check if the table exists
    $checkTable = mysqli_query($conn, "SHOW TABLES LIKE 'specialite'");
    if (!$checkTable || mysqli_num_rows($checkTable) == 0) {
        error_log("Table 'specialite' does not exist");
        mysqli_close($conn);
        return ["error" => "Table 'specialite' does not exist"];
    }

    // Get the table structure
    $describeTable = mysqli_query($conn, "DESCRIBE specialite");
    if (!$describeTable) {
        $error = mysqli_error($conn);
        error_log("Error describing specialite table: " . $error);
        mysqli_close($conn);
        return ["error" => "Error describing specialite table: " . $error];
    }

    // Log the table structure
    $tableColumns = [];
    while ($column = mysqli_fetch_assoc($describeTable)) {
        $tableColumns[] = $column['Field'];
    }
    error_log("Specialite table columns: " . implode(", ", $tableColumns));

    $sql = "SELECT * FROM specialite";
    error_log("Executing query: " . $sql);

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllSpecialites: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching specialites: " . $error];
    }

    $specialites = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $specialites[] = $row;
    }

    error_log("Found " . count($specialites) . " specialites");
    mysqli_close($conn);
    return $specialites;
}

/**
 * Get all semesters
 *
 * @param int|null $niveauId Optional niveau ID to filter semesters
 * @return array Array of semesters
 */
function getAllSemestres($niveauId = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllSemestres");
        return ["error" => "Database connection error"];
    }

    // Base query
    $sql = "SELECT * FROM semestre";

    // Add filter by niveau if provided
    if ($niveauId !== null) {
        $niveauId = mysqli_real_escape_string($conn, $niveauId);
        $sql .= " WHERE niveau_id = '$niveauId'";
    }

    $sql .= " ORDER BY id";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllSemestres: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching semesters: " . $error];
    }

    $semestres = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $semestres[] = $row;
    }

    mysqli_close($conn);
    return $semestres;
}

/**
 * Get all niveaux (levels)
 *
 * @param int|null $filiereId Optional filiere ID to filter niveaux
 * @return array Array of niveaux
 */
function getAllNiveaux($filiereId = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllNiveaux");
        return ["error" => "Database connection error"];
    }

    // For coordinators, use the filiere_id from the session if not explicitly provided
    if ($filiereId === null && isset($_SESSION['user']) && $_SESSION['user']['role'] === 'coordinateur' && isset($_SESSION['user']['filiere_id'])) {
        $filiereId = $_SESSION['user']['filiere_id'];
        error_log("[DEBUG] Using coordinator's filiere_id from session in getAllNiveaux: " . $filiereId);
    } else {
        error_log("[DEBUG] getAllNiveaux called with filiereId: " . ($filiereId ?? 'null') .
                 ", user role: " . (isset($_SESSION['user']) ? $_SESSION['user']['role'] : 'not set') .
                 ", filiere_id in session: " . (isset($_SESSION['user']['filiere_id']) ? $_SESSION['user']['filiere_id'] : 'not set'));
    }

    // If filiere ID is provided (or taken from session for coordinators), filter by cycle
    if ($filiereId !== null) {
        $filiereId = mysqli_real_escape_string($conn, $filiereId);

        // First, get the cycle_id from the filiere
        $cycleQuery = "SELECT id_cycle FROM filiere WHERE id_filiere = '$filiereId'";
        error_log("[DEBUG] Executing query to get cycle_id: " . $cycleQuery);
        $cycleResult = mysqli_query($conn, $cycleQuery);

        if (!$cycleResult) {
            $error = mysqli_error($conn);
            error_log("[ERROR] Error getting cycle_id for filiere: " . $error);
            mysqli_close($conn);
            return ["error" => "Error getting cycle_id for filiere: " . $error];
        }

        if (mysqli_num_rows($cycleResult) === 0) {
            error_log("[ERROR] No filiere found with ID: " . $filiereId);
            mysqli_close($conn);
            return ["error" => "No filiere found with ID: " . $filiereId];
        }

        $filiere = mysqli_fetch_assoc($cycleResult);
        $cycleId = $filiere['id_cycle'];
        error_log("[DEBUG] Found cycle_id: " . $cycleId . " for filiere_id: " . $filiereId);

        // Now get all niveaux for this cycle
        $sql = "SELECT * FROM niveaux WHERE cycle_id = '$cycleId' ORDER BY id";
        error_log("[DEBUG] Executing query to get niveaux: " . $sql);

        error_log("[DEBUG] Filtering niveaux by cycle_id: " . $cycleId . " for filiere_id: " . $filiereId);
    } else {
        $sql = "SELECT * FROM niveaux ORDER BY id";
    }

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllNiveaux: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching niveaux: " . $error];
    }

    $niveaux = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $niveaux[] = $row;
    }

    error_log("Found " . count($niveaux) . " niveaux" . ($filiereId ? " for filiere_id: " . $filiereId : ""));
    mysqli_close($conn);
    return $niveaux;
}

/**
 * Get all teachers
 *
 * @return array Array of teachers
 */
function getAllEnseignants() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllEnseignants");
        return ["error" => "Database connection error"];
    }

    $sql = "SELECT * FROM enseignant ORDER BY nom, prenom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllEnseignants: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching teachers: " . $error];
    }

    $enseignants = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $enseignants[] = $row;
    }

    mysqli_close($conn);
    return $enseignants;
}

/**
 * Import teaching units from CSV file
 *
 * @param string $filePath Path to the CSV file
 * @return array Result of the import operation
 */
function importUniteEnseignementFromCSV($filePath) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in importUniteEnseignementFromCSV");
        return ["error" => "Database connection error"];
    }

    if (!file_exists($filePath)) {
        return ["error" => "File not found"];
    }

    $file = fopen($filePath, "r");
    if (!$file) {
        return ["error" => "Could not open file"];
    }

    $headers = fgetcsv($file, 0, ",");

    $expectedHeaders = ["type", "volume_horaire", "nb_groupes", "module_id"];
    $missingHeaders = array_diff($expectedHeaders, $headers);

    if (!empty($missingHeaders)) {
        fclose($file);
        return ["error" => "Missing headers: " . implode(", ", $missingHeaders)];
    }

    $imported = 0;
    $errors = [];

    while (($data = fgetcsv($file, 0, ",")) !== FALSE) {
        $rowData = array_combine($headers, $data);

        $result = createUniteEnseignement($rowData);

        if (isset($result["success"])) {
            $imported++;
        } else {
            $errors[] = "Row " . ($imported + 1) . ": " . $result["error"];
        }
    }

    fclose($file);

    return [
        "success" => true,
        "imported" => $imported,
        "errors" => $errors
    ];
}

/**
 * Import modules from a file (CSV or Excel)
 *
 * @param string $filePath Path to the file
 * @param string $fileType File type (csv, xlsx, xls)
 * @param bool $hasHeader Whether the file has a header row
 * @param int|null $coordinatorFiliereId Optional filiere ID for coordinators to restrict imports
 * @return array Result of the import operation
 */
function importModulesFromFile($filePath, $fileType, $hasHeader, $coordinatorFiliereId = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in importModulesFromFile");
        return ["error" => "Database connection error"];
    }

    // Initialize variables
    $count = 0;
    $errors = [];

    // Process the file based on its type
    if ($fileType === 'csv') {
        // Process CSV file
        $file = fopen($filePath, 'r');
        if (!$file) {
            return ["error" => "Could not open the file"];
        }

        // Skip header row if needed
        if ($hasHeader) {
            fgetcsv($file);
        }

        // Read data from CSV
        while (($row = fgetcsv($file)) !== FALSE) {
            if (count($row) < 7) {
                $errors[] = "Row " . ($count + 1) . ": Not enough columns";
                continue;
            }

            // Convert semestre name to id if needed
            $semestreValue = $row[5];
            // If the value is a string like "S1", "S2", etc., convert it to the corresponding ID
            if (is_string($semestreValue) && preg_match('/^S(\d+)$/i', $semestreValue, $matches)) {
                $semestreId = $matches[1]; // Extract the number
            } else {
                $semestreId = $semestreValue; // Use as is if it's already a number
            }

            $moduleData = [
                'nom' => $row[0],
                'volume_total' => $row[1],
                'specialite_id' => $row[2],
                'filiere_id' => $row[3],
                'id_niveau' => $row[4],
                'id_semestre' => $semestreId,
                'is_cours' => isset($row[6]) && $row[6] == 1 ? 1 : 0,
                'is_td' => isset($row[7]) && $row[7] == 1 ? 1 : 0,
                'is_tp' => isset($row[8]) && $row[8] == 1 ? 1 : 0
            ];

            // For coordinators, enforce their department restriction
            if ($coordinatorFiliereId !== null) {
                // If the module's filiere_id doesn't match the coordinator's filiere_id, skip it
                if ($moduleData['filiere_id'] != $coordinatorFiliereId) {
                    $errors[] = "Row " . ($count + 1) . ": Module belongs to department ID " . $moduleData['filiere_id'] .
                               " but you can only import modules for your assigned department (ID: " . $coordinatorFiliereId . ")";
                    continue;
                }

                // Force the filiere_id to be the coordinator's filiere_id
                $moduleData['filiere_id'] = $coordinatorFiliereId;
                error_log("[DEBUG] Enforcing coordinator's filiere_id " . $coordinatorFiliereId . " for imported module");
            }

            $result = createModule($moduleData);

            if (isset($result["success"])) {
                $count++;

                // Create units if specified
                $moduleId = $result["id"];

                if ($moduleData['is_cours'] && isset($row[9]) && !empty($row[9])) {
                    $unitData = [
                        'module_id' => $moduleId,
                        'type' => 'Cours',
                        'volume_horaire' => $row[9],
                        'nb_groupes' => isset($row[10]) && !empty($row[10]) ? $row[10] : 1
                    ];
                    createUniteEnseignement($unitData);
                }

                if ($moduleData['is_td'] && isset($row[11]) && !empty($row[11])) {
                    $unitData = [
                        'module_id' => $moduleId,
                        'type' => 'TD',
                        'volume_horaire' => $row[11],
                        'nb_groupes' => isset($row[12]) && !empty($row[12]) ? $row[12] : 1
                    ];
                    createUniteEnseignement($unitData);
                }

                if ($moduleData['is_tp'] && isset($row[13]) && !empty($row[13])) {
                    $unitData = [
                        'module_id' => $moduleId,
                        'type' => 'TP',
                        'volume_horaire' => $row[13],
                        'nb_groupes' => isset($row[14]) && !empty($row[14]) ? $row[14] : 1
                    ];
                    createUniteEnseignement($unitData);
                }
            } else {
                $errors[] = "Row " . ($count + 1) . ": " . $result["error"];
            }
        }

        fclose($file);
    } else {
        // For Excel files, we would need a library like PhpSpreadsheet
        // For simplicity, we'll return an error message
        return ["error" => "Excel file support requires additional libraries. Please use CSV format."];
    }

    mysqli_close($conn);

    return [
        "success" => true,
        "count" => $count,
        "errors" => $errors
    ];
}