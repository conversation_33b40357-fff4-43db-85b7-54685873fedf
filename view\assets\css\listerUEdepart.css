/* listerUEdepart.css - Styles for the Department Modules page */

/* Main container styles */
.dashboard-container {
    min-height: 100vh;
    background-color: #f8f9fa;
}

/* Filter styles */
.filter-container {
    background: linear-gradient(to right, #f1f5fd, #f8f9fa);
    padding: 12px 15px;
    border-radius: 12px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(78, 115, 223, 0.1);
    position: relative;
    overflow: hidden;
}

.filter-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #4e73df, #a3bffa);
}

.filter-container label {
    color: #4e73df;
    font-weight: 600;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.filter-container label i {
    margin-right: 6px;
    font-size: 1rem;
}

.filter-container .form-select {
    border: 1px solid #d1d9e6;
    border-radius: 8px;
    box-shadow: none;
    transition: all 0.2s ease;
    padding: 8px 12px;
    background-color: white;
    font-size: 0.95rem;
    cursor: pointer;
}

.filter-container .form-select:focus {
    border-color: #a3bffa;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.15);
}

.filter-container .form-select:hover {
    border-color: #a3bffa;
}

/* Custom dropdown arrow */
.filter-container .form-select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%234e73df' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
}

/* Dashboard title styles */
.dashboard-title {
    color: #343a40;
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    display: inline-block;
    letter-spacing: -0.5px;
}

.dashboard-title:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -8px;
    width: 50px;
    height: 4px;
    background-color: #4e73df;
}

/* Breadcrumb styles */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-top: 0.75rem;
    margin-bottom: 1.5rem;
}

.breadcrumb-item a {
    color: #4e73df;
    text-decoration: none;
    transition: all 0.2s ease;
}

.breadcrumb-item a:hover {
    color: #224abe;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* Department card styles */
.department-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 25px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    position: relative;
}

.department-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, #4e73df, #36b9cc);
}

.department-header {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.department-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    box-shadow: 0 4px 8px rgba(78, 115, 223, 0.25);
    flex-shrink: 0;
}

.department-icon i {
    font-size: 20px;
    color: white;
}

.department-title {
    flex: 1;
}

.department-title h5 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 5px;
}

.department-name {
    color: #4e73df;
    font-weight: 700;
}

.department-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0;
}

.department-filter {
    padding: 15px 20px;
    background-color: rgba(78, 115, 223, 0.03);
}

/* Card styles */
.filiere-card {
    margin-bottom: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.filiere-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
}

/* Pastel color variations for filiere cards */
.filiere-card:nth-child(5n+1) .filiere-header {
    background-color: #e7f5ff; /* Light blue */
}

.filiere-card:nth-child(5n+2) .filiere-header {
    background-color: #f3f0ff; /* Light purple */
}

.filiere-card:nth-child(5n+3) .filiere-header {
    background-color: #ebfbee; /* Light green */
}

.filiere-card:nth-child(5n+4) .filiere-header {
    background-color: #fff3bf; /* Light yellow */
}

.filiere-card:nth-child(5n+5) .filiere-header {
    background-color: #ffe8eb; /* Light pink */
}

.filiere-header {
    padding: 18px 20px;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
    transition: all 0.2s ease;
}

.filiere-card:nth-child(5n+1) .filiere-header h5,
.filiere-card:nth-child(5n+1) .filiere-icon i {
    color: #1c7ed6; /* Blue */
}

.filiere-card:nth-child(5n+2) .filiere-header h5,
.filiere-card:nth-child(5n+2) .filiere-icon i {
    color: #7950f2; /* Purple */
}

.filiere-card:nth-child(5n+3) .filiere-header h5,
.filiere-card:nth-child(5n+3) .filiere-icon i {
    color: #40c057; /* Green */
}

.filiere-card:nth-child(5n+4) .filiere-header h5,
.filiere-card:nth-child(5n+4) .filiere-icon i {
    color: #e67700; /* Yellow/Orange */
}

.filiere-card:nth-child(5n+5) .filiere-header h5,
.filiere-card:nth-child(5n+5) .filiere-icon i {
    color: #e64980; /* Pink */
}

.filiere-header h5 {
    font-weight: 600;
    display: flex;
    flex-direction: column;
}

.filiere-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.filiere-icon i {
    font-size: 1.2rem;
}

.filiere-modules-count {
    font-size: 0.75rem;
    font-weight: 400;
    opacity: 0.7;
    margin-top: 4px;
}

/* Module list styles */
.module-list {
    padding: 0;
}

.module-item {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.module-item:hover {
    background-color: #f8f9fa;
}

.module-item:last-child {
    border-bottom: none;
    border-radius: 0 0 12px 12px;
}

/* Module info styles */
.module-info {
    flex: 1;
}

.module-title {
    font-size: 1rem;
    display: flex;
    align-items: center;
}

.module-title i {
    color: #4e73df;
    font-size: 0.9rem;
    opacity: 0.9;
}

.module-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

/* Module badges */
.module-badge {
    font-size: 0.75rem;
    padding: 5px 12px;
    border-radius: 20px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.module-badge i {
    font-size: 0.7rem;
    margin-right: 4px;
}

.badge-niveau {
    background-color: #e7f5ff;
    color: #1c7ed6;
    border: 1px solid rgba(28, 126, 214, 0.1);
}

.badge-niveau:hover {
    background-color: #d0ebff;
    transform: translateY(-1px);
}

.badge-semestre {
    background-color: #f3f0ff;
    color: #7950f2;
    border: 1px solid rgba(121, 80, 242, 0.1);
}

.badge-semestre:hover {
    background-color: #e5dbff;
    transform: translateY(-1px);
}

.badge-specialite {
    background-color: #fff3bf;
    color: #e67700;
    border: 1px solid rgba(230, 119, 0, 0.1);
}

.badge-specialite:hover {
    background-color: #ffec99;
    transform: translateY(-1px);
}

/* Button styles - with pastel colors matching filiere cards */
.filiere-card:nth-child(5n+1) .btn-view {
    background-color: #e7f5ff;
    color: #1c7ed6;
}

.filiere-card:nth-child(5n+1) .btn-view:hover {
    background-color: #d0ebff;
    color: #1971c2;
}

.filiere-card:nth-child(5n+2) .btn-view {
    background-color: #f3f0ff;
    color: #7950f2;
}

.filiere-card:nth-child(5n+2) .btn-view:hover {
    background-color: #e5dbff;
    color: #6741d9;
}

.filiere-card:nth-child(5n+3) .btn-view {
    background-color: #ebfbee;
    color: #40c057;
}

.filiere-card:nth-child(5n+3) .btn-view:hover {
    background-color: #d3f9d8;
    color: #37b24d;
}

.filiere-card:nth-child(5n+4) .btn-view {
    background-color: #fff3bf;
    color: #e67700;
}

.filiere-card:nth-child(5n+4) .btn-view:hover {
    background-color: #ffec99;
    color: #cc6a00;
}

.filiere-card:nth-child(5n+5) .btn-view {
    background-color: #ffe8eb;
    color: #e64980;
}

.filiere-card:nth-child(5n+5) .btn-view:hover {
    background-color: #ffd0d9;
    color: #d6336c;
}

.btn-view {
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 0.85rem;
}

.btn-view:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
}

.btn-view:active {
    transform: translateY(0);
}

.btn-view i {
    margin-right: 5px;
}

/* Module actions container */
.module-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Pagination styles */
.pagination-container {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
}

.pagination-controls .btn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    border: none;
}

/* Pastel colors for pagination buttons based on filière card colors */
.filiere-card:nth-child(5n+1) .pagination-controls .btn {
    background-color: #e7f5ff;
    color: #1c7ed6;
}

.filiere-card:nth-child(5n+1) .pagination-controls .btn:hover:not(:disabled) {
    background-color: #d0ebff;
    color: #1971c2;
}

.filiere-card:nth-child(5n+2) .pagination-controls .btn {
    background-color: #f3f0ff;
    color: #7950f2;
}

.filiere-card:nth-child(5n+2) .pagination-controls .btn:hover:not(:disabled) {
    background-color: #e5dbff;
    color: #6741d9;
}

.filiere-card:nth-child(5n+3) .pagination-controls .btn {
    background-color: #ebfbee;
    color: #40c057;
}

.filiere-card:nth-child(5n+3) .pagination-controls .btn:hover:not(:disabled) {
    background-color: #d3f9d8;
    color: #37b24d;
}

.filiere-card:nth-child(5n+4) .pagination-controls .btn {
    background-color: #fff3bf;
    color: #e67700;
}

.filiere-card:nth-child(5n+4) .pagination-controls .btn:hover:not(:disabled) {
    background-color: #ffec99;
    color: #cc6a00;
}

.filiere-card:nth-child(5n+5) .pagination-controls .btn {
    background-color: #ffe8eb;
    color: #e64980;
}

.filiere-card:nth-child(5n+5) .pagination-controls .btn:hover:not(:disabled) {
    background-color: #ffd0d9;
    color: #d6336c;
}

.pagination-controls .btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
}

.pagination-controls .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    color: #6c757d;
}

/* Bootstrap pagination customization */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #495057;
    border-color: #dee2e6;
    transition: all 0.2s ease;
}

.page-link:hover {
    background-color: #e9ecef;
    color: #212529;
    z-index: 2;
    transform: translateY(-1px);
}

.page-item.active .page-link {
    background-color: #1c7ed6;
    border-color: #1c7ed6;
}

.page-item.disabled .page-link {
    color: #adb5bd;
}

/* Pastel colors for pagination based on filière card */
.filiere-card:nth-child(5n+1) .page-item.active .page-link {
    background-color: #1c7ed6;
    border-color: #1c7ed6;
}

.filiere-card:nth-child(5n+2) .page-item.active .page-link {
    background-color: #7950f2;
    border-color: #7950f2;
}

.filiere-card:nth-child(5n+3) .page-item.active .page-link {
    background-color: #40c057;
    border-color: #40c057;
}

.filiere-card:nth-child(5n+4) .page-item.active .page-link {
    background-color: #e67700;
    border-color: #e67700;
}

.filiere-card:nth-child(5n+5) .page-item.active .page-link {
    background-color: #e64980;
    border-color: #e64980;
}

/* Search styles */
.search-container {
    background-color: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}

.module-search {
    border-radius: 20px;
    transition: all 0.3s ease;
}

.module-search:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.15);
}

.input-group-text {
    border-radius: 20px 0 0 20px;
}

.module-search {
    border-radius: 0 20px 20px 0;
}

/* Pastel colors for search input */
.filiere-card:nth-child(5n+1) .module-search:focus {
    border-color: #1c7ed6;
    box-shadow: 0 0 0 0.2rem rgba(28, 126, 214, 0.15);
}

.filiere-card:nth-child(5n+2) .module-search:focus {
    border-color: #7950f2;
    box-shadow: 0 0 0 0.2rem rgba(121, 80, 242, 0.15);
}

.filiere-card:nth-child(5n+3) .module-search:focus {
    border-color: #40c057;
    box-shadow: 0 0 0 0.2rem rgba(64, 192, 87, 0.15);
}

.filiere-card:nth-child(5n+4) .module-search:focus {
    border-color: #e67700;
    box-shadow: 0 0 0 0.2rem rgba(230, 119, 0, 0.15);
}

.filiere-card:nth-child(5n+5) .module-search:focus {
    border-color: #e64980;
    box-shadow: 0 0 0 0.2rem rgba(230, 73, 128, 0.15);
}

/* Modal styles */
.modal-header {
    background-color: #e7f5ff; /* Light blue */
    border-bottom: 1px solid #d0ebff;
    padding: 15px 20px;
}

.modal-title {
    color: #1c7ed6; /* Blue */
    font-weight: 600;
}

.modal-body {
    padding: 25px;
    background-color: #f8f9fa;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 15px 20px;
}

.modal-footer .btn-secondary {
    background-color: #e9ecef;
    color: #495057;
    border: none;
    transition: all 0.2s ease;
}

.modal-footer .btn-secondary:hover {
    background-color: #dee2e6;
    transform: translateY(-2px);
}

/* Unit item styles */
.units-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.unit-item {
    padding: 18px;
    border-radius: 10px;
    transition: all 0.2s ease;
    border: none;
}

/* Pastel colors for different unit types */
.unit-item:nth-child(5n+1) {
    background-color: #e7f5ff; /* Light blue */
}

.unit-item:nth-child(5n+2) {
    background-color: #f3f0ff; /* Light purple */
}

.unit-item:nth-child(5n+3) {
    background-color: #ebfbee; /* Light green */
}

.unit-item:nth-child(5n+4) {
    background-color: #fff3bf; /* Light yellow */
}

.unit-item:nth-child(5n+5) {
    background-color: #ffe8eb; /* Light pink */
}

.unit-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.unit-item:nth-child(5n+1) .unit-type {
    color: #1c7ed6; /* Blue */
    border-bottom: 1px solid #d0ebff;
}

.unit-item:nth-child(5n+2) .unit-type {
    color: #7950f2; /* Purple */
    border-bottom: 1px solid #e5dbff;
}

.unit-item:nth-child(5n+3) .unit-type {
    color: #40c057; /* Green */
    border-bottom: 1px solid #d3f9d8;
}

.unit-item:nth-child(5n+4) .unit-type {
    color: #e67700; /* Yellow/Orange */
    border-bottom: 1px solid #ffec99;
}

.unit-item:nth-child(5n+5) .unit-type {
    color: #e64980; /* Pink */
    border-bottom: 1px solid #ffd0d9;
}

.unit-type {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 10px;
    padding-bottom: 8px;
}

.unit-details {
    color: #495057;
    font-size: 0.9rem;
}

.unit-detail {
    display: flex;
    align-items: center;
}

.unit-detail i {
    color: #6c757d;
    font-size: 0.85rem;
    width: 20px;
    text-align: center;
    opacity: 0.8;
}

/* Debug section styles */
.debug-section {
    margin-top: 30px;
    border-top: 1px dashed #dee2e6;
    padding-top: 20px;
}

#debug-btn {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    color: #6c757d;
    transition: all 0.2s ease;
}

#debug-btn:hover {
    background-color: #e9ecef;
    color: #495057;
}

#debug-result {
    border-radius: 8px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    padding: 15px;
    font-family: monospace;
    font-size: 0.9rem;
}

/* Loading spinner */
.spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    color: #4e73df;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .units-list {
        grid-template-columns: 1fr;
    }

    .module-actions {
        margin-top: 10px;
    }

    .module-item {
        padding: 12px 15px;
    }
}
