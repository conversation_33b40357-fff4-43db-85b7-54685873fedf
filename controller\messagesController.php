<?php
require_once __DIR__ . '/../model/messagesModel.php';
require_once __DIR__ . '/../utils/response.php';

function handleMessage($action, $data) {
    header('Content-Type: application/json');

    try {
        // For debugging
        error_log("Action: " . $action);
        error_log("Data: " . json_encode($data ?? []));
        switch ($action) {
            case 'getAll':
                $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                $limit = isset($_GET['perPage']) ? (int)$_GET['perPage'] : 5; // messages per page
                $messages = getAllMessages($page, $limit);
                echo json_encode($messages);
                break;

            case 'markAsRead':
                if (!isset($data['id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Message ID is required']);
                    return;
                }
                $success = markAsRead($data['id']);
                echo json_encode(['success' => $success]);
                break;

            case 'deleteMessage':
                if (!isset($data['id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Message ID is required']);
                    return;
                }
                $success = deleteMessage($data['id']);
                echo json_encode(['success' => $success]);
                break;

            case 'markAllAsRead':
                $success = markAllAsRead();
                echo json_encode(['success' => $success]);
                break;

            case 'create':
            case 'createMessage':  // Ajout d'un alias pour plus de clarté
                if (!isset($data['title']) || !isset($data['content']) || !isset($data['sender_id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Title, content, and sender_id are required']);
                    return;
                }
                $success = createMessage(
                    $data['sender_id'],
                    $data['title'],
                    $data['content'],
                    $data['receiver_id'] ?? null,  // Ajout du paramètre receiver_id
                    $data['media_url'] ?? null,
                    $data['file_path'] ?? null
                );
                echo json_encode(['success' => $success]);
                break;

            case 'createAdminMessage':
                if (!isset($data['title']) || !isset($data['content']) || !isset($data['receiver_id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Title, content, and receiver ID are required']);
                    return;
                }

                // Log pour le débogage
                error_log("Creating admin message with data: " . json_encode($data));

                // Vérifier si un fichier est attaché
                if (isset($data['file_path']) && $data['file_path']) {
                    error_log("File path received: " . $data['file_path']);
                }

                // Create the message
                $success = createAdminMessage(
                    $data['sender_id'] ?? null, // Let the model find a valid user ID
                    $data['title'],
                    $data['content'],
                    $data['receiver_id'],
                    $data['media_url'] ?? null,
                    $data['file_path'] ?? null
                );

                if (!$success) {
                    http_response_code(500);
                    echo json_encode(['error' => 'Failed to create message. Either the sender ID is invalid or the receiver does not exist. Please check that the receiver ID exists in the appropriate table.']);
                    return;
                }

                echo json_encode(['success' => $success]);
                break;

            case 'checkColumns':
            case 'checkIfDatabaseNeedsUpdate':  // Ajout d'un alias pour plus de clarté
                // Check if the messages table has the required columns
                $needsUpdate = checkIfDatabaseNeedsUpdate();
                echo json_encode(['needsUpdate' => $needsUpdate]);
                break;

            case 'getUnreadCount':
                $count = getUnreadCount();
                echo json_encode(['count' => $count]);
                break;

            default:
                http_response_code(400);
                echo json_encode(['error' => 'Invalid action: ' . $action]);
                break;
        }
    } catch (Exception $e) {
        error_log("Error in handleMessage: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        http_response_code(500);
        echo json_encode([
            'error' => 'Server error: ' . $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
    }
}
?>