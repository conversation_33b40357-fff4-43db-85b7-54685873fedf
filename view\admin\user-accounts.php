<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';

require_once "../../config/constants.php";

$pageTitle = "Gestion des comptes utilisateurs";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - UniAdmin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        /* Styles généraux */
        body {
            background-color: #f5f7fa;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #eaedf2;
            padding: 20px 25px;
            border-radius: 10px 10px 0 0 !important;
        }
        .card-body {
            padding: 25px;
            background-color: #fff;
        }

        /* Styles pour le tableau */
        .table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            margin-bottom: 0;
        }
        .table th {
            background-color: #f8f9fa;
            color: #495057;
            font-weight: 600;
            border-top: none;
            padding: 15px 20px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .table td {
            padding: 15px 20px;
            vertical-align: middle;
            border-top: 1px solid #eaedf2;
            color: #495057;
            font-size: 14px;
        }
        .table-hover tbody tr:hover {
            background-color: #f8f9fa;
        }
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: #fcfcfd;
        }

        /* Styles pour les statuts */
        .user-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-active {
            background-color: #10b981;
        }
        .status-inactive {
            background-color: #ef4444;
        }
        .status-text {
            font-weight: 500;
        }
        .status-active-text {
            color: #10b981;
        }
        .status-inactive-text {
            color: #ef4444;
        }

        /* Styles pour les boutons d'action */
        .action-buttons .btn {
            width: 36px;
            height: 36px;
            padding: 0;
            line-height: 36px;
            text-align: center;
            margin-right: 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }
        .btn-view {
            background-color: #60a5fa;
            border-color: #60a5fa;
            color: white;
        }
        .btn-view:hover {
            background-color: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }
        .btn-toggle-on {
            background-color: #10b981;
            border-color: #10b981;
            color: white;
        }
        .btn-toggle-on:hover {
            background-color: #059669;
            border-color: #059669;
            color: white;
        }
        .btn-toggle-off {
            background-color: #f59e0b;
            border-color: #f59e0b;
            color: white;
        }
        .btn-toggle-off:hover {
            background-color: #d97706;
            border-color: #d97706;
            color: white;
        }
        .btn-delete {
            background-color: #ef4444;
            border-color: #ef4444;
            color: white;
        }
        .btn-delete:hover {
            background-color: #dc2626;
            border-color: #dc2626;
            color: white;
        }

        /* Styles pour la recherche et les filtres */
        .search-filter-container {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
        }
        .search-container .form-control,
        .filter-container .form-select {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px 15px;
            height: 45px;
            font-size: 14px;
            box-shadow: none;
        }
        .search-container .form-control:focus,
        .filter-container .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .search-container .btn,
        .filter-container .btn {
            height: 45px;
            padding: 0 20px;
            font-weight: 500;
            border-radius: 8px;
        }
        .btn-search {
            background-color: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }
        .btn-search:hover {
            background-color: #2563eb;
            border-color: #2563eb;
            color: white;
        }
        .btn-filter {
            background-color: #6366f1;
            border-color: #6366f1;
            color: white;
        }
        .btn-filter:hover {
            background-color: #4f46e5;
            border-color: #4f46e5;
            color: white;
        }

        /* Styles pour la pagination */
        .pagination {
            justify-content: center;
            margin-top: 25px;
        }
        .pagination .page-item .page-link {
            border: none;
            color: #6b7280;
            padding: 8px 16px;
            margin: 0 3px;
            border-radius: 8px;
            font-weight: 500;
            background-color: #f9fafb;
        }
        .pagination .page-item.active .page-link {
            background-color: #3b82f6;
            color: white;
        }
        .pagination .page-item .page-link:hover {
            background-color: #e5e7eb;
        }
        .pagination .page-item.disabled .page-link {
            color: #d1d5db;
            background-color: #f9fafb;
        }

        /* Styles pour les modals */
        .modal-content {
            border: none;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .modal-header {
            background-color: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            padding: 20px 25px;
            border-radius: 12px 12px 0 0;
        }
        .modal-header .modal-title {
            font-weight: 600;
            color: #1e293b;
        }
        .modal-body {
            padding: 25px;
        }
        .modal-footer {
            border-top: 1px solid #e2e8f0;
            padding: 20px 25px;
            border-radius: 0 0 12px 12px;
        }
        .form-label {
            font-weight: 500;
            color: #4b5563;
            margin-bottom: 8px;
        }
        .form-control {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 14px;
        }
        .form-control:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .form-control[readonly] {
            background-color: #f8fafc;
        }

        /* Styles pour les badges */
        .badge {
            padding: 6px 10px;
            font-weight: 500;
            font-size: 12px;
            border-radius: 6px;
        }
        .badge-active {
            background-color: #d1fae5;
            color: #065f46;
        }
        .badge-inactive {
            background-color: #fee2e2;
            color: #991b1b;
        }

        /* Styles pour les boutons du modal */
        .btn-modal-cancel {
            background-color: #f3f4f6;
            border-color: #f3f4f6;
            color: #4b5563;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 8px;
        }
        .btn-modal-cancel:hover {
            background-color: #e5e7eb;
            border-color: #e5e7eb;
        }
        .btn-modal-confirm {
            background-color: #3b82f6;
            border-color: #3b82f6;
            color: white;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 8px;
        }
        .btn-modal-confirm:hover {
            background-color: #2563eb;
            border-color: #2563eb;
        }
        .btn-modal-danger {
            background-color: #ef4444;
            border-color: #ef4444;
            color: white;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 8px;
        }
        .btn-modal-danger:hover {
            background-color: #dc2626;
            border-color: #dc2626;
        }
        .btn-modal-success {
            background-color: #10b981;
            border-color: #10b981;
            color: white;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 8px;
        }
        .btn-modal-success:hover {
            background-color: #059669;
            border-color: #059669;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
    <?php include "../includes/sidebar.php"; ?>

    <div class="main-content">
        <?php include "../includes/header.php"; ?>

        <div class="container-fluid mt-4">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1 class="h3 mb-0 text-gray-800">Gestion des comptes utilisateurs</h1>
                    </div>
                </div>
            </div>

            <!-- Filtres et recherche -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="search-filter-container">
                        <div class="row">
                            <div class="col-md-6 mb-3 mb-md-0">
                                <div class="search-container">
                                    <div class="input-group">
                                        <input type="text" id="searchInput" class="form-control" placeholder="Rechercher par nom d'utilisateur...">
                                        <button class="btn btn-search" id="searchBtn">
                                            <i class="bi bi-search me-2"></i>Rechercher
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="filter-container">
                                    <div class="d-flex">
                                        <select id="roleFilter" class="form-select me-2">
                                            <option value="">Tous les rôles</option>
                                            <option value="admin">Administrateur</option>
                                            <option value="enseignant">Enseignant</option>
                                            <option value="etudiant">Étudiant</option>
                                            <option value="chef_de_departement">Chef de département</option>
                                            <option value="coordinateur">Coordinateur</option>
                                            <option value="vacataire">Vacataire</option>
                                        </select>
                                        <select id="statusFilter" class="form-select me-2">
                                            <option value="">Tous les statuts</option>
                                            <option value="1">Actif</option>
                                            <option value="0">Inactif</option>
                                        </select>
                                        <button class="btn btn-filter" id="filterBtn">
                                            <i class="bi bi-funnel me-2"></i>Filtrer
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tableau des utilisateurs -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Nom d'utilisateur</th>
                                            <th>Rôle</th>
                                            <th>Statut</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTableBody">
                                        <!-- Les données seront chargées dynamiquement -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Pagination des utilisateurs">
                        <ul class="pagination" id="usersPagination">
                            <!-- La pagination sera générée dynamiquement -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de confirmation -->
    <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmModalLabel">Confirmation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="confirmModalBody">
                    Êtes-vous sûr de vouloir effectuer cette action ?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-modal-cancel" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-modal-confirm" id="confirmActionBtn">Confirmer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de détails utilisateur -->
    <div class="modal fade" id="userDetailsModal" tabindex="-1" aria-labelledby="userDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userDetailsModalLabel">Détails de l'utilisateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">ID</label>
                            <input type="text" class="form-control" id="userIdDetail" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Nom d'utilisateur</label>
                            <input type="text" class="form-control" id="usernameDetail" readonly>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Rôle</label>
                            <input type="text" class="form-control" id="roleDetail" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Statut</label>
                            <div class="form-control" id="statusDetail"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-modal-cancel" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-modal-confirm" id="toggleStatusBtn">Changer le statut</button>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Variables globales
            let currentPage = 1;
            let totalPages = 1;
            let currentUserId = null;
            let currentUserStatus = null;

            // Éléments DOM
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');
            const roleFilter = document.getElementById('roleFilter');
            const statusFilter = document.getElementById('statusFilter');
            const filterBtn = document.getElementById('filterBtn');
            const usersTableBody = document.getElementById('usersTableBody');
            const usersPagination = document.getElementById('usersPagination');
            const confirmModal = new bootstrap.Modal(document.getElementById('confirmModal'));
            const confirmModalBody = document.getElementById('confirmModalBody');
            const confirmActionBtn = document.getElementById('confirmActionBtn');
            const userDetailsModal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
            const toggleStatusBtn = document.getElementById('toggleStatusBtn');

            // Charger les utilisateurs au chargement de la page
            loadUsers();

            // Événements
            searchBtn.addEventListener('click', function() {
                currentPage = 1;
                loadUsers();
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    currentPage = 1;
                    loadUsers();
                }
            });

            filterBtn.addEventListener('click', function() {
                currentPage = 1;
                loadUsers();
            });

            // Fonction pour charger les utilisateurs
            function loadUsers() {
                const searchTerm = searchInput.value;
                const roleValue = roleFilter.value;
                const statusValue = statusFilter.value;

                // Afficher un indicateur de chargement
                usersTableBody.innerHTML = '<tr><td colspan="5" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Chargement...</span></div></td></tr>';

                // Appel à l'API
                fetch(`<?php echo BASE_URL; ?>/route/userRoute.php?action=getUsers&page=${currentPage}&search=${encodeURIComponent(searchTerm)}&role=${encodeURIComponent(roleValue)}&status=${encodeURIComponent(statusValue)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayUsers(data.users);
                            totalPages = data.totalPages;
                            displayPagination();
                        } else {
                            usersTableBody.innerHTML = `<tr><td colspan="5" class="text-center">${data.error || 'Erreur lors du chargement des utilisateurs'}</td></tr>`;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        usersTableBody.innerHTML = '<tr><td colspan="5" class="text-center">Erreur lors du chargement des utilisateurs</td></tr>';
                    });
            }

            // Fonction pour afficher les utilisateurs
            function displayUsers(users) {
                if (users.length === 0) {
                    usersTableBody.innerHTML = '<tr><td colspan="5" class="text-center">Aucun utilisateur trouvé</td></tr>';
                    return;
                }

                let html = '';
                users.forEach(user => {
                    const statusClass = user.is_active == 1 ? 'status-active' : 'status-inactive';
                    const statusText = user.is_active == 1 ? 'Actif' : 'Inactif';
                    html += `
                    <tr>
                        <td>${user.id_user}</td>
                        <td>${user.username}</td>
                        <td>${formatRole(user.role)}</td>
                        <td>
                            <span class="user-status ${statusClass}"></span>
                            <span class="status-text ${user.is_active == 1 ? 'status-active-text' : 'status-inactive-text'}">${statusText}</span>
                        </td>
                        <td class="text-center action-buttons">
                            <button class="btn btn-view view-user" data-id="${user.id_user}" title="Voir les détails">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn ${user.is_active == 1 ? 'btn-toggle-off' : 'btn-toggle-on'} toggle-status" data-id="${user.id_user}" data-status="${user.is_active}" title="${user.is_active == 1 ? 'Désactiver' : 'Activer'}">
                                <i class="bi ${user.is_active == 1 ? 'bi-toggle-off' : 'bi-toggle-on'}"></i>
                            </button>
                            <button class="btn btn-delete delete-user" data-id="${user.id_user}" title="Supprimer">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                    `;
                });

                usersTableBody.innerHTML = html;

                // Ajouter les événements aux boutons
                document.querySelectorAll('.view-user').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const userId = this.getAttribute('data-id');
                        viewUserDetails(userId);
                    });
                });

                document.querySelectorAll('.toggle-status').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const userId = this.getAttribute('data-id');
                        const status = this.getAttribute('data-status');
                        confirmToggleStatus(userId, status);
                    });
                });

                document.querySelectorAll('.delete-user').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const userId = this.getAttribute('data-id');
                        confirmDeleteUser(userId);
                    });
                });
            }

            // Fonction pour afficher la pagination
            function displayPagination() {
                let html = '';

                // Bouton précédent
                html += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="Précédent">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                `;

                // Pages
                for (let i = 1; i <= totalPages; i++) {
                    html += `
                    <li class="page-item ${currentPage === i ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                    `;
                }

                // Bouton suivant
                html += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="Suivant">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                `;

                usersPagination.innerHTML = html;

                // Ajouter les événements aux liens de pagination
                document.querySelectorAll('#usersPagination .page-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = parseInt(this.getAttribute('data-page'));
                        if (page >= 1 && page <= totalPages) {
                            currentPage = page;
                            loadUsers();
                        }
                    });
                });
            }

            // Fonction pour formater le rôle
            function formatRole(role) {
                switch (role) {
                    case 'admin':
                        return 'Administrateur';
                    case 'enseignant':
                        return 'Enseignant';
                    case 'etudiant':
                        return 'Étudiant';
                    case 'chef_de_departement':
                        return 'Chef de département';
                    case 'coordinateur':
                        return 'Coordinateur';
                    case 'vacataire':
                        return 'Vacataire';
                    default:
                        return role;
                }
            }

            // Fonction pour afficher les détails d'un utilisateur
            function viewUserDetails(userId) {
                fetch(`<?php echo BASE_URL; ?>/route/userRoute.php?action=getUserDetails&id=${userId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const user = data.user;

                            // Remplir les champs du modal
                            document.getElementById('userIdDetail').value = user.id_user;
                            document.getElementById('usernameDetail').value = user.username;
                            document.getElementById('roleDetail').value = formatRole(user.role);

                            const statusHtml = user.is_active == 1
                                ? '<span class="badge badge-active">Actif</span>'
                                : '<span class="badge badge-inactive">Inactif</span>';
                            document.getElementById('statusDetail').innerHTML = statusHtml;

                            // Stocker l'ID et le statut de l'utilisateur pour les actions
                            currentUserId = user.id_user;
                            currentUserStatus = user.is_active;

                            // Mettre à jour le texte du bouton de changement de statut
                            toggleStatusBtn.textContent = user.is_active == 1 ? 'Désactiver le compte' : 'Activer le compte';
                            toggleStatusBtn.className = user.is_active == 1
                                ? 'btn btn-modal-danger'
                                : 'btn btn-modal-success';

                            // Afficher le modal
                            userDetailsModal.show();
                        } else {
                            alert(data.error || 'Erreur lors du chargement des détails de l\'utilisateur');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Erreur lors du chargement des détails de l\'utilisateur');
                    });
            }

            // Fonction pour confirmer le changement de statut
            function confirmToggleStatus(userId, status) {
                currentUserId = userId;
                currentUserStatus = status;

                const newStatus = status == 1 ? 'désactiver' : 'activer';
                confirmModalBody.textContent = `Êtes-vous sûr de vouloir ${newStatus} ce compte utilisateur ?`;

                // Configurer le bouton de confirmation
                confirmActionBtn.onclick = function() {
                    toggleUserStatus();
                    confirmModal.hide();
                };

                confirmModal.show();
            }

            // Fonction pour confirmer la suppression
            function confirmDeleteUser(userId) {
                currentUserId = userId;

                confirmModalBody.textContent = 'Êtes-vous sûr de vouloir supprimer ce compte utilisateur ? Cette action est irréversible.';

                // Configurer le bouton de confirmation
                confirmActionBtn.onclick = function() {
                    deleteUser();
                    confirmModal.hide();
                };

                confirmModal.show();
            }

            // Fonction pour changer le statut d'un utilisateur
            function toggleUserStatus() {
                const newStatus = currentUserStatus == 1 ? 0 : 1;

                fetch(`<?php echo BASE_URL; ?>/route/userRoute.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'toggleUserStatus',
                        id: currentUserId,
                        status: newStatus
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Recharger les utilisateurs
                        loadUsers();

                        // Fermer le modal de détails s'il est ouvert
                        if (userDetailsModal._isShown) {
                            userDetailsModal.hide();
                        }
                    } else {
                        alert(data.error || 'Erreur lors du changement de statut');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erreur lors du changement de statut');
                });
            }

            // Fonction pour supprimer un utilisateur
            function deleteUser() {
                fetch(`<?php echo BASE_URL; ?>/route/userRoute.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'deleteUser',
                        id: currentUserId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Recharger les utilisateurs
                        loadUsers();
                    } else {
                        alert(data.error || 'Erreur lors de la suppression');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erreur lors de la suppression');
                });
            }

            // Événement pour le bouton de changement de statut dans le modal de détails
            toggleStatusBtn.addEventListener('click', function() {
                confirmToggleStatus(currentUserId, currentUserStatus);
                userDetailsModal.hide();
            });
        });
    </script>
</body>
</html>
