# Service Management System - Quick Start Guide

## 🚀 Immediate Setup

The Service Management System is now fully installed and ready to use! Here's how to get started:

### 1. Access the Admin Interface

1. **Login as Admin**: Make sure you're logged in with admin credentials
2. **Navigate to Service Management**: 
   - Go to **Settings** → **Service Management** in the admin sidebar
   - Or directly access: `view/admin/service-management.php`

### 2. Activate UE Preferences Service

1. **Find UE Preferences Service**: Look for "Collecte des Préférences UE" in the service list
2. **Click Activate**: Click the green "Activer" button
3. **Set Duration**: 
   - Enter duration in hours (e.g., `24` for 1 day, `168` for 1 week)
   - Or set a specific end date/time
4. **Confirm Activation**: Click "Activer le service"

### 3. Test the System

1. **Check Service Status**: The service should show as "Actif" with remaining time
2. **Test Teacher Access**: 
   - Login as a teacher
   - Navigate to UE Preferences page
   - You should see a green status widget showing the service is active
3. **Test Access Control**: 
   - Deactivate the service from admin panel
   - Try accessing UE preferences as teacher
   - You should be redirected with an informative message

## 🎯 Key Features Available Now

### ✅ Admin Controls
- **Service Activation/Deactivation**: Full control over service availability
- **Time-based Management**: Set specific durations or end times
- **Real-time Monitoring**: See active services and remaining time
- **Activity Logs**: View all service actions and who performed them
- **Bulk Operations**: Check and deactivate expired services

### ✅ User Experience
- **Access Control**: Users can only access active services
- **Status Display**: Clear indication of service availability and remaining time
- **Informative Messages**: Helpful messages when services are unavailable
- **Seamless Integration**: Works with existing authentication and workflows

### ✅ Automation
- **Auto-expiration**: Services automatically deactivate when time expires
- **Background Processing**: Cron job support for automatic management
- **Comprehensive Logging**: All actions are logged for audit purposes

## 📋 Default Services Configured

1. **UE Preferences** (`ue_preferences`)
   - Controls teacher UE preference submission
   - Integrated with existing UE preferences workflow

2. **Grade Submission** (`grade_submission`)
   - Controls grade submission periods
   - Ready for integration with grade upload pages

3. **Course Evaluation** (`course_evaluation`)
   - Controls student course evaluation periods
   - Ready for integration with evaluation systems

4. **Schedule Modification** (`schedule_modification`)
   - Controls schedule modification periods
   - Ready for integration with scheduling systems

## 🔧 Optional: Set Up Automatic Expiration

To enable automatic service expiration checking, add this to your server's crontab:

```bash
# Check for expired services every minute
* * * * * /usr/bin/php /path/to/your/project/cron/check_expired_services.php >> /var/log/service_cron.log 2>&1
```

**To add to crontab:**
1. Run: `crontab -e`
2. Add the line above (replace `/path/to/your/project` with actual path)
3. Save and exit

## 🎮 How to Use

### For Administrators

#### Activate a Service
1. Go to Service Management page
2. Find the service you want to activate
3. Click "Activer"
4. Set duration (hours) or specific end time
5. Click "Activer le service"

#### Monitor Services
- **Green status**: Service is active
- **Red status**: Service is inactive
- **Time remaining**: Shows countdown for active services
- **Activity logs**: Click journal icon to view service history

#### Deactivate a Service
1. Find the active service
2. Click "Désactiver"
3. Confirm the action

### For Users (Teachers, Students, etc.)

#### When Service is Active
- Access pages normally
- See green status widget with remaining time
- Submit data/preferences as usual

#### When Service is Inactive
- Redirected from protected pages
- See informative message about service availability
- Contact department head or admin for assistance

## 🔍 Troubleshooting

### Service Not Activating
- Check database connection
- Verify admin permissions
- Check browser console for JavaScript errors

### Users Still Can Access Inactive Service
- Ensure middleware is properly included in protected pages
- Check session management
- Verify service status in database

### Automatic Expiration Not Working
- Set up cron job (see above)
- Check cron job permissions
- Verify cron job logs

## 📞 Support

If you encounter any issues:

1. **Check the logs**: Service logs are available in the admin interface
2. **Verify configuration**: Ensure all files are properly uploaded
3. **Test API endpoints**: Use the provided API URLs to test functionality
4. **Contact support**: Reach out to the development team if needed

## 🎉 You're Ready!

The Service Management System is now fully operational and ready to manage your academic services. Start by activating the UE Preferences service and testing the workflow with your teachers.

**Happy managing! 🚀**
