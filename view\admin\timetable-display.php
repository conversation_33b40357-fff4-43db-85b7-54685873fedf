<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emplo<PERSON> du Temps - UniAdmin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/timetable.css">
    <link rel="stylesheet" href="../assets/css/timetable-display.css">
    <link rel="stylesheet" href="../assets/css/exam-schedule-fixes.css">
    <link rel="stylesheet" href="../assets/css/filter-adjustments.css">
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4 timetable-display-container" style="padding-top: 80px !important;">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Emploi du Temps</h2>
                    <div>
                        <a href="timetable.php" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left me-2"></i>Retour
                        </a>
                    </div>
                </div>



                <!-- Filter Summary -->
                <div class="card animate-fade-in mb-4">
                    <div class="card-header d-flex align-items-center">
                        <div class="filter-icon me-2">
                            <i class="bi bi-funnel-fill text-primary"></i>
                        </div>
                        <div class="card-title mb-0 fw-bold">
                            Filtres Appliqués
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="applied-filters" id="applied-filters">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Error message container - will be hidden by default -->
                <div id="error-container" class="d-none"></div>

                <!-- Class Title (will be populated by JavaScript) -->
                <div id="class-title" class="alert alert-info mt-4 mb-4 d-none">
                    <!-- Will be populated by JavaScript -->
                </div>

                <!-- Schedule Container -->
                <div class="schedule-container animate-fade-in">
                    <div class="schedule-header">
                        <div class="schedule-title">
                            <i class="bi bi-calendar-week"></i>
                            <h2>Class Schedule</h2>
                        </div>
                        <div class="schedule-actions">
                            <button class="add-session-btn" id="add-session-btn">
                                <i class="bi bi-plus"></i> Add Session
                            </button>
                            <button class="download-pdf-btn" id="download-pdf-btn">
                                <i class="bi bi-file-pdf"></i> Download PDF
                            </button>
                        </div>
                    </div>

                    <div class="timetable-wrapper">
                        <table class="timetable">
                            <thead>
                                <tr>
                                    <th>Day / Time</th>
                                    <th class="time-header">08h-10h</th>
                                    <th class="time-header">10h-12h</th>
                                    <th class="time-header">14h-16h</th>
                                    <th class="time-header">16h-18h</th>
                                </tr>
                            </thead>
                            <tbody id="timetable-body">
                                <!-- Days as rows, time slots as columns -->
                                <tr>
                                    <td class="day-slot">Monday</td>
                                    <td class="empty-cell" data-day="1" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="1" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="1" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="1" data-time="16h-18h"></td>
                                </tr>
                                <tr>
                                    <td class="day-slot">Tuesday</td>
                                    <td class="empty-cell" data-day="2" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="2" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="2" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="2" data-time="16h-18h"></td>
                                </tr>
                                <tr>
                                    <td class="day-slot">Wednesday</td>
                                    <td class="empty-cell" data-day="3" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="3" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="3" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="3" data-time="16h-18h"></td>
                                </tr>
                                <tr>
                                    <td class="day-slot">Thursday</td>
                                    <td class="empty-cell" data-day="4" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="4" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="4" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="4" data-time="16h-18h"></td>
                                </tr>
                                <tr>
                                    <td class="day-slot">Friday</td>
                                    <td class="empty-cell" data-day="5" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="5" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="5" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="5" data-time="16h-18h"></td>
                                </tr>
                                <tr>
                                    <td class="day-slot">Saturday</td>
                                    <td class="empty-cell" data-day="6" data-time="08h-10h"></td>
                                    <td class="empty-cell" data-day="6" data-time="10h-12h"></td>
                                    <td class="empty-cell" data-day="6" data-time="14h-16h"></td>
                                    <td class="empty-cell" data-day="6" data-time="16h-18h"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- No Classes Message (shown when no classes are found) -->
                <div id="no-classes-message" class="alert alert-info mt-4 d-none">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    Aucune séance trouvée pour les filtres sélectionnés.
                </div>
            </div>
        </div>
    </div>

    <!-- Add Session Modal -->
    <div class="modal fade" id="addSessionModal" tabindex="-1" aria-labelledby="addSessionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addSessionModalLabel">Add a Session</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addSessionForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="module" class="form-label">Module</label>
                                <select class="form-select" id="module" required>
                                    <option value="" selected disabled>Select a module</option>
                                    <!-- Will be populated from database -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="enseignant" class="form-label">Teacher</label>
                                <select class="form-select" id="enseignant" required>
                                    <option value="" selected disabled>Select a teacher</option>
                                    <!-- Will be populated from database -->
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="salle" class="form-label">Room</label>
                                <select class="form-select" id="salle" required>
                                    <option value="" selected disabled>Select a room</option>
                                    <!-- Will be populated from database -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="type_seance" class="form-label">Session Type</label>
                                <select class="form-select" id="type_seance" required>
                                    <option value="" selected disabled>Select a type</option>
                                    <!-- Will be populated dynamically from database -->
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="day" class="form-label">Day</label>
                                <select class="form-select" id="day" required>
                                    <option value="" selected disabled>Select a day</option>
                                    <option value="1">Monday</option>
                                    <option value="2">Tuesday</option>
                                    <option value="3">Wednesday</option>
                                    <option value="4">Thursday</option>
                                    <option value="5">Friday</option>
                                    <option value="6">Saturday</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="time" class="form-label">Time</label>
                                <select class="form-select" id="time" required>
                                    <option value="" selected disabled>Select a time</option>
                                    <option value="08h-10h">08h-10h</option>
                                    <option value="10h-12h">10h-12h</option>
                                    <option value="14h-16h">14h-16h</option>
                                    <option value="16h-18h">16h-18h</option>
                                </select>
                            </div>
                        </div>
                        <input type="hidden" id="filiere_id" name="filiere_id">
                        <input type="hidden" id="niveau_id" name="niveau_id">
                        <input type="hidden" id="groupe_id" name="groupe_id">
                        <input type="hidden" id="semestre" name="semestre">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveSessionBtn">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Session Modal -->
    <div class="modal fade" id="editSessionModal" tabindex="-1" aria-labelledby="editSessionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editSessionModalLabel">Edit Session</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editSessionForm">
                        <input type="hidden" id="edit_seance_id" name="seance_id">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_module" class="form-label">Module</label>
                                <select class="form-select" id="edit_module" required>
                                    <option value="" selected disabled>Select a module</option>
                                    <!-- Will be populated from database -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_enseignant" class="form-label">Teacher</label>
                                <select class="form-select" id="edit_enseignant" required>
                                    <option value="" selected disabled>Select a teacher</option>
                                    <!-- Will be populated from database -->
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_salle" class="form-label">Room</label>
                                <select class="form-select" id="edit_salle" required>
                                    <option value="" selected disabled>Select a room</option>
                                    <!-- Will be populated from database -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_type_seance" class="form-label">Session Type</label>
                                <select class="form-select" id="edit_type_seance" required>
                                    <option value="" selected disabled>Select a type</option>
                                    <!-- Will be populated dynamically from database -->
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_day" class="form-label">Day</label>
                                <select class="form-select" id="edit_day" required>
                                    <option value="" selected disabled>Select a day</option>
                                    <option value="1">Monday</option>
                                    <option value="2">Tuesday</option>
                                    <option value="3">Wednesday</option>
                                    <option value="4">Thursday</option>
                                    <option value="5">Friday</option>
                                    <option value="6">Saturday</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_time" class="form-label">Time</label>
                                <select class="form-select" id="edit_time" required>
                                    <option value="" selected disabled>Select a time</option>
                                    <option value="08h-10h">08h-10h</option>
                                    <option value="10h-12h">10h-12h</option>
                                    <option value="14h-16h">14h-16h</option>
                                    <option value="16h-18h">16h-18h</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger me-auto" id="deleteSessionBtn">Delete</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="updateSessionBtn">Update</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>
    <script src="../assets/js/timetable-view.js"></script>
    <script src="../assets/js/timetable-pdf.js"></script>
    <script src="../assets/js/filter-width-fix.js"></script>
</body>
</html>