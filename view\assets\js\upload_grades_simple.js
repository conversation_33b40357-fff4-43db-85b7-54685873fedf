/**
 * Simplified JavaScript for upload_grades page
 * Focuses on loading teacher modules and redirecting to student_grades page
 */

// Global variables
let currentTeacherId = null;
let moduleData = {};

// Document ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Upload Grades page loaded');
    
    // Get teacher ID from PHP variable
    if (typeof window.currentTeacherId !== 'undefined') {
        currentTeacherId = window.currentTeacherId;
        console.log('Teacher ID from PHP:', currentTeacherId);
    }
    
    // Initialize the page
    initializePage();
});

/**
 * Initialize the page
 */
function initializePage() {
    // Show loading status
    showLoadingStatus(true);
    
    // Load teacher modules
    loadTeacherModules();
    
    // Setup event listeners
    setupEventListeners();
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Module selection change
    const moduleSelect = document.getElementById('module');
    if (moduleSelect) {
        moduleSelect.addEventListener('change', function() {
            updateModuleInfo();
            checkFormValidity();
        });
    }
    
    // Session selection change
    const sessionSelect = document.getElementById('session');
    if (sessionSelect) {
        sessionSelect.addEventListener('change', function() {
            checkFormValidity();
        });
    }
    
    // Display button click
    const displayButton = document.getElementById('display-button');
    if (displayButton) {
        displayButton.addEventListener('click', function() {
            redirectToStudentGrades();
        });
    }
}

/**
 * Load all modules for the current teacher
 */
async function loadTeacherModules() {
    try {
        if (!currentTeacherId) {
            console.error('No teacher ID available');
            showError('ID enseignant non trouvé. Veuillez actualiser la page.');
            return;
        }
        
        console.log('Loading modules for teacher ID:', currentTeacherId);
        
        const url = `../../controller/importGradesController.php?action=getAllTeacherModules&teacherId=${currentTeacherId}`;
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.data && Array.isArray(data.data) && data.data.length > 0) {
            populateModuleSelect(data.data);
            console.log(`${data.data.length} modules chargés avec succès`);
        } else {
            console.warn('Aucun module trouvé pour cet enseignant');
            showWarning('Aucun module trouvé. Vous devez être assigné à au moins un module pour saisir des notes.');
        }
        
    } catch (error) {
        console.error('Error loading teacher modules:', error);
        showError('Erreur lors du chargement des modules. Veuillez actualiser la page.');
    } finally {
        showLoadingStatus(false);
    }
}

/**
 * Populate the module select dropdown
 */
function populateModuleSelect(modules) {
    const moduleSelect = document.getElementById('module');
    if (!moduleSelect) return;
    
    // Clear existing options
    moduleSelect.innerHTML = '<option value="" disabled selected>Sélectionnez un module</option>';
    
    // Store module data and populate dropdown
    modules.forEach(module => {
        const moduleId = module.id;
        const moduleName = module.nom;
        
        // Store module data for later use
        moduleData[moduleId] = module;
        
        // Add option to select
        const option = document.createElement('option');
        option.value = moduleId;
        option.textContent = moduleName;
        moduleSelect.appendChild(option);
    });
}

/**
 * Update module information display
 */
function updateModuleInfo() {
    const moduleSelect = document.getElementById('module');
    const moduleInfo = document.getElementById('module-info');
    
    if (!moduleSelect || !moduleInfo) return;
    
    const selectedModuleId = moduleSelect.value;
    
    if (selectedModuleId && moduleData[selectedModuleId]) {
        const module = moduleData[selectedModuleId];
        
        // Update display elements
        document.getElementById('selected-module-name').textContent = module.nom || '-';
        document.getElementById('selected-filiere-name').textContent = module.filiere_nom || '-';
        document.getElementById('selected-niveau-name').textContent = module.niveau_nom || '-';
        document.getElementById('selected-semestre-name').textContent = module.semestre_nom || '-';
        
        // Show module info
        moduleInfo.style.display = 'block';
    } else {
        // Hide module info
        moduleInfo.style.display = 'none';
    }
}

/**
 * Check if form is valid and enable/disable submit button
 */
function checkFormValidity() {
    const moduleSelect = document.getElementById('module');
    const sessionSelect = document.getElementById('session');
    const displayButton = document.getElementById('display-button');
    
    if (!moduleSelect || !sessionSelect || !displayButton) return;
    
    const isValid = moduleSelect.value && sessionSelect.value;
    displayButton.disabled = !isValid;
}

/**
 * Redirect to student grades page with selected parameters
 */
function redirectToStudentGrades() {
    const moduleSelect = document.getElementById('module');
    const sessionSelect = document.getElementById('session');
    
    if (!moduleSelect || !sessionSelect) {
        showError('Éléments de formulaire manquants');
        return;
    }
    
    const moduleId = moduleSelect.value;
    const session = sessionSelect.value;
    
    if (!moduleId || !session) {
        showError('Veuillez sélectionner un module et une session');
        return;
    }
    
    if (!moduleData[moduleId]) {
        showError('Données du module non trouvées');
        return;
    }
    
    const module = moduleData[moduleId];
    
    // Prepare URL parameters
    const params = new URLSearchParams({
        module: moduleId,
        filiere: module.filiere_id || '',
        niveau: module.niveau_id || '',
        semestre: module.semestre_id || '',
        session: session,
        module_name: module.nom || '',
        filiere_name: module.filiere_nom || '',
        niveau_name: module.niveau_nom || '',
        semestre_name: module.semestre_nom || ''
    });
    
    // Redirect to student grades page
    const url = `student_grades.php?${params.toString()}`;
    console.log('Redirecting to:', url);
    window.location.href = url;
}

/**
 * Show/hide loading status
 */
function showLoadingStatus(show) {
    const loadingStatus = document.getElementById('loading-status');
    if (loadingStatus) {
        loadingStatus.style.display = show ? 'block' : 'none';
    }
}

/**
 * Show error message
 */
function showError(message) {
    console.error(message);
    alert('Erreur: ' + message);
}

/**
 * Show warning message
 */
function showWarning(message) {
    console.warn(message);
    alert('Attention: ' + message);
}

/**
 * Show success message
 */
function showSuccess(message) {
    console.log(message);
    alert('Succès: ' + message);
}
