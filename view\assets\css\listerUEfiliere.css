/* Custom styles for the listerUEfiliere page */

/* Layout fixes */
.container-fluid {
    padding-left: 0;
    padding-right: 0;
}

.row {
    margin-left: 0;
    margin-right: 0;
    display: flex;
    flex-wrap: wrap;
}

/* Header styles specific to listerUEfiliere */
.content-wrapper h1.h2 {
    color: #212529;
    font-weight: 600;
    font-size: 2rem;
    margin-left: 0.5rem;
}

.content-wrapper .pt-4 {
    padding-top: 2rem !important;
}

.content-wrapper .border-bottom {
    border-bottom: 2px solid #4a90e2 !important;
    width: 10rem;
    margin-left: 0.5rem;
}

/* Card styling */
.card {
    border-radius: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: none;
    margin-top: 1.5rem;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
}

.card-header h5 {
    font-weight: 600;
    color: #212529;
}

.card-body {
    padding: 1.5rem;
    background-color: white;
}

/* Table styling */
.table {
    border-collapse: separate;
    border-spacing: 0 8px;
}

.table th {
    background-color: transparent;
    font-weight: 600;
    border: none;
    color: #6c757d;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    padding: 0.75rem 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border: none;
    background-color: white;
}

.table tbody tr {
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.table tbody tr:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.table tbody tr td:first-child {
    border-radius: 8px 0 0 8px;
}

.table tbody tr td:last-child {
    border-radius: 0 8px 8px 0;
}

/* Button styling with pastel colors */
.btn-pastel-blue, .btn-sm.btn-pastel-blue, .btn-primary, .btn-sm.btn-primary {
    background-color: #d4e5ff !important;
    border-color: #d4e5ff !important;
    color: #4a5568 !important;
    transition: all 0.3s ease;
}

.btn-pastel-blue:hover, .btn-sm.btn-pastel-blue:hover, .btn-primary:hover, .btn-sm.btn-primary:hover {
    background-color: #c5dbff !important;
    border-color: #c5dbff !important;
    color: #4a5568 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.btn-pastel-purple, .btn-sm.btn-pastel-purple {
    background-color: #e2d9ff !important;
    border-color: #e2d9ff !important;
    color: #4a5568 !important;
    transition: all 0.3s ease;
}

.btn-pastel-purple:hover, .btn-sm.btn-pastel-purple:hover {
    background-color: #d5c8ff !important;
    border-color: #d5c8ff !important;
    color: #4a5568 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.btn-pastel-pink, .btn-sm.btn-pastel-pink {
    background-color: #ffe2f0 !important;
    border-color: #ffe2f0 !important;
    color: #4a5568 !important;
    transition: all 0.3s ease;
}

.btn-pastel-pink:hover, .btn-sm.btn-pastel-pink:hover {
    background-color: #ffd4e5 !important;
    border-color: #ffd4e5 !important;
    color: #4a5568 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

/* Action buttons */
.action-btn {
    margin-right: 8px;
    padding: 0.4rem 0.6rem;
    font-size: 0.875rem;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: none !important;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-btn i {
    margin-right: 3px;
    font-size: 1rem;
}

/* Pastel colors for action buttons */
.btn-info.action-btn {
    background-color: #d4e5ff !important;
    border-color: #d4e5ff !important;
    color: #4a5568 !important;
}

.btn-info.action-btn:hover {
    background-color: #c5dbff !important;
    border-color: #c5dbff !important;
}

.btn-warning.action-btn {
    background-color: #fff0c5 !important;
    border-color: #fff0c5 !important;
    color: #4a5568 !important;
}

.btn-warning.action-btn:hover {
    background-color: #ffe7a0 !important;
    border-color: #ffe7a0 !important;
}

.btn-danger.action-btn {
    background-color: #ffe2e2 !important;
    border-color: #ffe2e2 !important;
    color: #4a5568 !important;
}

.btn-danger.action-btn:hover {
    background-color: #ffd0d0 !important;
    border-color: #ffd0d0 !important;
}

/* Pagination styling */
.pagination {
    margin-top: 2rem;
}

.pagination .page-item .page-link {
    color: #6c757d;
    border: none;
    margin: 0 3px;
    border-radius: 50px;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
}

.pagination .page-item.active .page-link {
    background-color: #d4e5ff;
    color: #4a5568;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.pagination .page-item .page-link:hover {
    background-color: #e2d9ff;
    color: #4a5568;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

/* Filter controls */
.form-select, .form-control {
    border-radius: 5px;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #a0c4ff;
    box-shadow: 0 0 0 0.25rem rgba(160, 196, 255, 0.25);
}

/* Input group styling */
.input-group {
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.input-group:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.input-group-text {
    color: #212529;
    border-radius: 50px 0 0 50px !important;
}

.input-group .form-select,
.input-group .form-control {
    border-radius: 0 50px 50px 0 !important;
}

/* Background colors for input groups */
.bg-pastel-blue {
    background-color: #d4e5ff !important;
}

.bg-pastel-purple {
    background-color: #e2d9ff !important;
}

.bg-pastel-pink {
    background-color: #ffe2f0 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .col-md-3 {
        margin-bottom: 10px;
    }

    /* Navbar toggler */
    .navbar-toggler {
        border: none;
        background: transparent;
        padding: 0.25rem 0.5rem;
        font-size: 1.25rem;
    }

    .navbar-toggler:focus {
        outline: none;
        box-shadow: none;
    }
}