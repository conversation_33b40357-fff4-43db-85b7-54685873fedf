<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Get the department head's ID and department ID from the session
$chefId = $_SESSION['user']['teacher_id'] ?? null;
$departmentId = $_SESSION['user']['department_id'] ?? null;

// Get department head's name from session
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$chefName = trim($prenom . ' ' . $nom);
if (empty($chefName)) {
    $chefName = $_SESSION['user']['username'] ?? 'Chef de département';
}

// Get the current academic year
require_once '../../model/affectationModel.php';
$academicYear = getCurrentAcademicYear();

// Page title
$pageTitle = "Affectations d'Enseignement";
$currentPage = "view_affectation.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Système de Gestion ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        :root {
            /* Palette de couleurs pastel */
            --primary-color: #6d8bd3;       /* Bleu pastel */
            --primary-light: rgba(109, 139, 211, 0.1);
            --secondary-color: #7bc5ae;     /* Vert pastel */
            --secondary-light: rgba(123, 197, 174, 0.1);
            --warning-color: #f8d486;       /* Jaune pastel */
            --danger-color: #e6a4a4;        /* Rouge pastel */
            --dark-color: #5a5c69;          /* Gris foncé */
            --light-color: #f8f9fc;         /* Gris très clair */
            --card-border-radius: 0.35rem;  /* Rayon de bordure réduit */
            --transition-speed: 0.2s;       /* Transition plus rapide */
            --spacing-sm: 0.5rem;           /* Petit espacement */
            --spacing-md: 0.75rem;          /* Espacement moyen */
            --spacing-lg: 1rem;             /* Grand espacement */
        }

        /* Page Layout */
        .main-content {
            background-color: #f8f9fc;
        }

        .page-title {
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.2rem;
            font-size: 1.5rem;
        }

        .page-subtitle {
            font-size: 0.9rem;
            margin-bottom: 0;
        }

        /* Container avec espacement réduit */
        .container-fluid {
            padding: var(--spacing-lg) !important;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 0.1rem 0.75rem 0 rgba(58, 59, 69, 0.08);
            margin-bottom: var(--spacing-lg);
            transition: box-shadow var(--transition-speed);
        }

        .card:hover {
            box-shadow: 0 0.15rem 1rem 0 rgba(58, 59, 69, 0.12);
        }

        .card-header {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid #e3e6f0;
            background-color: white;
            border-top-left-radius: var(--card-border-radius) !important;
            border-top-right-radius: var(--card-border-radius) !important;
        }

        /* Filter Styles */
        .filter-section {
            background-color: white;
            border-radius: var(--card-border-radius);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            box-shadow: 0 0.1rem 0.75rem 0 rgba(58, 59, 69, 0.08);
        }

        .filter-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: var(--spacing-sm);
            font-size: 0.85rem;
        }

        .filter-select {
            transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
            cursor: pointer;
            font-size: 0.9rem;
            padding: 0.4rem 0.75rem;
            height: auto;
        }

        .filter-select:hover {
            border-color: var(--primary-color);
        }

        .filter-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.15rem rgba(109, 139, 211, 0.2);
        }

        .filter-active {
            border-color: var(--primary-color);
            background-color: var(--primary-light);
        }

        /* Module Card Styles */
        .module-card {
            border-radius: var(--card-border-radius);
            margin-bottom: var(--spacing-lg);
            transition: transform var(--transition-speed), box-shadow var(--transition-speed);
            overflow: hidden;
        }

        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
        }

        .module-header {
            background-color: var(--primary-light);
            color: var(--primary-color);
            padding: var(--spacing-md);
            font-weight: 600;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .module-body {
            padding: 0;
        }

        .ue-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .ue-item {
            padding: var(--spacing-md);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background-color var(--transition-speed);
        }

        .ue-item:last-child {
            border-bottom: none;
        }

        .ue-item:hover {
            background-color: var(--light-color);
        }

        .ue-type {
            font-weight: 600;
            color: var(--dark-color);
        }

        .ue-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--spacing-sm);
        }

        .ue-teacher {
            display: flex;
            align-items: center;
        }

        .ue-teacher i {
            color: var(--primary-color);
            margin-right: var(--spacing-sm);
        }

        .ue-hours {
            background-color: var(--secondary-light);
            color: var(--secondary-color);
            padding: 0.2rem 0.5rem;
            border-radius: 30px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Badge pour l'année académique */
        .bg-primary-light {
            background-color: var(--primary-light);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 2rem 1rem;
        }

        .empty-state i {
            font-size: 2.5rem;
            color: #d1d1d1;
            margin-bottom: 0.75rem;
        }

        .empty-state h5 {
            font-size: 1rem;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        .empty-state p {
            font-size: 0.85rem;
            color: #6c757d;
        }

        /* Animation pour les cartes */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animated-card {
            animation: fadeIn 0.5s ease-out forwards;
        }

        /* Compteur de résultats */
        .affectation-count {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.75rem;
            padding: 0.25em 0.6em;
            border-radius: 30px;
        }

        /* Statut des filtres */
        .filter-status {
            font-size: 0.8rem;
            color: #6c757d;
        }

        /* Espacement réduit */
        .g-3 {
            --bs-gutter-y: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h1 class="page-title"><?php echo $pageTitle; ?></h1>
                        <p class="page-subtitle text-muted">Consultez les affectations d'enseignement validées pour votre département</p>
                    </div>
                    <div>
                        <span class="badge bg-primary-light text-primary py-1 px-2">
                            <i class="bi bi-calendar-check me-1"></i> <?php echo htmlspecialchars($academicYear); ?>
                        </span>
                    </div>
                </div>

                <!-- Alert Container -->
                <div class="alert-container" id="alertContainer"></div>

                <!-- Filters Section -->
                <div class="filter-section mb-3">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="moduleFilter" class="filter-label">Module</label>
                            <select class="form-select form-select-sm filter-select" id="moduleFilter">
                                <option value="">Tous les modules</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="enseignantFilter" class="filter-label">Enseignant</label>
                            <select class="form-select form-select-sm filter-select" id="enseignantFilter">
                                <option value="">Tous les enseignants</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="ueTypeFilter" class="filter-label">Type d'UE</label>
                            <select class="form-select form-select-sm filter-select" id="ueTypeFilter">
                                <option value="">Tous les types</option>
                                <option value="Cours">Cours magistral</option>
                                <option value="TD">TD</option>
                                <option value="TP">TP</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="semestreFilter" class="filter-label">Semestre</label>
                            <select class="form-select form-select-sm filter-select" id="semestreFilter">
                                <option value="">Tous les semestres</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Affectations Container -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 me-2">Affectations</h5>
                            <span class="affectation-count" id="affectationCount">0</span>
                            <span class="ms-2 filter-status" id="filterStatus"></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="affectationsContainer">
                            <!-- Affectations will be loaded dynamically -->
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="mt-2">Chargement des affectations...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        const departmentId = <?php echo json_encode($departmentId); ?>;
        const academicYear = <?php echo json_encode($academicYear); ?> || '<?php echo date('Y') . '-' . (date('Y') + 1); ?>';
        let affectations = [];
        let filteredAffectations = [];

        // DOM elements
        const affectationsContainer = document.getElementById('affectationsContainer');
        const moduleFilter = document.getElementById('moduleFilter');
        const enseignantFilter = document.getElementById('enseignantFilter');
        const ueTypeFilter = document.getElementById('ueTypeFilter');
        const semestreFilter = document.getElementById('semestreFilter');
        const filterSelects = document.querySelectorAll('.filter-select');
        const affectationCount = document.getElementById('affectationCount');
        const filterStatus = document.getElementById('filterStatus');

        // Load affectations when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            loadAffectations();

            // Add change event listeners to all filter selects
            filterSelects.forEach(select => {
                select.addEventListener('change', function() {
                    // Add visual feedback for active filters
                    if (this.value) {
                        this.classList.add('filter-active');
                    } else {
                        this.classList.remove('filter-active');
                    }

                    // Apply filters immediately
                    applyFilters();
                });
            });
        });

        // Load affectations from the server
        async function loadAffectations() {
            if (!departmentId) {
                showAlert('ID de département non disponible. Veuillez vous reconnecter.', 'danger');
                return;
            }

            try {
                const response = await fetch(`../../route/affectationRoute.php?action=getAffectationsByDepartment&department_id=${departmentId}`);

                // Check if response is OK
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server error:', errorText);
                    showAlert(`Erreur serveur: ${response.status} ${response.statusText}`, 'danger');
                    return;
                }

                // Check content type
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const errorText = await response.text();
                    console.error('Invalid content type:', contentType, 'Response:', errorText);
                    showAlert('Erreur: La réponse du serveur n\'est pas au format JSON', 'danger');
                    return;
                }

                const data = await response.json();

                if (data.error) {
                    showAlert(`Erreur: ${data.error}`, 'danger');
                    return;
                }

                affectations = data.data || [];
                filteredAffectations = [...affectations];

                // Populate filters (this will preserve current filter selections)
                populateFilters();

                // Apply filters (this will update the filtered affectations and render them)
                applyFilters();
            } catch (error) {
                console.error('Error loading affectations:', error);
                showAlert('Erreur lors du chargement des affectations. Vérifiez la console pour plus de détails.', 'danger');
            }
        }

        // Populate filter dropdowns
        function populateFilters() {
            // Save current selections
            const currentModuleValue = moduleFilter.value;
            const currentEnseignantValue = enseignantFilter.value;
            const currentUeTypeValue = ueTypeFilter.value;
            const currentSemestreValue = semestreFilter.value;

            // Clear existing options (except the first one)
            moduleFilter.innerHTML = '<option value="">Tous les modules</option>';
            enseignantFilter.innerHTML = '<option value="">Tous les enseignants</option>';
            semestreFilter.innerHTML = '<option value="">Tous les semestres</option>';

            // Create sets to track unique values
            const modules = new Set();
            const enseignants = new Map();
            const semestres = new Set();

            // Populate sets with unique values
            affectations.forEach(affectation => {
                modules.add(affectation.module_name);
                enseignants.set(affectation.id_enseignant, `${affectation.enseignant_nom} ${affectation.enseignant_prenom}`);
                if (affectation.semestre) {
                    semestres.add(affectation.semestre);
                }
            });

            // Add module options
            [...modules].sort().forEach(module => {
                const option = document.createElement('option');
                option.value = module;
                option.textContent = module;
                option.selected = (module === currentModuleValue);
                moduleFilter.appendChild(option);
            });

            // Add enseignant options
            [...enseignants.entries()].sort((a, b) => a[1].localeCompare(b[1])).forEach(([id, name]) => {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = name;
                option.selected = (id === currentEnseignantValue);
                enseignantFilter.appendChild(option);
            });

            // Add semestre options
            [...semestres].sort().forEach(semestre => {
                const option = document.createElement('option');
                option.value = semestre;
                option.textContent = semestre;
                option.selected = (semestre === currentSemestreValue);
                semestreFilter.appendChild(option);
            });

            // Restore UE type filter value
            if (currentUeTypeValue) {
                ueTypeFilter.value = currentUeTypeValue;
            }

            // Update visual feedback for active filters
            filterSelects.forEach(select => {
                if (select.value) {
                    select.classList.add('filter-active');
                } else {
                    select.classList.remove('filter-active');
                }
            });
        }

        // Apply filters to the affectations
        function applyFilters() {
            const moduleValue = moduleFilter.value;
            const enseignantValue = enseignantFilter.value;
            const ueTypeValue = ueTypeFilter.value;
            const semestreValue = semestreFilter.value;

            // Check if any filter is active
            const isFiltering = moduleValue || enseignantValue || ueTypeValue || semestreValue;

            // Apply filters
            filteredAffectations = affectations.filter(affectation => {
                const moduleMatch = !moduleValue || affectation.module_name === moduleValue;
                const enseignantMatch = !enseignantValue || affectation.id_enseignant === enseignantValue;
                const ueTypeMatch = !ueTypeValue || affectation.ue_type === ueTypeValue;
                const semestreMatch = !semestreValue || affectation.semestre === semestreValue;
                return moduleMatch && enseignantMatch && ueTypeMatch && semestreMatch;
            });

            // Update filter status text
            if (isFiltering) {
                let filterText = [];
                if (moduleValue) filterText.push(`Module: ${moduleValue}`);
                if (enseignantValue) {
                    const enseignantOption = enseignantFilter.querySelector(`option[value="${enseignantValue}"]`);
                    if (enseignantOption) {
                        filterText.push(`Enseignant: ${enseignantOption.textContent}`);
                    }
                }
                if (ueTypeValue) filterText.push(`Type: ${ueTypeValue}`);
                if (semestreValue) filterText.push(`Semestre: ${semestreValue}`);

                filterStatus.textContent = `Filtres actifs: ${filterText.join(', ')}`;
                filterStatus.style.display = 'inline';
            } else {
                filterStatus.textContent = '';
                filterStatus.style.display = 'none';
            }

            // Update count
            affectationCount.textContent = filteredAffectations.length;

            // Render affectations
            renderAffectations();
        }

        // Render affectations grouped by module
        function renderAffectations() {
            if (filteredAffectations.length === 0) {
                affectationsContainer.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-clipboard-x"></i>
                    <h5>Aucune affectation trouvée</h5>
                    <p>Il n'y a pas d'affectations correspondant à vos critères de recherche.</p>
                </div>`;
                return;
            }

            // Group affectations by module
            const moduleGroups = {};
            filteredAffectations.forEach(affectation => {
                const moduleId = affectation.module_id;
                if (!moduleGroups[moduleId]) {
                    moduleGroups[moduleId] = {
                        id: moduleId,
                        name: affectation.module_name,
                        filiere: affectation.nom_filiere,
                        niveau: affectation.niveau,
                        semestre: affectation.semestre,
                        affectations: []
                    };
                }
                moduleGroups[moduleId].affectations.push(affectation);
            });

            // Sort modules by name
            const sortedModules = Object.values(moduleGroups).sort((a, b) => a.name.localeCompare(b.name));

            // Generate HTML for each module
            let html = '<div class="row">';

            sortedModules.forEach((module, index) => {
                // Add animation delay based on index
                const delay = index * 0.1;

                html += `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card module-card animated-card" style="animation-delay: ${delay}s">
                        <div class="module-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>${module.name}</div>
                                <div class="badge bg-primary-light text-primary">${module.semestre || 'N/A'}</div>
                            </div>
                            <div class="text-muted small">${module.filiere} - ${module.niveau || 'N/A'}</div>
                        </div>
                        <div class="module-body">
                            <ul class="ue-list">`;

                // Sort affectations by UE type (Cours, TD, TP)
                const sortedAffectations = module.affectations.sort((a, b) => {
                    const typeOrder = { 'Cours': 1, 'TD': 2, 'TP': 3 };
                    return typeOrder[a.ue_type] - typeOrder[b.ue_type];
                });

                sortedAffectations.forEach(affectation => {
                    html += `
                                <li class="ue-item">
                                    <div class="ue-type">${affectation.ue_type}</div>
                                    <div class="ue-details">
                                        <div class="ue-teacher">
                                            <i class="bi bi-person-badge"></i>
                                            <span>${affectation.enseignant_nom} ${affectation.enseignant_prenom}</span>
                                        </div>
                                        <div class="ue-hours">${affectation.volume_horaire}h</div>
                                    </div>
                                </li>`;
                });

                html += `
                            </ul>
                        </div>
                    </div>
                </div>`;
            });

            html += '</div>';

            affectationsContainer.innerHTML = html;
        }

        // Show an alert message
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = `alert-${Date.now()}`;

            const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>`;

            alertContainer.insertAdjacentHTML('beforeend', alertHtml);

            // Auto-dismiss after 4 seconds
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.classList.remove('show');
                    setTimeout(() => alertElement.remove(), 250);
                }
            }, 4000);
        }
    </script>
</body>
</html>
