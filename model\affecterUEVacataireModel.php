<?php
/**
 * Model for managing teaching unit assignments to part-time lecturers (vacataires)
 * Handles database operations for coordinator assignment functionality
 */

require_once __DIR__ . "/../config/db.php";

/**
 * Create the ue_vacantes table if it doesn't exist
 */
function ensureUeVacantesTableExists() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in ensureUeVacantesTableExists");
        return false;
    }

    $sql = "CREATE TABLE IF NOT EXISTS ue_vacantes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ue_id INT NOT NULL,
        department_id INT NOT NULL,
        academic_year VARCHAR(9) NOT NULL,
        is_vacant BOOLEAN NOT NULL DEFAULT FALSE,
        marked_by INT NOT NULL,
        marked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        comments TEXT,
        UNIQUE KEY unique_ue_dept_year (ue_id, department_id, academic_year),
        FOREI<PERSON><PERSON> KEY (ue_id) REFERENCES uniteenseignement(id) ON DELETE CASCADE,
        FOREIGN KEY (marked_by) REFERENCES enseignant(id_enseignant) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        error_log("Error creating ue_vacantes table: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    mysqli_close($conn);
    return true;
}

/**
 * Get vacant teaching units for a specific filière
 *
 * @param int $filiereId The filière ID
 * @return array Array of vacant teaching units or error
 */
function getVacantUEsByFiliere($filiereId) {
    // Ensure the ue_vacantes table exists
    ensureUeVacantesTableExists();

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getVacantUEsByFiliere");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Get current academic year
    $currentYear = date('Y');
    $nextYear = $currentYear + 1;
    $academicYear = $currentYear . '-' . $nextYear;

    $sql = "SELECT DISTINCT ue.id as ue_id, ue.type as ue_type, ue.volume_horaire, ue.nb_groupes,
            m.id as module_id, m.nom as module_name, m.volume_total,
            f.nom_filiere, f.id_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre,
            uv.is_vacant, uv.comments, uv.marked_at,
            CONCAT(e.prenom, ' ', e.nom) as marked_by_name
            FROM uniteenseignement ue
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveau n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id
            LEFT JOIN ue_vacantes uv ON ue.id = uv.ue_id AND uv.academic_year = '$academicYear'
            LEFT JOIN enseignant e ON uv.marked_by = e.id_enseignant
            WHERE f.id_filiere = '$filiereId'
            AND uv.is_vacant = 1
            AND ue.id NOT IN (
                SELECT DISTINCT COALESCE(aff.id_ue, aff.unite_enseignement_id)
                FROM affectation aff
                WHERE aff.annee_academique = '$academicYear'
                AND aff.statut = 'acceptee'
            )
            ORDER BY m.nom, ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getVacantUEsByFiliere: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching vacant teaching units: " . $error];
    }

    $vacantUEs = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $vacantUEs[] = $row;
    }

    mysqli_close($conn);
    return $vacantUEs;
}

/**
 * Get available vacataires (part-time lecturers) for a specific filière
 *
 * @param int $filiereId The filière ID
 * @return array Array of available vacataires or error
 */
function getAvailableVacatairesByFiliere($filiereId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAvailableVacatairesByFiliere");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Get vacataires from the same department as the filière
    $sql = "SELECT DISTINCT e.id_enseignant, e.CNI, e.nom, e.prenom, e.email, e.tele,
            e.sexe, e.role, d.nom_dep, sp.nom as specialite_nom,
            CONCAT(e.prenom, ' ', e.nom) as full_name
            FROM enseignant e
            LEFT JOIN departement d ON e.id_departement = d.id_departement
            LEFT JOIN specialite sp ON e.id_specialite = sp.id
            WHERE e.role = 'vacataire'
            AND e.id_departement IN (
                SELECT DISTINCT d2.id_departement
                FROM filiere f2
                LEFT JOIN departement d2 ON f2.id_departement = d2.id_departement
                WHERE f2.id_filiere = '$filiereId'
            )
            ORDER BY e.nom, e.prenom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAvailableVacatairesByFiliere: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching available vacataires: " . $error];
    }

    $vacataires = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $vacataires[] = $row;
    }

    mysqli_close($conn);
    return $vacataires;
}

/**
 * Assign teaching units to a vacataire
 *
 * @param int $vacataireId The vacataire's enseignant ID
 * @param array $ueIds Array of teaching unit IDs to assign
 * @param string $academicYear Academic year (optional, defaults to current)
 * @param string $comments Optional comments for the assignment
 * @return array Success status or error
 */
function assignUEsToVacataire($vacataireId, $ueIds, $academicYear = null, $comments = '') {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in assignUEsToVacataire");
        return ["error" => "Database connection error"];
    }

    // Get current academic year if not provided
    if (!$academicYear) {
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $academicYear = $currentYear . '-' . $nextYear;
    }

    $vacataireId = mysqli_real_escape_string($conn, $vacataireId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);
    $comments = mysqli_real_escape_string($conn, $comments);

    // Check table structure to determine correct column names
    $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
    $columns = [];
    while ($column = mysqli_fetch_assoc($columnsCheck)) {
        $columns[] = $column['Field'];
    }

    // Determine correct column names
    $teacherIdField = in_array('professeur_id', $columns) ? 'professeur_id' : 'id_enseignant';
    $ueIdField = in_array('unite_enseignement_id', $columns) ? 'unite_enseignement_id' : 'id_ue';
    $dateField = in_array('created_at', $columns) ? 'created_at' : 'date_affectation';

    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        $assignedCount = 0;
        $errors = [];

        foreach ($ueIds as $ueId) {
            $ueId = mysqli_real_escape_string($conn, $ueId);

            // Check if assignment already exists
            $checkSql = "SELECT id FROM affectation
                        WHERE $teacherIdField = '$vacataireId'
                        AND $ueIdField = '$ueId'
                        AND annee_academique = '$academicYear'";

            $checkResult = mysqli_query($conn, $checkSql);

            if (mysqli_num_rows($checkResult) > 0) {
                $errors[] = "Teaching unit ID $ueId is already assigned to this vacataire";
                continue;
            }

            // Build insert query with correct column names
            $insertColumns = [$teacherIdField, $ueIdField, 'annee_academique', 'statut'];
            $insertValues = ["'$vacataireId'", "'$ueId'", "'$academicYear'", "'acceptee'"];

            if (in_array('commentaire', $columns) && !empty($comments)) {
                $insertColumns[] = 'commentaire';
                $insertValues[] = "'$comments'";
            }

            if (in_array($dateField, $columns)) {
                $insertColumns[] = $dateField;
                $insertValues[] = 'NOW()';
            }

            $insertSql = "INSERT INTO affectation (" . implode(', ', $insertColumns) . ")
                         VALUES (" . implode(', ', $insertValues) . ")";

            $insertResult = mysqli_query($conn, $insertSql);

            if (!$insertResult) {
                $errors[] = "Failed to assign teaching unit ID $ueId: " . mysqli_error($conn);
                continue;
            }

            $assignedCount++;
        }

        if ($assignedCount > 0) {
            // Commit transaction
            mysqli_commit($conn);
            mysqli_close($conn);

            $message = "$assignedCount teaching unit(s) assigned successfully";
            if (!empty($errors)) {
                $message .= ". Errors: " . implode(", ", $errors);
            }

            return ["success" => true, "message" => $message, "assigned_count" => $assignedCount];
        } else {
            // Rollback transaction
            mysqli_rollback($conn);
            mysqli_close($conn);

            return ["error" => "No teaching units were assigned. Errors: " . implode(", ", $errors)];
        }

    } catch (Exception $e) {
        // Rollback transaction on exception
        mysqli_rollback($conn);
        mysqli_close($conn);

        error_log("Exception in assignUEsToVacataire: " . $e->getMessage());
        return ["error" => "Assignment failed: " . $e->getMessage()];
    }
}

/**
 * Get current academic year
 *
 * @return string Current academic year in format YYYY-YYYY
 */
function getCurrentAcademicYear() {
    $currentYear = date('Y');
    $currentMonth = date('n');

    // Academic year typically starts in September (month 9)
    if ($currentMonth >= 9) {
        $startYear = $currentYear;
        $endYear = $currentYear + 1;
    } else {
        $startYear = $currentYear - 1;
        $endYear = $currentYear;
    }

    return $startYear . '-' . $endYear;
}

/**
 * Get assignment statistics for a coordinator's filière
 *
 * @param int $filiereId The filière ID
 * @return array Statistics about assignments
 */
function getAssignmentStatistics($filiereId) {
    // Ensure the ue_vacantes table exists
    ensureUeVacantesTableExists();

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAssignmentStatistics");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $academicYear = getCurrentAcademicYear();

    // Get total vacant UEs
    $vacantSql = "SELECT COUNT(*) as total_vacant
                  FROM ue_vacantes uv
                  JOIN uniteenseignement ue ON uv.ue_id = ue.id
                  JOIN module m ON ue.module_id = m.id
                  WHERE m.filiere_id = '$filiereId'
                  AND uv.is_vacant = 1
                  AND uv.academic_year = '$academicYear'";

    // Check affectation table structure
    $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
    $columns = [];
    while ($column = mysqli_fetch_assoc($columnsCheck)) {
        $columns[] = $column['Field'];
    }

    $ueIdField = in_array('unite_enseignement_id', $columns) ? 'unite_enseignement_id' : 'id_ue';

    // Get assigned vacant UEs
    $assignedSql = "SELECT COUNT(*) as total_assigned
                    FROM affectation aff
                    JOIN uniteenseignement ue ON aff.$ueIdField = ue.id
                    JOIN module m ON ue.module_id = m.id
                    JOIN ue_vacantes uv ON ue.id = uv.ue_id
                    WHERE m.filiere_id = '$filiereId'
                    AND uv.is_vacant = 1
                    AND aff.annee_academique = '$academicYear'
                    AND aff.statut = 'acceptee'";

    // Get total vacataires
    $vacatairesSql = "SELECT COUNT(*) as total_vacataires
                      FROM enseignant e
                      WHERE e.role = 'vacataire'
                      AND e.id_departement IN (
                          SELECT DISTINCT d.id_departement
                          FROM filiere f
                          LEFT JOIN departement d ON f.id_departement = d.id_departement
                          WHERE f.id_filiere = '$filiereId'
                      )";

    $stats = [];

    // Execute queries
    $vacantResult = mysqli_query($conn, $vacantSql);
    if ($vacantResult) {
        $row = mysqli_fetch_assoc($vacantResult);
        $stats['total_vacant'] = $row['total_vacant'];
    } else {
        $stats['total_vacant'] = 0;
    }

    $assignedResult = mysqli_query($conn, $assignedSql);
    if ($assignedResult) {
        $row = mysqli_fetch_assoc($assignedResult);
        $stats['total_assigned'] = $row['total_assigned'];
    } else {
        $stats['total_assigned'] = 0;
    }

    $vacatairesResult = mysqli_query($conn, $vacatairesSql);
    if ($vacatairesResult) {
        $row = mysqli_fetch_assoc($vacatairesResult);
        $stats['total_vacataires'] = $row['total_vacataires'];
    } else {
        $stats['total_vacataires'] = 0;
    }

    $stats['unassigned'] = $stats['total_vacant'] - $stats['total_assigned'];

    mysqli_close($conn);
    return $stats;
}
?>
