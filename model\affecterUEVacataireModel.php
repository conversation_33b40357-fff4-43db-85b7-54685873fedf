<?php
/**
 * Model for managing teaching unit assignments to part-time lecturers (vacataires)
 * Handles database operations for coordinator assignment functionality
 */

require_once __DIR__ . "/../config/db.php";

/**
 * Create the ue_vacantes table if it doesn't exist
 */
function ensureUeVacantesTableExists() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in ensureUeVacantesTableExists");
        return false;
    }

    $sql = "CREATE TABLE IF NOT EXISTS ue_vacantes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ue_id INT NOT NULL,
        department_id INT NOT NULL,
        academic_year VARCHAR(9) NOT NULL,
        is_vacant BOOLEAN NOT NULL DEFAULT FALSE,
        marked_by INT NOT NULL,
        marked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        comments TEXT,
        UNIQUE KEY unique_ue_dept_year (ue_id, department_id, academic_year),
        FOREI<PERSON><PERSON> KEY (ue_id) REFERENCES uniteenseignement(id) ON DELETE CASCADE,
        FOREIGN KEY (marked_by) REFERENCES enseignant(id_enseignant) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        error_log("Error creating ue_vacantes table: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    mysqli_close($conn);
    return true;
}

/**
 * Create sample vacant UEs for testing purposes
 *
 * @param int $filiereId The filière ID
 * @param string $academicYear The academic year
 * @return bool Success status
 */
function createSampleVacantUEs($filiereId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in createSampleVacantUEs");
        return false;
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    // Get some teaching units from the specified filière
    $sql = "SELECT ue.id, m.filiere_id, f.id_departement
            FROM uniteenseignement ue
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            WHERE f.id_filiere = '$filiereId'
            LIMIT 5";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        error_log("Error getting UEs for sample data: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    // Get a default enseignant ID for marking (use first available)
    $enseignantSql = "SELECT id_enseignant FROM enseignant WHERE role IN ('coordinateur', 'chef de departement') LIMIT 1";
    $enseignantResult = mysqli_query($conn, $enseignantSql);
    $markedBy = 1; // Default fallback
    if ($enseignantResult && mysqli_num_rows($enseignantResult) > 0) {
        $enseignantRow = mysqli_fetch_assoc($enseignantResult);
        $markedBy = $enseignantRow['id_enseignant'];
    }

    $insertedCount = 0;
    while ($row = mysqli_fetch_assoc($result)) {
        $ueId = $row['id'];
        $departmentId = $row['id_departement'] ?? 1; // Default to department 1

        // Insert into ue_vacantes table
        $insertSql = "INSERT IGNORE INTO ue_vacantes (ue_id, department_id, academic_year, is_vacant, marked_by, comments)
                      VALUES ('$ueId', '$departmentId', '$academicYear', 1, '$markedBy', 'Sample vacant UE for testing')";

        if (mysqli_query($conn, $insertSql)) {
            $insertedCount++;
        }
    }

    mysqli_close($conn);
    error_log("Created $insertedCount sample vacant UEs for filière $filiereId");
    return $insertedCount > 0;
}

/**
 * Get vacant teaching units for a specific filière
 *
 * @param int $filiereId The filière ID
 * @return array Array of vacant teaching units or error
 */
function getVacantUEsByFiliere($filiereId) {
    // Ensure the ue_vacantes table exists
    ensureUeVacantesTableExists();

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getVacantUEsByFiliere");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Get current academic year
    $currentYear = date('Y');
    $nextYear = $currentYear + 1;
    $academicYear = $currentYear . '-' . $nextYear;

    // First, check if we have any vacant UEs marked in ue_vacantes table
    $checkVacantSql = "SELECT COUNT(*) as count FROM ue_vacantes WHERE academic_year = '$academicYear' AND is_vacant = 1";
    $checkResult = mysqli_query($conn, $checkVacantSql);
    $vacantCount = 0;
    if ($checkResult) {
        $row = mysqli_fetch_assoc($checkResult);
        $vacantCount = $row['count'];
    }

    // If no vacant UEs are marked, create some sample data for testing
    if ($vacantCount == 0) {
        createSampleVacantUEs($filiereId, $academicYear);
    }

    // Check affectation table structure to determine correct column names
    $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
    $columns = [];
    while ($column = mysqli_fetch_assoc($columnsCheck)) {
        $columns[] = $column['Field'];
    }

    $ueIdField = in_array('unite_enseignement_id', $columns) ? 'unite_enseignement_id' : 'id_ue';

    $sql = "SELECT DISTINCT ue.id as ue_id, ue.type as ue_type, ue.volume_horaire, ue.nb_groupes,
            m.id as module_id, m.nom as module_name, m.volume_total,
            f.nom_filiere, f.id_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre,
            uv.is_vacant, uv.comments, uv.marked_at,
            CONCAT(e.prenom, ' ', e.nom) as marked_by_name
            FROM uniteenseignement ue
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveau n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id
            LEFT JOIN ue_vacantes uv ON ue.id = uv.ue_id AND uv.academic_year = '$academicYear'
            LEFT JOIN enseignant e ON uv.marked_by = e.id_enseignant
            WHERE f.id_filiere = '$filiereId'
            AND uv.is_vacant = 1
            AND ue.id NOT IN (
                SELECT DISTINCT aff.$ueIdField
                FROM affectation aff
                WHERE aff.annee_academique = '$academicYear'
                AND aff.statut = 'acceptee'
                AND aff.$ueIdField IS NOT NULL
            )
            ORDER BY m.nom, ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getVacantUEsByFiliere: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching vacant teaching units: " . $error];
    }

    $vacantUEs = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $vacantUEs[] = $row;
    }

    mysqli_close($conn);
    return $vacantUEs;
}

/**
 * Get available vacataires (part-time lecturers) for a specific filière
 *
 * @param int $filiereId The filière ID
 * @return array Array of available vacataires or error
 */
function getAvailableVacatairesByFiliere($filiereId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAvailableVacatairesByFiliere");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // First check if we have any vacataires
    $checkVacatairesSql = "SELECT COUNT(*) as count FROM enseignant WHERE role = 'vacataire'";
    $checkResult = mysqli_query($conn, $checkVacatairesSql);
    $vacataireCount = 0;
    if ($checkResult) {
        $row = mysqli_fetch_assoc($checkResult);
        $vacataireCount = $row['count'];
    }

    // If no vacataires exist, create some sample data for testing
    if ($vacataireCount == 0) {
        createSampleVacataires($filiereId);
    }

    // Get vacataires from the same department as the filière
    $sql = "SELECT DISTINCT e.id_enseignant, e.CNI, e.nom, e.prenom, e.email, e.tele,
            e.sexe, e.role, d.nom_dep, sp.nom as specialite_nom,
            CONCAT(e.prenom, ' ', e.nom) as full_name
            FROM enseignant e
            LEFT JOIN departement d ON e.id_departement = d.id_departement
            LEFT JOIN specialite sp ON e.id_specialite = sp.id
            WHERE e.role = 'vacataire'
            AND e.id_departement IN (
                SELECT DISTINCT d2.id_departement
                FROM filiere f2
                LEFT JOIN departement d2 ON f2.id_departement = d2.id_departement
                WHERE f2.id_filiere = '$filiereId'
            )
            ORDER BY e.nom, e.prenom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAvailableVacatairesByFiliere: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching available vacataires: " . $error];
    }

    $vacataires = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $vacataires[] = $row;
    }

    mysqli_close($conn);
    return $vacataires;
}

/**
 * Create sample vacataires for testing purposes
 *
 * @param int $filiereId The filière ID
 * @return bool Success status
 */
function createSampleVacataires($filiereId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in createSampleVacataires");
        return false;
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);

    // Get the department ID for the filière
    $deptSql = "SELECT id_departement FROM filiere WHERE id_filiere = '$filiereId'";
    $deptResult = mysqli_query($conn, $deptSql);
    $departmentId = 1; // Default fallback
    if ($deptResult && mysqli_num_rows($deptResult) > 0) {
        $deptRow = mysqli_fetch_assoc($deptResult);
        $departmentId = $deptRow['id_departement'];
    }

    // Get a specialite ID (use first available)
    $specSql = "SELECT id FROM specialite LIMIT 1";
    $specResult = mysqli_query($conn, $specSql);
    $specialiteId = 1; // Default fallback
    if ($specResult && mysqli_num_rows($specResult) > 0) {
        $specRow = mysqli_fetch_assoc($specResult);
        $specialiteId = $specRow['id'];
    }

    // Sample vacataires data
    $vacataires = [
        ['CNI' => 'VAC001', 'nom' => 'Alami', 'prenom' => 'Ahmed', 'email' => '<EMAIL>'],
        ['CNI' => 'VAC002', 'nom' => 'Benali', 'prenom' => 'Fatima', 'email' => '<EMAIL>'],
        ['CNI' => 'VAC003', 'nom' => 'Cherkaoui', 'prenom' => 'Mohamed', 'email' => '<EMAIL>']
    ];

    $insertedCount = 0;
    foreach ($vacataires as $vacataire) {
        $cni = mysqli_real_escape_string($conn, $vacataire['CNI']);
        $nom = mysqli_real_escape_string($conn, $vacataire['nom']);
        $prenom = mysqli_real_escape_string($conn, $vacataire['prenom']);
        $email = mysqli_real_escape_string($conn, $vacataire['email']);

        $insertSql = "INSERT IGNORE INTO enseignant (CNI, nom, prenom, email, sexe, role, id_departement, id_specialite, date_debut_travail)
                      VALUES ('$cni', '$nom', '$prenom', '$email', 'Masculin', 'vacataire', '$departmentId', '$specialiteId', CURDATE())";

        if (mysqli_query($conn, $insertSql)) {
            $insertedCount++;
        }
    }

    mysqli_close($conn);
    error_log("Created $insertedCount sample vacataires for filière $filiereId");
    return $insertedCount > 0;
}

/**
 * Assign teaching units to a vacataire
 *
 * @param int $vacataireId The vacataire's enseignant ID
 * @param array $ueIds Array of teaching unit IDs to assign
 * @param string $academicYear Academic year (optional, defaults to current)
 * @param string $comments Optional comments for the assignment
 * @return array Success status or error
 */
function assignUEsToVacataire($vacataireId, $ueIds, $academicYear = null, $comments = '') {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in assignUEsToVacataire");
        return ["error" => "Database connection error"];
    }

    // Get current academic year if not provided
    if (!$academicYear) {
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $academicYear = $currentYear . '-' . $nextYear;
    }

    $vacataireId = mysqli_real_escape_string($conn, $vacataireId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);
    $comments = mysqli_real_escape_string($conn, $comments);

    // Check table structure to determine correct column names
    $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
    $columns = [];
    while ($column = mysqli_fetch_assoc($columnsCheck)) {
        $columns[] = $column['Field'];
    }

    // Determine correct column names
    $teacherIdField = in_array('professeur_id', $columns) ? 'professeur_id' : 'id_enseignant';
    $ueIdField = in_array('unite_enseignement_id', $columns) ? 'unite_enseignement_id' : 'id_ue';
    $dateField = in_array('created_at', $columns) ? 'created_at' : 'date_affectation';

    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        $assignedCount = 0;
        $errors = [];

        foreach ($ueIds as $ueId) {
            $ueId = mysqli_real_escape_string($conn, $ueId);

            // Check if assignment already exists
            $checkSql = "SELECT id FROM affectation
                        WHERE $teacherIdField = '$vacataireId'
                        AND $ueIdField = '$ueId'
                        AND annee_academique = '$academicYear'";

            $checkResult = mysqli_query($conn, $checkSql);

            if (mysqli_num_rows($checkResult) > 0) {
                $errors[] = "Teaching unit ID $ueId is already assigned to this vacataire";
                continue;
            }

            // Build insert query with correct column names
            $insertColumns = [$teacherIdField, $ueIdField, 'annee_academique', 'statut'];
            $insertValues = ["'$vacataireId'", "'$ueId'", "'$academicYear'", "'acceptee'"];

            if (in_array('commentaire', $columns) && !empty($comments)) {
                $insertColumns[] = 'commentaire';
                $insertValues[] = "'$comments'";
            }

            if (in_array($dateField, $columns)) {
                $insertColumns[] = $dateField;
                $insertValues[] = 'NOW()';
            }

            $insertSql = "INSERT INTO affectation (" . implode(', ', $insertColumns) . ")
                         VALUES (" . implode(', ', $insertValues) . ")";

            $insertResult = mysqli_query($conn, $insertSql);

            if (!$insertResult) {
                $errors[] = "Failed to assign teaching unit ID $ueId: " . mysqli_error($conn);
                continue;
            }

            $assignedCount++;
        }

        if ($assignedCount > 0) {
            // Commit transaction
            mysqli_commit($conn);
            mysqli_close($conn);

            $message = "$assignedCount teaching unit(s) assigned successfully";
            if (!empty($errors)) {
                $message .= ". Errors: " . implode(", ", $errors);
            }

            return ["success" => true, "message" => $message, "assigned_count" => $assignedCount];
        } else {
            // Rollback transaction
            mysqli_rollback($conn);
            mysqli_close($conn);

            return ["error" => "No teaching units were assigned. Errors: " . implode(", ", $errors)];
        }

    } catch (Exception $e) {
        // Rollback transaction on exception
        mysqli_rollback($conn);
        mysqli_close($conn);

        error_log("Exception in assignUEsToVacataire: " . $e->getMessage());
        return ["error" => "Assignment failed: " . $e->getMessage()];
    }
}

/**
 * Get current academic year
 *
 * @return string Current academic year in format YYYY-YYYY
 */
function getCurrentAcademicYear() {
    $currentYear = date('Y');
    $currentMonth = date('n');

    // Academic year typically starts in September (month 9)
    if ($currentMonth >= 9) {
        $startYear = $currentYear;
        $endYear = $currentYear + 1;
    } else {
        $startYear = $currentYear - 1;
        $endYear = $currentYear;
    }

    return $startYear . '-' . $endYear;
}

/**
 * Get assignment statistics for a coordinator's filière
 *
 * @param int $filiereId The filière ID
 * @return array Statistics about assignments
 */
function getAssignmentStatistics($filiereId) {
    // Ensure the ue_vacantes table exists
    ensureUeVacantesTableExists();

    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAssignmentStatistics");
        return ["error" => "Database connection error"];
    }

    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    $academicYear = getCurrentAcademicYear();

    // Get total vacant UEs
    $vacantSql = "SELECT COUNT(*) as total_vacant
                  FROM ue_vacantes uv
                  JOIN uniteenseignement ue ON uv.ue_id = ue.id
                  JOIN module m ON ue.module_id = m.id
                  WHERE m.filiere_id = '$filiereId'
                  AND uv.is_vacant = 1
                  AND uv.academic_year = '$academicYear'";

    // Check affectation table structure
    $columnsCheck = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
    $columns = [];
    while ($column = mysqli_fetch_assoc($columnsCheck)) {
        $columns[] = $column['Field'];
    }

    $ueIdField = in_array('unite_enseignement_id', $columns) ? 'unite_enseignement_id' : 'id_ue';

    // Get assigned vacant UEs
    $assignedSql = "SELECT COUNT(*) as total_assigned
                    FROM affectation aff
                    JOIN uniteenseignement ue ON aff.$ueIdField = ue.id
                    JOIN module m ON ue.module_id = m.id
                    JOIN ue_vacantes uv ON ue.id = uv.ue_id
                    WHERE m.filiere_id = '$filiereId'
                    AND uv.is_vacant = 1
                    AND aff.annee_academique = '$academicYear'
                    AND aff.statut = 'acceptee'";

    // Get total vacataires
    $vacatairesSql = "SELECT COUNT(*) as total_vacataires
                      FROM enseignant e
                      WHERE e.role = 'vacataire'
                      AND e.id_departement IN (
                          SELECT DISTINCT d.id_departement
                          FROM filiere f
                          LEFT JOIN departement d ON f.id_departement = d.id_departement
                          WHERE f.id_filiere = '$filiereId'
                      )";

    $stats = [];

    // Execute queries
    $vacantResult = mysqli_query($conn, $vacantSql);
    if ($vacantResult) {
        $row = mysqli_fetch_assoc($vacantResult);
        $stats['total_vacant'] = $row['total_vacant'];
    } else {
        $stats['total_vacant'] = 0;
    }

    $assignedResult = mysqli_query($conn, $assignedSql);
    if ($assignedResult) {
        $row = mysqli_fetch_assoc($assignedResult);
        $stats['total_assigned'] = $row['total_assigned'];
    } else {
        $stats['total_assigned'] = 0;
    }

    $vacatairesResult = mysqli_query($conn, $vacatairesSql);
    if ($vacatairesResult) {
        $row = mysqli_fetch_assoc($vacatairesResult);
        $stats['total_vacataires'] = $row['total_vacataires'];
    } else {
        $stats['total_vacataires'] = 0;
    }

    $stats['unassigned'] = $stats['total_vacant'] - $stats['total_assigned'];

    mysqli_close($conn);
    return $stats;
}
?>
