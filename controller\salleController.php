<?php
require_once "../model/salleModel.php";
require_once "../utils/response.php";

// Get all salles
function getAllSallesAPI() {
    $salles = getAllSalles();

    if (isset($salles['error'])) {
        jsonResponse(['error' => $salles['error']], 404);
    }

    // Log the number of salles found
    error_log("Retrieved " . count($salles) . " salles");

    jsonResponse(['data' => $salles], 200);
}

// Get salle by ID
function getSalleByIdAPI($id) {
    $salle = getSalleById($id);

    if (isset($salle['error'])) {
        jsonResponse(['error' => $salle['error']], 404);
    }

    jsonResponse(['data' => $salle], 200);
}

// Add a new salle
function addSalleAPI() {
    $data = json_decode(file_get_contents("php://input"), true);
    
    if (!$data) {
        jsonResponse(['error' => 'Invalid JSON data'], 400);
    }

    $result = addSalle($data);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
    }

    jsonResponse(['data' => $result, 'message' => 'Room added successfully'], 201);
}

// Update a salle
function updateSalleAPI() {
    $data = json_decode(file_get_contents("php://input"), true);
    
    if (!$data) {
        jsonResponse(['error' => 'Invalid JSON data'], 400);
    }

    $result = updateSalle($data);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
    }

    jsonResponse(['data' => $result, 'message' => 'Room updated successfully'], 200);
}

// Delete a salle
function deleteSalleAPI($id) {
    if (!$id) {
        jsonResponse(['error' => 'Room ID is required'], 400);
    }

    $result = deleteSalle($id);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
    }

    jsonResponse(['data' => $result, 'message' => 'Room deleted successfully'], 200);
}
?>