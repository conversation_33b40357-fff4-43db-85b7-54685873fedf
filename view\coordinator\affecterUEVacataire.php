<?php
// Temporarily disable authentication for testing
// require_once '../includes/auth_check_coordinateur.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// For testing purposes, set a default coordinator session if not set
if (!isset($_SESSION['user'])) {
    // Check if we have session data in different format
    if (isset($_SESSION['id_filiere'])) {
        $_SESSION['user'] = [
            'username' => $_SESSION['username'] ?? 'coordinator',
            'role' => 'coordinateur',
            'filiere_id' => $_SESSION['id_filiere'],
            'filiere_name' => $_SESSION['filiere_name'] ?? 'Unknown',
            'prenom' => $_SESSION['prenom'] ?? '',
            'nom' => $_SESSION['nom'] ?? ''
        ];
    } else {
        $_SESSION['user'] = [
            'username' => 'test_coordinator',
            'role' => 'coordinateur',
            'filiere_id' => 1, // Default to filiere ID 1 (informatique)
            'filiere_name' => 'Informatique',
            'prenom' => 'Test',
            'nom' => 'Coordinator'
        ];
    }
    error_log("[DEBUG] Created test coordinator session for affecterUEVacataire");
}

// Get the coordinator's filiere ID from the session
$filiereId = $_SESSION['user']['filiere_id'] ?? $_SESSION['id_filiere'] ?? 1;
$filiereName = $_SESSION['user']['filiere_name'] ?? $_SESSION['filiere_name'] ?? 'Informatique';

// Get coordinator's name from session
$prenom = $_SESSION['user']['prenom'] ?? $_SESSION['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? $_SESSION['nom'] ?? '';
$coordinatorName = trim($prenom . ' ' . $nom);
if (empty($coordinatorName)) {
    $coordinatorName = $_SESSION['user']['username'] ?? $_SESSION['username'] ?? 'Coordinateur';
}

// Get the current academic year
require_once '../../model/affecterUEVacataireModel.php';
$academicYear = getCurrentAcademicYear();

// Page title
$pageTitle = "Assign Teaching Units to Part-time Lecturers";
$currentPage = "affecterUEVacataire.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Système de Gestion ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/affecterUEVacataire.css">

</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h1 class="page-title"><?php echo $pageTitle; ?></h1>
                        <p class="page-subtitle text-muted">Assign vacant teaching units to part-time lecturers in your program</p>
                    </div>
                    <div>
                        <span class="badge bg-primary-light text-primary py-1 px-2">
                            <i class="bi bi-calendar-check me-1"></i> <?php echo htmlspecialchars($academicYear); ?>
                        </span>
                        <span class="badge bg-info-light text-info py-1 px-2 ms-2">
                            <i class="bi bi-mortarboard me-1"></i> <?php echo htmlspecialchars($filiereName); ?>
                        </span>
                    </div>
                </div>

                <!-- Alert Container -->
                <div class="alert-container" id="alertContainer"></div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card stat-vacant">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div>
                                        <h5 class="stat-number" id="totalVacant">-</h5>
                                        <p class="stat-label">Vacant Units</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card stat-assigned">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div>
                                        <h5 class="stat-number" id="totalAssigned">-</h5>
                                        <p class="stat-label">Assigned</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card stat-unassigned">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <h5 class="stat-number" id="totalUnassigned">-</h5>
                                        <p class="stat-label">Unassigned</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card stat-vacataires">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <h5 class="stat-number" id="totalVacataires">-</h5>
                                        <p class="stat-label">Vacataires</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assignment Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            Create New Assignment
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="assignmentForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="vacataireSelect" class="form-label">
                                        <i class="fas fa-user me-1"></i>
                                        Select Part-time Lecturer
                                    </label>
                                    <select class="form-select" id="vacataireSelect" required>
                                        <option value="">Choose a vacataire...</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="commentsInput" class="form-label">
                                        <i class="fas fa-comment me-1"></i>
                                        Comments (Optional)
                                    </label>
                                    <input type="text" class="form-control" id="commentsInput" placeholder="Add any comments...">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-list me-1"></i>
                                    Select Teaching Units to Assign
                                </label>
                                <div class="teaching-units-container" id="teachingUnitsContainer">
                                    <!-- Teaching units will be loaded here -->
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">Loading vacant teaching units...</p>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>
                                    Clear Selection
                                </button>
                                <button type="submit" class="btn btn-primary" disabled id="assignButton">
                                    <i class="fas fa-save me-1"></i>
                                    Assign Selected Units
                                </button>
                            </div>
                        </form>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery (for easier DOM manipulation) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Pass PHP variables to JavaScript
        const filiereId = <?php echo json_encode($filiereId); ?>;
        const filiereName = <?php echo json_encode($filiereName); ?>;
        const academicYear = <?php echo json_encode($academicYear); ?>;
    </script>
    <script src="../assets/js/affecterUEVacataire.js"></script>

</body>
</html>
