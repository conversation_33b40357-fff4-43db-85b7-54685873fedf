<?php
/**
 * Service Management Model
 * Handles database operations for system service management
 */

require_once __DIR__ . '/../config/db.php';

// Ensure we have the getConnection function
if (!function_exists('getConnection')) {
    function getConnection() {
        $host = 'localhost';
        $username = 'root';
        $password = '';
        $database = 'gestion_ensah';

        $conn = mysqli_connect($host, $username, $password, $database);

        if (!$conn) {
            error_log("Database connection failed: " . mysqli_connect_error());
            return false;
        }

        mysqli_set_charset($conn, "utf8");
        return $conn;
    }
}

/**
 * Ensure the service_management table exists
 */
function ensureServiceManagementTable() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in ensureServiceManagementTable");
        return false;
    }

    $createTableQuery = "
        CREATE TABLE IF NOT EXISTS service_management (
            id INT AUTO_INCREMENT PRIMARY KEY,
            service_name VARCHAR(100) NOT NULL UNIQUE,
            service_key VARCHAR(50) NOT NULL UNIQUE,
            display_name VARCHAR(150) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT FALSE,
            start_time DATETIME NULL,
            end_time DATETIME NULL,
            duration_hours INT NULL,
            auto_deactivate BOOLEAN DEFAULT TRUE,
            created_by VARCHAR(50) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_activated_at DATETIME NULL,
            last_deactivated_at DATETIME NULL,
            activation_count INT DEFAULT 0,
            settings JSON NULL,
            INDEX idx_service_key (service_key),
            INDEX idx_is_active (is_active),
            INDEX idx_end_time (end_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $result = mysqli_query($conn, $createTableQuery);

    if (!$result) {
        error_log("Error creating service_management table: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    // Insert default services if table is empty
    $checkQuery = "SELECT COUNT(*) as count FROM service_management";
    $checkResult = mysqli_query($conn, $checkQuery);
    $count = mysqli_fetch_assoc($checkResult)['count'];

    if ($count == 0) {
        $defaultServices = [
            [
                'service_name' => 'UE Preferences',
                'service_key' => 'ue_preferences',
                'display_name' => 'Collecte des Préférences UE',
                'description' => 'Permet aux enseignants de soumettre leurs préférences pour les unités d\'enseignement',
                'settings' => json_encode([
                    'notification_enabled' => true,
                    'reminder_hours' => [24, 6, 1],
                    'department_notification' => true
                ])
            ],
            [
                'service_name' => 'Grade Submission',
                'service_key' => 'grade_submission',
                'display_name' => 'Soumission des Notes',
                'description' => 'Période de soumission des notes par les enseignants',
                'settings' => json_encode([
                    'notification_enabled' => true,
                    'reminder_hours' => [48, 24, 6],
                    'auto_lock' => true
                ])
            ],
            [
                'service_name' => 'Course Evaluation',
                'service_key' => 'course_evaluation',
                'display_name' => 'Évaluation des Cours',
                'description' => 'Période d\'évaluation des cours par les étudiants',
                'settings' => json_encode([
                    'notification_enabled' => true,
                    'anonymous_feedback' => true,
                    'reminder_hours' => [72, 24]
                ])
            ],
            [
                'service_name' => 'Schedule Modification',
                'service_key' => 'schedule_modification',
                'display_name' => 'Modification d\'Emploi du Temps',
                'description' => 'Période de modification des emplois du temps',
                'settings' => json_encode([
                    'notification_enabled' => true,
                    'approval_required' => true,
                    'reminder_hours' => [24, 6]
                ])
            ]
        ];

        foreach ($defaultServices as $service) {
            $insertQuery = "INSERT INTO service_management (service_name, service_key, display_name, description, settings)
                           VALUES (?, ?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $insertQuery);
            mysqli_stmt_bind_param($stmt, "sssss",
                $service['service_name'],
                $service['service_key'],
                $service['display_name'],
                $service['description'],
                $service['settings']
            );
            mysqli_stmt_execute($stmt);
        }
    }

    mysqli_close($conn);
    return true;
}

/**
 * Get all services
 */
function getAllServices() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllServices");
        return ["error" => "Database connection error"];
    }

    if (!ensureServiceManagementTable()) {
        mysqli_close($conn);
        return ["error" => "Failed to ensure service management table"];
    }

    $query = "SELECT * FROM service_management ORDER BY service_name";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        error_log("Error getting services: " . mysqli_error($conn));
        mysqli_close($conn);
        return ["error" => "Error fetching services"];
    }

    $services = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Parse JSON settings
        if ($row['settings']) {
            $row['settings'] = json_decode($row['settings'], true);
        }

        // Calculate remaining time if active
        if ($row['is_active'] && $row['end_time']) {
            $now = new DateTime();
            $endTime = new DateTime($row['end_time']);
            if ($endTime > $now) {
                $interval = $now->diff($endTime);
                $row['remaining_time'] = [
                    'days' => $interval->days,
                    'hours' => $interval->h,
                    'minutes' => $interval->i,
                    'total_minutes' => ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i
                ];
            } else {
                $row['remaining_time'] = null;
                $row['expired'] = true;
            }
        }

        $services[] = $row;
    }

    mysqli_close($conn);
    return $services;
}

/**
 * Get service by key
 */
function getServiceByKey($serviceKey) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getServiceByKey");
        return false;
    }

    $serviceKey = mysqli_real_escape_string($conn, $serviceKey);
    $query = "SELECT * FROM service_management WHERE service_key = '$serviceKey'";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        error_log("Error getting service by key: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    $service = mysqli_fetch_assoc($result);

    if ($service && $service['settings']) {
        $service['settings'] = json_decode($service['settings'], true);
    }

    mysqli_close($conn);
    return $service;
}

/**
 * Check if a service is active
 */
function isServiceActive($serviceKey) {
    $service = getServiceByKey($serviceKey);

    if (!$service) {
        return false;
    }

    if (!$service['is_active']) {
        return false;
    }

    // Check if service has expired
    if ($service['end_time']) {
        $now = new DateTime();
        $endTime = new DateTime($service['end_time']);

        if ($endTime <= $now) {
            // Service has expired, deactivate it
            deactivateService($serviceKey, 'auto_expired');
            return false;
        }
    }

    return true;
}

/**
 * Activate a service
 */
function activateService($serviceKey, $durationHours = null, $endTime = null, $activatedBy = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in activateService");
        return ["error" => "Database connection error"];
    }

    try {
        mysqli_begin_transaction($conn);

        $serviceKey = mysqli_real_escape_string($conn, $serviceKey);
        $activatedBy = $activatedBy ? mysqli_real_escape_string($conn, $activatedBy) : null;

        $now = date('Y-m-d H:i:s');
        $calculatedEndTime = null;

        if ($endTime) {
            $calculatedEndTime = $endTime;
        } elseif ($durationHours) {
            $calculatedEndTime = date('Y-m-d H:i:s', strtotime("+$durationHours hours"));
        }

        $updateQuery = "UPDATE service_management
                       SET is_active = TRUE,
                           start_time = '$now',
                           end_time = " . ($calculatedEndTime ? "'$calculatedEndTime'" : "NULL") . ",
                           duration_hours = " . ($durationHours ? $durationHours : "NULL") . ",
                           last_activated_at = '$now',
                           activation_count = activation_count + 1,
                           updated_at = '$now'
                       WHERE service_key = '$serviceKey'";

        $result = mysqli_query($conn, $updateQuery);

        if (!$result) {
            throw new Exception("Error activating service: " . mysqli_error($conn));
        }

        if (mysqli_affected_rows($conn) === 0) {
            throw new Exception("Service not found: $serviceKey");
        }

        // Log the activation
        logServiceAction($serviceKey, 'activated', $activatedBy, [
            'duration_hours' => $durationHours,
            'end_time' => $calculatedEndTime
        ]);

        mysqli_commit($conn);
        mysqli_close($conn);

        return ["success" => true, "message" => "Service activated successfully"];

    } catch (Exception $e) {
        mysqli_rollback($conn);
        error_log("Error in activateService: " . $e->getMessage());
        mysqli_close($conn);
        return ["error" => $e->getMessage()];
    }
}

/**
 * Extend a service duration
 */
function extendService($serviceKey, $additionalHours = null, $newEndTime = null, $extendedBy = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in extendService");
        return ["error" => "Database connection error"];
    }

    try {
        mysqli_begin_transaction($conn);

        $serviceKey = mysqli_real_escape_string($conn, $serviceKey);
        $extendedBy = $extendedBy ? mysqli_real_escape_string($conn, $extendedBy) : null;

        // Get current service data
        $service = getServiceByKey($serviceKey);
        if (!$service) {
            throw new Exception("Service not found: $serviceKey");
        }

        if (!$service['is_active']) {
            throw new Exception("Cannot extend inactive service");
        }

        $now = date('Y-m-d H:i:s');
        $currentEndTime = $service['end_time'];
        $calculatedNewEndTime = null;

        if ($newEndTime) {
            $calculatedNewEndTime = $newEndTime;
        } elseif ($additionalHours && $currentEndTime) {
            $calculatedNewEndTime = date('Y-m-d H:i:s', strtotime($currentEndTime . " +$additionalHours hours"));
        } elseif ($additionalHours) {
            // If no current end time, extend from now
            $calculatedNewEndTime = date('Y-m-d H:i:s', strtotime("+$additionalHours hours"));
        }

        if (!$calculatedNewEndTime) {
            throw new Exception("Invalid extension parameters");
        }

        // Validate that new end time is in the future
        $newEndTimeObj = new DateTime($calculatedNewEndTime);
        $nowObj = new DateTime();
        if ($newEndTimeObj <= $nowObj) {
            throw new Exception("New end time must be in the future");
        }

        $updateQuery = "UPDATE service_management
                       SET end_time = '$calculatedNewEndTime',
                           updated_at = '$now'
                       WHERE service_key = '$serviceKey'";

        $result = mysqli_query($conn, $updateQuery);

        if (!$result) {
            throw new Exception("Error extending service: " . mysqli_error($conn));
        }

        if (mysqli_affected_rows($conn) === 0) {
            throw new Exception("Service not found: $serviceKey");
        }

        // Log the extension
        logServiceAction($serviceKey, 'extended', $extendedBy, [
            'additional_hours' => $additionalHours,
            'previous_end_time' => $currentEndTime,
            'new_end_time' => $calculatedNewEndTime
        ]);

        mysqli_commit($conn);
        mysqli_close($conn);

        return ["success" => true, "message" => "Service extended successfully", "new_end_time" => $calculatedNewEndTime];

    } catch (Exception $e) {
        mysqli_rollback($conn);
        error_log("Error in extendService: " . $e->getMessage());
        mysqli_close($conn);
        return ["error" => $e->getMessage()];
    }
}

/**
 * Deactivate a service
 */
function deactivateService($serviceKey, $reason = 'manual', $deactivatedBy = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in deactivateService");
        return ["error" => "Database connection error"];
    }

    try {
        mysqli_begin_transaction($conn);

        $serviceKey = mysqli_real_escape_string($conn, $serviceKey);
        $deactivatedBy = $deactivatedBy ? mysqli_real_escape_string($conn, $deactivatedBy) : null;

        $now = date('Y-m-d H:i:s');

        $updateQuery = "UPDATE service_management
                       SET is_active = FALSE,
                           last_deactivated_at = '$now',
                           updated_at = '$now'
                       WHERE service_key = '$serviceKey'";

        $result = mysqli_query($conn, $updateQuery);

        if (!$result) {
            throw new Exception("Error deactivating service: " . mysqli_error($conn));
        }

        if (mysqli_affected_rows($conn) === 0) {
            throw new Exception("Service not found: $serviceKey");
        }

        // Log the deactivation
        logServiceAction($serviceKey, 'deactivated', $deactivatedBy, [
            'reason' => $reason
        ]);

        mysqli_commit($conn);
        mysqli_close($conn);

        return ["success" => true, "message" => "Service deactivated successfully"];

    } catch (Exception $e) {
        mysqli_rollback($conn);
        error_log("Error in deactivateService: " . $e->getMessage());
        mysqli_close($conn);
        return ["error" => $e->getMessage()];
    }
}

/**
 * Log service actions
 */
function logServiceAction($serviceKey, $action, $performedBy = null, $details = []) {
    $conn = getConnection();

    if (!$conn) {
        return false;
    }

    // Create service_logs table if it doesn't exist
    $createLogTableQuery = "
        CREATE TABLE IF NOT EXISTS service_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            service_key VARCHAR(50) NOT NULL,
            action VARCHAR(50) NOT NULL,
            performed_by VARCHAR(50) NULL,
            details JSON NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_service_key (service_key),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    mysqli_query($conn, $createLogTableQuery);

    $serviceKey = mysqli_real_escape_string($conn, $serviceKey);
    $action = mysqli_real_escape_string($conn, $action);
    $performedBy = $performedBy ? mysqli_real_escape_string($conn, $performedBy) : null;
    $detailsJson = json_encode($details);

    $insertQuery = "INSERT INTO service_logs (service_key, action, performed_by, details)
                   VALUES ('$serviceKey', '$action', " . ($performedBy ? "'$performedBy'" : "NULL") . ", '$detailsJson')";

    $result = mysqli_query($conn, $insertQuery);
    mysqli_close($conn);

    return $result;
}

/**
 * Check and deactivate expired services
 */
function checkExpiredServices() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in checkExpiredServices");
        return false;
    }

    $now = date('Y-m-d H:i:s');
    $query = "SELECT service_key FROM service_management
              WHERE is_active = TRUE
              AND end_time IS NOT NULL
              AND end_time <= '$now'
              AND auto_deactivate = TRUE";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        error_log("Error checking expired services: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    $expiredServices = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $serviceKey = $row['service_key'];
        $deactivateResult = deactivateService($serviceKey, 'auto_expired', 'system');

        if (isset($deactivateResult['success'])) {
            $expiredServices[] = $serviceKey;
        }
    }

    mysqli_close($conn);
    return $expiredServices;
}

?>
