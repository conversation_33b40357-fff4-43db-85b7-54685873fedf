// Helper function to get URL parameters
function getUrlParams() {
    const params = {};
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);

    for (const [key, value] of urlParams.entries()) {
        params[key] = value;
    }

    return params;
}

// Function to display the applied filters
function displayAppliedFilters(filters) {
    const appliedFiltersContainer = document.getElementById('applied-filters');
    if (!appliedFiltersContainer) return;

    let filtersHtml = '<div class="filter-badges">';

    // Filière
    if (filters.filiere && filters.filiere !== 'none') {
        filtersHtml += `<div class="filter-badge"><span class="filter-label">Field:</span> <span class="filter-value">${filters.filiereName || filters.filiere}</span></div>`;
    }

    // Niveau
    if (filters.niveau) {
        filtersHtml += `<div class="filter-badge"><span class="filter-label">Level:</span> <span class="filter-value">${filters.niveauName || filters.niveau}</span></div>`;
    }

    // Groupe
    if (filters.groupe && filters.groupe !== 'all') {
        filtersHtml += `<div class="filter-badge"><span class="filter-label">Group:</span> <span class="filter-value">${filters.groupeName || filters.groupe}</span></div>`;
    }

    // Semestre
    if (filters.semestre) {
        filtersHtml += `<div class="filter-badge"><span class="filter-label">Semester:</span> <span class="filter-value">${filters.semestre}</span></div>`;
    }

    // Semaine
    if (filters.semaine) {
        filtersHtml += `<div class="filter-badge"><span class="filter-label">Week:</span> <span class="filter-value">${filters.semaine}</span></div>`;
    }

    filtersHtml += '</div>';

    appliedFiltersContainer.innerHTML = filtersHtml;
}

// Function to show error message
function showErrorMessage(message) {
    console.log('Showing error message:', message);

    // First try to use the error-container
    const errorContainer = document.getElementById('error-container');
    if (errorContainer) {
        errorContainer.className = 'alert alert-danger mt-4';
        errorContainer.innerHTML = `
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            Error loading data: ${message}
        `;
        errorContainer.classList.remove('d-none');
        return;
    }

    // Fallback to alert
    alert(`Error: ${message}`);
}

// Function to hide error message
function hideErrorMessage() {
    const errorContainer = document.getElementById('error-container');
    if (errorContainer) {
        errorContainer.classList.add('d-none');
    }
}

// Function to show success message
function showSuccessMessage(message) {
    // Create a success message element if it doesn't exist
    let successMessage = document.getElementById('success-message');
    if (!successMessage) {
        successMessage = document.createElement('div');
        successMessage.id = 'success-message';
        successMessage.className = 'alert alert-success mt-4 animate-fade-in';
        successMessage.style.position = 'fixed';
        successMessage.style.top = '20px';
        successMessage.style.right = '20px';
        successMessage.style.zIndex = '9999';
        document.body.appendChild(successMessage);
    }

    // Set the message
    successMessage.innerHTML = `
        <i class="bi bi-check-circle-fill me-2"></i>
        ${message}
    `;

    // Show the message
    successMessage.classList.remove('d-none');

    // Hide the message after 3 seconds
    setTimeout(() => {
        successMessage.classList.add('d-none');
    }, 3000);
}

// Function to create a class card
function createClassCard(classData) {
    console.log('Creating class card for:', classData);

    // Create the card element
    const card = document.createElement('div');
    card.className = 'class-card exam';

    // Set the ID - for exams, use id_examen instead of id_seance
    const examId = classData.id_examen || classData.id_exam || classData.id_seance || '';
    card.dataset.id = examId;

    // Determine the card type class based on session type
    const typeClass = getCardTypeClass(classData.type || classData.type_exam || classData.type_seance);
    if (typeClass) {
        card.classList.add(typeClass);
    }

    // Create the card header with subject and actions
    const cardHeader = document.createElement('div');
    cardHeader.className = 'class-card-header';

    // Create the subject element
    const subject = document.createElement('div');
    subject.className = 'class-subject';
    subject.textContent = classData.type || classData.type_exam || classData.type_seance || 'Exam';
    cardHeader.appendChild(subject);

    // Create actions container
    const actions = document.createElement('div');
    actions.className = 'class-actions';

    // Add delete button
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'btn-delete';
    deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
    deleteBtn.title = 'Delete exam';

    // Add click event to delete button with stopPropagation to prevent card click
    deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        confirmDeleteExam(examId);
    });

    actions.appendChild(deleteBtn);
    cardHeader.appendChild(actions);
    card.appendChild(cardHeader);

    // Create the title element
    const title = document.createElement('div');
    title.className = 'class-title';
    title.textContent = classData.nom_module || classData.module || 'Module';
    card.appendChild(title);

    // Create the details element
    const details = document.createElement('div');
    details.className = 'class-details';

    // Add professor
    const professor = document.createElement('div');
    professor.className = 'class-detail';

    // Format professor name
    let profName = 'Professor';
    if (classData.nom_enseignant) {
        profName = classData.nom_enseignant;
    } else if (classData.enseignant) {
        profName = classData.enseignant;
    } else if (classData.prof_nom && classData.prof_prenom) {
        profName = `${classData.prof_nom} ${classData.prof_prenom}`;
    }

    professor.innerHTML = `<i class="bi bi-person-fill"></i> ${profName}`;
    details.appendChild(professor);

    // Add room
    const room = document.createElement('div');
    room.className = 'class-detail';
    room.innerHTML = `<i class="bi bi-geo-alt-fill"></i> ${classData.nom_salle || classData.salle || 'Room'}`;
    details.appendChild(room);

    // Add time
    const time = document.createElement('div');
    time.className = 'class-detail';
    time.innerHTML = `<i class="bi bi-clock-fill"></i> ${classData.heure_debut || ''}-${classData.heure_fin || ''}`;
    details.appendChild(time);

    // Add date if available
    if (classData.date_examen || classData.date_exam) {
        const date = document.createElement('div');
        date.className = 'class-detail';

        // Use the formatted date from the server if available, otherwise format it here
        let formattedDate = classData.formatted_date || classData.date_examen || classData.date_exam;
        if (!classData.formatted_date) {
            try {
                const dateObj = new Date(classData.date_examen || classData.date_exam);
                formattedDate = dateObj.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });
            } catch (e) {
                console.error('Error formatting date:', e);
            }
        }

        date.innerHTML = `<i class="bi bi-calendar-date-fill"></i> ${formattedDate}`;
        details.appendChild(date);
    }

    card.appendChild(details);

    // Add click event to edit the session
    card.addEventListener('click', () => {
        openAddSessionModal(
            classData.jour || classData.day,
            `${classData.heure_debut}-${classData.heure_fin}`,
            examId
        );
    });

    return card;
}

// Function to get card type class based on session type
function getCardTypeClass(typeSeance) {
    if (!typeSeance) return '';

    const typeMap = {
        'Exam': 'exam',
        'Examen': 'exam',
        'Examen final': 'exam',
        'Final': 'exam',
        'Midterm': 'exam',
        'Quiz': 'exam',
        'Test': 'exam',
        'DS': 'exam',
        'Rattrapage': 'exam'
    };

    const lowerType = typeSeance.toLowerCase();

    for (const [key, value] of Object.entries(typeMap)) {
        if (lowerType.includes(key.toLowerCase())) {
            return value;
        }
    }

    return 'exam'; // Default to exam for all types in the exam schedule
}

// Function to clear the timetable
function clearTimetable() {
    console.log('Clearing timetable completely');

    // Get all cells except the time slots
    const cells = document.querySelectorAll('td[data-day][data-time]');

    // Clear each cell
    cells.forEach(cell => {
        cell.innerHTML = '';
        cell.className = 'empty-cell';

        // Remove any data attributes except day and time
        const attributes = Array.from(cell.attributes);
        attributes.forEach(attr => {
            if (attr.name !== 'data-day' && attr.name !== 'data-time' && attr.name.startsWith('data-')) {
                cell.removeAttribute(attr.name);
            }
        });
    });
}

// Function to update the timetable with exam data
function updateTimetable(exams) {
    console.log('Updating timetable with', exams.length, 'exams');

    // Debug: Log all cells in the timetable
    const allCells = document.querySelectorAll('td[data-day][data-time]');
    console.log(`Found ${allCells.length} cells in the timetable`);

    // Log all available time slots for debugging
    const availableTimeSlots = new Set();
    allCells.forEach(cell => {
        availableTimeSlots.add(cell.dataset.time);
    });
    console.log('Available time slots:', [...availableTimeSlots]);

    // Process each exam
    exams.forEach(examData => {
        console.log('Processing exam data:', examData);

        // For exams, we need to determine the day
        let day;

        // Priority 1: If day is a string (like "Lundi"), convert it to a number
        if (examData.day && typeof examData.day === 'string') {
            const dayMap = {
                'lundi': 1, 'monday': 1,
                'mardi': 2, 'tuesday': 2,
                'mercredi': 3, 'wednesday': 3,
                'jeudi': 4, 'thursday': 4,
                'vendredi': 5, 'friday': 5,
                'samedi': 6, 'saturday': 6,
                'dimanche': 7, 'sunday': 7
            };
            const dayLower = examData.day.toLowerCase();
            if (dayMap[dayLower]) {
                day = dayMap[dayLower];
                console.log(`Converted string day "${examData.day}" to number: ${day}`);
            }
        }
        // Priority 2: Use day field if it's a number
        else if (examData.day && !isNaN(examData.day)) {
            day = parseInt(examData.day);
            console.log(`Using day from data.day: ${day}`);
        }
        // Priority 3: Use jour field if it's a number
        else if (examData.jour && !isNaN(examData.jour)) {
            day = parseInt(examData.jour);
            console.log(`Using day from data.jour: ${day}`);
        }
        // Priority 4: If jour is a string (like "Lundi"), convert it to a number
        else if (examData.jour && typeof examData.jour === 'string') {
            const dayMap = {
                'lundi': 1, 'monday': 1,
                'mardi': 2, 'tuesday': 2,
                'mercredi': 3, 'wednesday': 3,
                'jeudi': 4, 'thursday': 4,
                'vendredi': 5, 'friday': 5,
                'samedi': 6, 'saturday': 6,
                'dimanche': 7, 'sunday': 7
            };
            const dayLower = examData.jour.toLowerCase();
            if (dayMap[dayLower]) {
                day = dayMap[dayLower];
                console.log(`Converted string jour "${examData.jour}" to number: ${day}`);
            }
        }
        // Priority 5: Use calculated_day from SQL if available
        else if (examData.calculated_day) {
            // DAYOFWEEK() returns 1 for Sunday, 2 for Monday, etc.
            // We need to convert to 1 for Monday, 2 for Tuesday, etc.
            const calculatedDay = parseInt(examData.calculated_day);
            const adjustedDay = (calculatedDay === 1) ? 7 : calculatedDay - 1;
            day = adjustedDay;
            console.log(`Using calculated_day from SQL: ${examData.calculated_day} -> adjusted to: ${adjustedDay}`);
        }
        // Priority 6: Calculate from date_examen if available
        else if (examData.date_examen) {
            try {
                const examDate = new Date(examData.date_examen);
                const dayOfWeek = examDate.getDay(); // 0 (Sunday) to 6 (Saturday)
                day = dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday (0) to 7
                console.log(`Calculated day from date_examen (${examData.date_examen}): ${day}`);
            } catch (e) {
                console.error(`Error calculating day from date_examen (${examData.date_examen}):`, e);
            }
        }
        // Priority 7: Calculate from date_exam if available (for backward compatibility)
        else if (examData.date_exam) {
            try {
                const examDate = new Date(examData.date_exam);
                const dayOfWeek = examDate.getDay(); // 0 (Sunday) to 6 (Saturday)
                day = dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday (0) to 7
                console.log(`Calculated day from date_exam (${examData.date_exam}): ${day}`);
            } catch (e) {
                console.error(`Error calculating day from date_exam (${examData.date_exam}):`, e);
            }
        }

        // If we still don't have a day, skip this exam
        if (!day) {
            console.warn('Could not determine day for exam:', examData);
            return; // Skip to next exam
        }

        // Get the time slot - normalize format to match the timetable
        let startTime = examData.heure_debut || '';
        let endTime = examData.heure_fin || '';

        // Normalize time format (e.g., "08:00:00" to "08h", "10:00:00" to "10h")
        startTime = startTime.replace(/^(\d+):00:00$/, '$1h');
        endTime = endTime.replace(/^(\d+):00:00$/, '$1h');

        // If still not in the right format, try another approach
        if (!startTime.endsWith('h')) {
            startTime = startTime.replace(/^(\d+).*$/, '$1h');
        }
        if (!endTime.endsWith('h')) {
            endTime = endTime.replace(/^(\d+).*$/, '$1h');
        }

        const timeSlot = `${startTime}-${endTime}`;
        console.log(`Looking for cell with day=${day}, time=${timeSlot}`);

        // Try to find the cell
        let cell = document.querySelector(`td[data-day="${day}"][data-time="${timeSlot}"]`);

        // If cell not found, try with different time formats
        if (!cell) {
            // Try all available time slots to find a match
            for (const availableSlot of availableTimeSlots) {
                const [availableStart, availableEnd] = availableSlot.split('-');
                const examStart = startTime.replace('h', '');
                const examEnd = endTime.replace('h', '');

                // Check if the exam time falls within this slot
                if (availableStart.includes(examStart) || availableEnd.includes(examEnd)) {
                    cell = document.querySelector(`td[data-day="${day}"][data-time="${availableSlot}"]`);
                    console.log(`Found matching cell with time slot ${availableSlot}`);
                    break;
                }
            }
        }

        if (cell) {
            console.log(`Cell found for day=${day}, time=${timeSlot}`);

            // Remove the empty-cell class
            cell.classList.remove('empty-cell');
            cell.className = 'session-cell';

            // Clear any existing content
            cell.innerHTML = '';

            // Create and append the class card
            const classCard = createClassCard(examData);
            cell.appendChild(classCard);

            console.log('Exam card added to cell');
        } else {
            console.warn(`Cell not found for day ${day} and time ${timeSlot}`);
            console.warn('Exam data that could not be placed:', examData);

            // Debug: Log all available cells
            console.log('Available cells:');
            allCells.forEach(c => {
                console.log(`Cell: day=${c.dataset.day}, time=${c.dataset.time}`);
            });
        }
    });
}

// Function to calculate and display dates for the current week
function displayDatesForWeek(examDates = null) {
    console.log('Displaying dates for week with exam dates:', examDates);

    // Get the current date
    const today = new Date();

    // Find the Monday of the current week
    const dayOfWeek = today.getDay(); // 0 is Sunday, 1 is Monday, etc.
    const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // If today is Sunday, go back 6 days, otherwise find the most recent Monday

    const monday = new Date(today);
    monday.setDate(today.getDate() + mondayOffset);

    // Create a map to store exam dates by day of week
    const examDatesByDay = {};

    // Map of day names to day numbers
    const dayNameToNumber = {
        'lundi': 1, 'monday': 1,
        'mardi': 2, 'tuesday': 2,
        'mercredi': 3, 'wednesday': 3,
        'jeudi': 4, 'thursday': 4,
        'vendredi': 5, 'friday': 5,
        'samedi': 6, 'saturday': 6,
        'dimanche': 7, 'sunday': 7
    };

    // If we have exam dates, process them
    if (examDates && examDates.length > 0) {
        console.log('Processing exam dates for display');

        // Group exam dates by day of week
        examDates.forEach(exam => {
            if (exam.date_examen || exam.date_exam) {
                try {
                    // Get the day number from the day name if available
                    let dayNumber;

                    if (exam.day && typeof exam.day === 'string') {
                        const dayLower = exam.day.toLowerCase();
                        dayNumber = dayNameToNumber[dayLower];
                        console.log(`Converted day name "${exam.day}" to number: ${dayNumber}`);
                    } else if (exam.day && !isNaN(exam.day)) {
                        dayNumber = parseInt(exam.day);
                    } else {
                        // Try to use calculated_day from SQL if available
                        if (exam.calculated_day) {
                            const calculatedDay = parseInt(exam.calculated_day);
                            // DAYOFWEEK() returns 1 for Sunday, 2 for Monday, etc.
                            // We need to convert to 1 for Monday, 2 for Tuesday, etc.
                            dayNumber = (calculatedDay === 1) ? 7 : calculatedDay - 1;
                            console.log(`Using calculated_day from SQL for date display: ${exam.calculated_day} -> adjusted to: ${dayNumber}`);
                        } else {
                            // Fallback to calculating from date
                            const examDate = new Date(exam.date_examen || exam.date_exam);
                            const calculatedDayOfWeek = examDate.getDay(); // 0 (Sunday) to 6 (Saturday)
                            dayNumber = calculatedDayOfWeek === 0 ? 7 : calculatedDayOfWeek; // Convert Sunday (0) to 7
                        }
                    }

                    // Store the formatted date
                    const examDate = exam.date_examen || exam.date_exam;
                    if (dayNumber && (!examDatesByDay[dayNumber] || new Date(examDatesByDay[dayNumber].date) > new Date(examDate))) {
                        examDatesByDay[dayNumber] = {
                            date: examDate,
                            formatted: exam.formatted_date || new Date(examDate).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                            })
                        };
                    }
                } catch (e) {
                    console.error(`Error processing exam date (${exam.date_exam}):`, e);
                }
            }
        });

        console.log('Exam dates by day:', examDatesByDay);
    }

    // Format and display dates for each day
    const dateElements = document.querySelectorAll('.day-date');
    dateElements.forEach(element => {
        const dayNumber = parseInt(element.getAttribute('data-day'));

        // If we have an exam date for this day, use it
        if (examDatesByDay[dayNumber]) {
            element.textContent = examDatesByDay[dayNumber].formatted;
            element.setAttribute('data-full-date', examDatesByDay[dayNumber].date);
            console.log(`Set day ${dayNumber} date to exam date: ${examDatesByDay[dayNumber].formatted}`);
        } else {
            // Otherwise use the current week's date
            const date = new Date(monday);
            date.setDate(monday.getDate() + (dayNumber - 1)); // -1 because Monday is already day 1

            // Format the date (e.g., "May 15")
            const formattedDate = date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric'
            });

            // Set the date text
            element.textContent = formattedDate;

            // Store the full date as a data attribute for reference
            element.setAttribute('data-full-date', date.toISOString().split('T')[0]);
        }
    });
}

// Function to load timetable data from the server
async function loadTimetableData(forceRefresh = false) {
    try {
        console.log('Loading exam schedule data, forceRefresh =', forceRefresh);

        // Clear any existing error messages
        hideErrorMessage();

        // Always clear the timetable first to ensure a fresh start
        clearTimetable();

        // Get the URL parameters
        const params = getUrlParams();
        console.log('URL parameters:', params);

        // Set the hidden fields
        const filiereIdField = document.getElementById('filiere_id');
        const niveauIdField = document.getElementById('niveau_id');
        const groupeIdField = document.getElementById('groupe_id');
        const semestreField = document.getElementById('semestre');

        if (filiereIdField) filiereIdField.value = params.filiere || '';
        if (niveauIdField) niveauIdField.value = params.niveau || '';
        if (groupeIdField) groupeIdField.value = params.groupe || '';
        if (semestreField) semestreField.value = params.semestre || '';

        // Display the applied filters
        displayAppliedFilters(params);

        // Build the query string for the API request
        const queryParams = new URLSearchParams();

        if (params.niveau) {
            queryParams.append('niveau', params.niveau);
            console.log('Added niveau filter:', params.niveau);
        }

        if (params.semestre) {
            queryParams.append('semestre', params.semestre);
            console.log('Added semestre filter:', params.semestre);
        }

        // Only add filiere if it's not 'none'
        if (params.filiere && params.filiere !== 'none') {
            queryParams.append('filiere', params.filiere);
            console.log('Added filiere filter:', params.filiere);
        }

        // Only add groupe if it's not 'all'
        if (params.groupe && params.groupe !== 'all') {
            queryParams.append('groupe', params.groupe);
            console.log('Added groupe filter:', params.groupe);
        }

        if (params.semaine) {
            queryParams.append('semaine', params.semaine);
            console.log('Added semaine filter:', params.semaine);
        }

        // We'll use the exams table instead of filtering by type

        // Add a cache-busting parameter with a random component
        queryParams.append('_', Date.now() + Math.random().toString(36).substring(2, 15));

        // Make the API request with cache-busting headers
        const url = `../../route/examRoute.php?${queryParams.toString()}`;
        console.log('Fetching exam schedule data from:', url);

        const response = await fetch(url, {
            method: 'GET',
            cache: 'no-store',  // Strongest cache prevention
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
                'X-Requested-With': 'XMLHttpRequest'  // Helps prevent caching in some servers
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        let data;
        try {
            const text = await response.text();
            console.log('Raw response text:', text.substring(0, 200) + '...'); // Log first 200 chars

            try {
                data = JSON.parse(text);
                console.log('Received data:', data);
            } catch (jsonError) {
                console.error('Error parsing JSON response:', jsonError);
                console.error('Raw response:', text);
                throw new Error('Invalid JSON response from server. Check the server logs for details.');
            }
        } catch (textError) {
            console.error('Error reading response text:', textError);
            throw new Error('Failed to read server response');
        }

        // Hide class title - we don't want to display it
        const classTitle = document.getElementById('class-title');
        if (classTitle) {
            classTitle.textContent = '';
            classTitle.classList.add('d-none');
        }

        // Process the data and update the timetable
        if (data.data && data.data.length > 0) {
            console.log(`Found ${data.data.length} exams to display`);

            // Debug: Log the first exam to see its structure
            if (data.data.length > 0) {
                console.log('Sample exam data:', data.data[0]);
            }

            // Display dates for the week based on exam dates
            displayDatesForWeek(data.data);

            // Process the data to ensure it has the required fields
            const processedData = data.data.map(exam => {
                // Make sure we have the necessary fields
                const processedExam = { ...exam };

                // Ensure we have a day field
                if (!processedExam.day) {
                    // Try to use calculated_day from SQL if available
                    if (processedExam.calculated_day) {
                        const calculatedDay = parseInt(processedExam.calculated_day);
                        // DAYOFWEEK() returns 1 for Sunday, 2 for Monday, etc.
                        // We need to convert to 1 for Monday, 2 for Tuesday, etc.
                        processedExam.day = (calculatedDay === 1) ? 7 : calculatedDay - 1;
                        console.log(`Using calculated_day from SQL for processed data: ${processedExam.calculated_day} -> adjusted to: ${processedExam.day}`);
                    }
                    // If still no day but we have date_examen, calculate from date
                    else if (processedExam.date_examen) {
                        try {
                            const examDate = new Date(processedExam.date_examen);
                            const dayOfWeek = examDate.getDay(); // 0 (Sunday) to 6 (Saturday)
                            processedExam.day = dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday (0) to 7
                            console.log(`Calculated day from date_examen for processed data (${processedExam.date_examen}): ${processedExam.day}`);
                        } catch (e) {
                            console.error('Error processing date:', e);
                        }
                    }
                    // If still no day but we have date_exam (for backward compatibility), calculate from date
                    else if (processedExam.date_exam) {
                        try {
                            const examDate = new Date(processedExam.date_exam);
                            const dayOfWeek = examDate.getDay(); // 0 (Sunday) to 6 (Saturday)
                            processedExam.day = dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday (0) to 7
                            console.log(`Calculated day from date_exam for processed data (${processedExam.date_exam}): ${processedExam.day}`);
                        } catch (e) {
                            console.error('Error processing date:', e);
                        }
                    }
                }

                // Normalize time formats
                if (processedExam.heure_debut) {
                    processedExam.heure_debut = processedExam.heure_debut.replace(/^(\d+):00:00$/, '$1h');
                    if (!processedExam.heure_debut.endsWith('h')) {
                        processedExam.heure_debut = processedExam.heure_debut.replace(/^(\d+).*$/, '$1h');
                    }
                }

                if (processedExam.heure_fin) {
                    processedExam.heure_fin = processedExam.heure_fin.replace(/^(\d+):00:00$/, '$1h');
                    if (!processedExam.heure_fin.endsWith('h')) {
                        processedExam.heure_fin = processedExam.heure_fin.replace(/^(\d+).*$/, '$1h');
                    }
                }

                return processedExam;
            });

            updateTimetable(processedData);
            document.getElementById('no-classes-message').classList.add('d-none');
        } else {
            console.log('No exams found for the current filters');
            // Show the no classes message
            document.getElementById('no-classes-message').classList.remove('d-none');
        }
    } catch (error) {
        console.error('Error loading exam schedule data:', error);
        showErrorMessage(error.message || 'Unknown error');

        // Show the no classes message
        document.getElementById('no-classes-message').classList.remove('d-none');
    }
}

// Function to open the add session modal
function openAddSessionModal(day, timeSlot, seanceId = '') {
    try {
        console.log('Opening add session modal with day:', day, 'time:', timeSlot, 'seanceId:', seanceId);

        // Reset form fields first
        const form = document.getElementById('addSessionForm');
        if (form) {
            form.reset();
        }

        // Set the hidden fields
        document.getElementById('seance_id').value = seanceId || '';
        document.getElementById('selected_day').value = day || '';
        document.getElementById('selected_time').value = timeSlot || '';

        // Set the display fields
        const dayDisplay = document.getElementById('day_display');
        const timeDisplay = document.getElementById('time_display');

        if (dayDisplay) {
            if (day) {
                // Convert numeric day to name if needed
                const dayNames = {
                    '1': 'Monday', '2': 'Tuesday', '3': 'Wednesday',
                    '4': 'Thursday', '5': 'Friday', '6': 'Saturday'
                };
                dayDisplay.value = dayNames[day] || day;

                // Set a default date for the selected day
                const dateExamField = document.getElementById('date_exam');
                if (dateExamField && !dateExamField.value) {
                    // Find the next occurrence of this day
                    const today = new Date();
                    const dayOfWeek = parseInt(day);
                    const todayDayOfWeek = today.getDay() || 7; // Convert Sunday (0) to 7

                    // Calculate days to add to get to the selected day
                    let daysToAdd = dayOfWeek - todayDayOfWeek;
                    if (daysToAdd <= 0) {
                        daysToAdd += 7; // Go to next week if day has passed
                    }

                    const nextDate = new Date(today);
                    nextDate.setDate(today.getDate() + daysToAdd);

                    // Format date as YYYY-MM-DD for the input field
                    const formattedDate = nextDate.toISOString().split('T')[0];
                    dateExamField.value = formattedDate;
                }
            } else {
                dayDisplay.value = '';
            }
        }

        if (timeDisplay) {
            timeDisplay.value = timeSlot || '';
        }

        // Show the modal first so the user sees something is happening
        const modalElement = document.getElementById('addSessionModal');
        if (!modalElement) {
            console.error('Modal element not found');
            return;
        }

        console.log('Showing modal...');
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Add a loading indicator to the modal
        const modalBody = modalElement.querySelector('.modal-body');
        if (modalBody) {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'loading-indicator';
            loadingIndicator.className = 'text-center my-3';
            loadingIndicator.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading data from database...</p>
            `;
            modalBody.prepend(loadingIndicator);
        }

        // Load dropdown options
        console.log('Loading dropdown options...');
        Promise.all([
            loadModules(),
            loadEnseignants(),
            loadSalles(),
            loadTypeSeances()
        ]).then(() => {
            console.log('All dropdown options loaded successfully');

            // Remove loading indicator
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }

            // If we have a seanceId, load the session data
            if (seanceId) {
                loadSessionData(seanceId);
            }
        }).catch(error => {
            console.error('Error loading dropdown options:', error);

            // Remove loading indicator and show error
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Error loading data: ${error.message}
                    </div>
                `;
            }
        });

        console.log('Modal shown');
    } catch (error) {
        console.error('Error opening add session modal:', error);
        alert('Error opening form: ' + error.message);
    }
}

// Function to load modules for the dropdown
async function loadModules() {
    try {
        const response = await fetch('../../route/moduleRoute.php');
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const responseData = await response.json();
        console.log('Modules loaded:', responseData);

        // Extract the data array from the response
        // Handle both formats: direct array or {data: [...]}
        const modules = responseData.data || responseData;

        if (!Array.isArray(modules)) {
            throw new Error('Invalid module data format received from server');
        }

        const moduleSelect = document.getElementById('module');
        if (!moduleSelect) {
            console.error('Module select element not found');
            return;
        }

        // Clear existing options except the first one
        while (moduleSelect.options.length > 1) {
            moduleSelect.remove(1);
        }

        // Add new options
        modules.forEach(module => {
            const option = document.createElement('option');
            option.value = module.id_module;
            option.textContent = module.nom_module;
            moduleSelect.appendChild(option);
        });

        console.log(`Added ${modules.length} module options to select`);
    } catch (error) {
        console.error('Error loading modules:', error);
    }
}

// Function to load enseignants for the dropdown
async function loadEnseignants() {
    try {
        const response = await fetch('../../route/enseignantRoute.php');
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const responseData = await response.json();
        console.log('Enseignants loaded:', responseData);

        // Extract the data array from the response
        // Handle both formats: direct array or {data: [...]}
        const enseignants = responseData.data || responseData;

        if (!Array.isArray(enseignants)) {
            throw new Error('Invalid enseignant data format received from server');
        }

        const enseignantSelect = document.getElementById('enseignant');
        if (!enseignantSelect) {
            console.error('Enseignant select element not found');
            return;
        }

        // Clear existing options except the first one
        while (enseignantSelect.options.length > 1) {
            enseignantSelect.remove(1);
        }

        // Add new options
        enseignants.forEach(enseignant => {
            const option = document.createElement('option');
            option.value = enseignant.id_enseignant;

            // Handle different name formats
            let displayName = enseignant.nom_enseignant;
            if (!displayName && enseignant.nom && enseignant.prenom) {
                displayName = `${enseignant.nom} ${enseignant.prenom}`;
            }

            option.textContent = displayName;
            enseignantSelect.appendChild(option);
        });

        console.log(`Added ${enseignants.length} enseignant options to select`);
    } catch (error) {
        console.error('Error loading enseignants:', error);
    }
}

// Function to load salles for the dropdown
async function loadSalles() {
    try {
        const response = await fetch('../../route/salleRoute.php');
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const responseData = await response.json();
        console.log('Salles loaded:', responseData);

        // Extract the data array from the response
        // Handle both formats: direct array or {data: [...]}
        const salles = responseData.data || responseData;

        if (!Array.isArray(salles)) {
            throw new Error('Invalid salle data format received from server');
        }

        const salleSelect = document.getElementById('salle');
        if (!salleSelect) {
            console.error('Salle select element not found');
            return;
        }

        // Clear existing options except the first one
        while (salleSelect.options.length > 1) {
            salleSelect.remove(1);
        }

        // Add new options
        salles.forEach(salle => {
            const option = document.createElement('option');
            option.value = salle.id_salle;
            option.textContent = salle.nom_salle;
            salleSelect.appendChild(option);
        });

        console.log(`Added ${salles.length} salle options to select`);
    } catch (error) {
        console.error('Error loading salles:', error);
    }
}

// Function to load type seances for the dropdown
async function loadTypeSeances() {
    try {
        const response = await fetch('../../route/typeSeanceRoute.php');
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const responseData = await response.json();
        console.log('Type seances loaded:', responseData);

        // Extract the data array from the response
        const data = responseData.data || [];

        const typeSeanceSelect = document.getElementById('type_seance');
        if (!typeSeanceSelect) {
            console.error('Type seance select element not found');
            return;
        }

        // Clear existing options except the first one
        while (typeSeanceSelect.options.length > 1) {
            typeSeanceSelect.remove(1);
        }

        // Define exam-related types to filter from the database
        const examKeywords = ['exam', 'examen', 'test', 'quiz', 'ds', 'controle', 'contrôle', 'rattrapage', 'final', 'midterm'];

        // Filter exam-related types from the database
        const examTypesFromDB = data.filter(type => {
            const typeName = type.nom_type.toLowerCase();
            return examKeywords.some(keyword => typeName.includes(keyword));
        });

        console.log('Exam types from database:', examTypesFromDB);

        // If we have exam types from the database, use those
        if (examTypesFromDB.length > 0) {
            examTypesFromDB.forEach(type => {
                const option = document.createElement('option');
                option.value = type.nom_type;
                option.textContent = type.nom_type;
                typeSeanceSelect.appendChild(option);
            });
        } else {
            // Fallback to predefined exam types if none found in database
            const defaultExamTypes = [
                'Final Exam',
                'Midterm Exam',
                'Quiz',
                'Test',
                'Practical Exam',
                'Examen Final',
                'Contrôle Continu',
                'Rattrapage'
            ];

            defaultExamTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = type;
                typeSeanceSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading type seances:', error);

        // Fallback to predefined exam types in case of error
        const typeSeanceSelect = document.getElementById('type_seance');
        if (typeSeanceSelect) {
            const defaultExamTypes = [
                'Final Exam',
                'Midterm Exam',
                'Quiz',
                'Test',
                'Practical Exam',
                'Examen Final',
                'Contrôle Continu',
                'Rattrapage'
            ];

            defaultExamTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = type;
                typeSeanceSelect.appendChild(option);
            });
        }
    }
}

// Function to load session data for editing
async function loadSessionData(seanceId) {
    try {
        console.log('Loading exam data for examId:', seanceId);

        const response = await fetch(`../../route/examRoute.php?id=${seanceId}`);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Exam data loaded:', data);

        if (!data || !data.data) {
            throw new Error('No exam data found');
        }

        const session = data.data;

        // Wait for all dropdowns to be loaded before setting values
        await Promise.all([
            loadModules(),
            loadEnseignants(),
            loadSalles(),
            loadTypeSeances()
        ]);

        // Set the form values
        const moduleSelect = document.getElementById('module');
        if (moduleSelect) moduleSelect.value = session.id_module || '';

        const enseignantSelect = document.getElementById('enseignant');
        if (enseignantSelect) enseignantSelect.value = session.id_enseignant || '';

        const salleSelect = document.getElementById('salle');
        if (salleSelect) salleSelect.value = session.id_salle || '';

        // Handle the type field - this might need special handling
        const typeSeanceSelect = document.getElementById('type_seance');
        if (typeSeanceSelect) {
            const typeValue = session.type || session.type_exam || '';

            // Try to find the option with the exact value
            let typeOption = Array.from(typeSeanceSelect.options).find(option =>
                option.value.toLowerCase() === typeValue.toLowerCase()
            );

            // If not found, try to find an option that contains the value
            if (!typeOption) {
                typeOption = Array.from(typeSeanceSelect.options).find(option =>
                    option.value.toLowerCase().includes(typeValue.toLowerCase()) ||
                    typeValue.toLowerCase().includes(option.value.toLowerCase())
                );
            }

            // If we found a matching option, select it
            if (typeOption) {
                typeSeanceSelect.value = typeOption.value;
                console.log(`Selected type option: ${typeOption.value}`);
            } else if (typeValue) {
                // If no matching option but we have a value, add it as a new option
                console.log(`No matching option found for type: ${typeValue}, adding it`);
                const newOption = document.createElement('option');
                newOption.value = typeValue;
                newOption.textContent = typeValue;
                typeSeanceSelect.appendChild(newOption);
                typeSeanceSelect.value = typeValue;
            }
        }

        // Set the day and time display
        const dayNames = {
            '1': 'Monday', '2': 'Tuesday', '3': 'Wednesday',
            '4': 'Thursday', '5': 'Friday', '6': 'Saturday'
        };

        // For exams, use the day from the database if available
        if (session.day) {
            // If day is a string (like "Lundi"), use it directly
            if (typeof session.day === 'string') {
                document.getElementById('day_display').value = session.day;

                // Convert day name to number for internal use
                const dayMap = {
                    'lundi': 1, 'monday': 1,
                    'mardi': 2, 'tuesday': 2,
                    'mercredi': 3, 'wednesday': 3,
                    'jeudi': 4, 'thursday': 4,
                    'vendredi': 5, 'friday': 5,
                    'samedi': 6, 'saturday': 6,
                    'dimanche': 7, 'sunday': 7
                };
                const dayLower = session.day.toLowerCase();
                if (dayMap[dayLower]) {
                    document.getElementById('selected_day').value = dayMap[dayLower];
                }
            }
            // If day is a number, convert to name for display
            else if (!isNaN(session.day)) {
                const dayNumber = parseInt(session.day);
                document.getElementById('day_display').value = dayNames[dayNumber] || '';
                document.getElementById('selected_day').value = dayNumber;
            }
        }

        // Set the date_exam field
        const dateExamField = document.getElementById('date_exam');
        if (dateExamField) {
            if (session.date_examen) {
                dateExamField.value = session.date_examen;
            } else if (session.date_exam) {
                dateExamField.value = session.date_exam;
            }
        }

        // Set the time display
        const timeDisplayField = document.getElementById('time_display');
        if (timeDisplayField) {
            timeDisplayField.value = `${session.heure_debut}-${session.heure_fin}` || '';
        }

        // Set the hidden fields if not already set
        const selectedDayField = document.getElementById('selected_day');
        if (selectedDayField && !selectedDayField.value) {
            selectedDayField.value = session.day || '';
        }

        const selectedTimeField = document.getElementById('selected_time');
        if (selectedTimeField) {
            selectedTimeField.value = `${session.heure_debut}-${session.heure_fin}` || '';
        }

        const seanceIdField = document.getElementById('seance_id');
        if (seanceIdField) {
            seanceIdField.value = session.id_examen || session.id_exam || '';
        }
    } catch (error) {
        console.error('Error loading session data:', error);
        alert('Error loading session data: ' + error.message);
    }
}

// Function to confirm deletion of an exam
function confirmDeleteExam(examId) {
    console.log('Confirming deletion of exam with ID:', examId);

    if (!examId) {
        console.error('No exam ID provided for deletion');
        alert('Error: No exam ID provided for deletion');
        return;
    }

    // Create a confirmation dialog
    const confirmDialog = document.createElement('div');
    confirmDialog.className = 'modal fade';
    confirmDialog.id = 'confirmDeleteModal';
    confirmDialog.setAttribute('tabindex', '-1');
    confirmDialog.setAttribute('aria-labelledby', 'confirmDeleteModalLabel');
    confirmDialog.setAttribute('aria-hidden', 'true');

    confirmDialog.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="confirmDeleteModalLabel">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this exam? This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
                </div>
            </div>
        </div>
    `;

    // Add the dialog to the document
    document.body.appendChild(confirmDialog);

    // Initialize the modal
    const modal = new bootstrap.Modal(confirmDialog);
    modal.show();

    // Add event listener to the confirm button
    document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
        // Close the modal
        modal.hide();

        // Delete the exam
        deleteExam(examId);

        // Remove the modal from the DOM after it's hidden
        confirmDialog.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(confirmDialog);
        });
    });

    // Remove the modal from the DOM when it's closed
    confirmDialog.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(confirmDialog);
    });
}

// Function to delete an exam
async function deleteExam(examId) {
    console.log('Deleting exam with ID:', examId);

    try {
        // Show a loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.id = 'delete-loading-indicator';
        loadingIndicator.className = 'position-fixed top-50 start-50 translate-middle bg-white p-4 rounded shadow';
        loadingIndicator.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="spinner-border text-primary me-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div>Deleting exam...</div>
            </div>
        `;
        document.body.appendChild(loadingIndicator);

        // Make the API request
        const response = await fetch(`../../route/examRoute.php?id=${examId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        // Remove the loading indicator
        document.body.removeChild(loadingIndicator);

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
        }

        const responseData = await response.json();
        console.log('Delete response:', responseData);

        // Reload the timetable data
        loadTimetableData(true);

        // Show success message
        showSuccessMessage('Exam deleted successfully');
    } catch (error) {
        console.error('Error deleting exam:', error);

        // Remove the loading indicator if it exists
        const loadingIndicator = document.getElementById('delete-loading-indicator');
        if (loadingIndicator) {
            document.body.removeChild(loadingIndicator);
        }

        alert(`Error deleting exam: ${error.message}`);
    }
}

// Function to save a session
async function saveSession() {
    try {
        console.log('Saving exam session...');

        // Get form values
        const seanceId = document.getElementById('seance_id').value;
        const moduleId = document.getElementById('module').value;
        const enseignantId = document.getElementById('enseignant').value;
        const salleId = document.getElementById('salle').value;
        const typeSeance = document.getElementById('type_seance').value;
        const day = document.getElementById('selected_day').value;
        const time = document.getElementById('selected_time').value;
        const dateExam = document.getElementById('date_exam').value;

        // Get hidden fields for class info
        const filiereId = document.getElementById('filiere_id').value;
        const niveauId = document.getElementById('niveau_id').value;
        const groupeId = document.getElementById('groupe_id').value;
        const semestre = document.getElementById('semestre').value;

        console.log('Form values:', {
            seanceId, moduleId, enseignantId, salleId, typeSeance, day, time, dateExam,
            filiereId, niveauId, groupeId, semestre
        });

        // Validate required fields
        if (!moduleId || !enseignantId || !salleId || !typeSeance || !day || !time) {
            alert('Please fill in all required fields');
            return;
        }

        // Validate exam date
        if (!dateExam) {
            alert('Please select an exam date');
            return;
        }

        // Parse the time slot
        const [startTime, endTime] = time.split('-');
        console.log('Time slot:', { startTime, endTime });

        // Get the day name from the day number
        const dayNames = {
            '1': 'Lundi',
            '2': 'Mardi',
            '3': 'Mercredi',
            '4': 'Jeudi',
            '5': 'Vendredi',
            '6': 'Samedi',
            '7': 'Dimanche'
        };

        // Get the type display name
        const typeSeanceElement = document.getElementById('type_seance');
        const typeSeanceDisplayName = typeSeanceElement.options[typeSeanceElement.selectedIndex].text;

        // Create the request data
        const requestData = {
            id_module: moduleId,
            id_enseignant: enseignantId,
            id_salle: salleId,
            type: typeSeance,  // Using type for exams
            date_examen: dateExam,    // Date is required for exams
            heure_debut: startTime,
            heure_fin: endTime,
            id_niveau: niveauId,
            semestre: semestre,
            day: dayNames[day] || day  // Use the day name if available
        };

        // Add optional fields if they exist
        if (filiereId && filiereId !== 'none') {
            requestData.id_filiere = filiereId;
        }

        if (groupeId && groupeId !== 'all') {
            requestData.id_groupe = groupeId;
        }

        // Add exam ID if editing an existing exam
        if (seanceId) {
            requestData.id_examen = seanceId;
        }

        console.log('Request data:', requestData);

        // Determine if this is an update or a new session
        const method = seanceId ? 'PUT' : 'POST';
        console.log('Using HTTP method:', method);

        // Make the API request
        const response = await fetch('../../route/examRoute.php', {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
        }

        const responseData = await response.json();
        console.log('Response:', responseData);

        // Close the modal
        const modalElement = document.getElementById('addSessionModal');
        const modal = bootstrap.Modal.getInstance(modalElement);
        modal.hide();

        // Create a new session object for immediate display
        const newSession = {
            id_exam: responseData.id || seanceId,
            id_module: moduleId,
            id_enseignant: enseignantId,
            id_salle: salleId,
            type_exam: typeSeance,
            day: day,
            date_exam: dateExam,
            heure_debut: startTime,
            heure_fin: endTime,
            // Add display names from the select elements
            nom_module: document.getElementById('module').options[document.getElementById('module').selectedIndex].text,
            nom_enseignant: document.getElementById('enseignant').options[document.getElementById('enseignant').selectedIndex].text,
            nom_salle: document.getElementById('salle').options[document.getElementById('salle').selectedIndex].text,
            type: typeSeanceDisplayName // Add the display name for the type
        };

        console.log('New session object:', newSession);

        // Reload the timetable data
        loadTimetableData(true);

        // Show success message
        showSuccessMessage('Exam session ' + (seanceId ? 'updated' : 'added') + ' successfully');
    } catch (error) {
        console.error('Error saving session:', error);
        alert(`Error saving session: ${error.message}`);
    }
}

// Function to set up empty cell click handlers
function setupEmptyCellClickHandlers() {
    // Get all empty cells
    const emptyCells = document.querySelectorAll('td[data-day][data-time]');

    // Add click event to each cell
    emptyCells.forEach(cell => {
        cell.addEventListener('click', () => {
            // Only open the modal if the cell is empty
            if (cell.classList.contains('empty-cell')) {
                const day = cell.dataset.day;
                const timeSlot = cell.dataset.time;
                openAddSessionModal(day, timeSlot);
            }
        });
    });
}

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    console.log('Exam schedule view page loaded');

    // Clear any existing data first
    clearTimetable();

    // Load the timetable data with a fresh request
    loadTimetableData(true);

    // Set up empty cell click handlers
    setupEmptyCellClickHandlers();

    // Set up the add session button
    const addSessionBtn = document.getElementById('add-session-btn');
    if (addSessionBtn) {
        addSessionBtn.addEventListener('click', () => {
            openAddSessionModal('', '');
        });
    }

    // Set up the save session button
    const saveSessionBtn = document.getElementById('saveSessionBtn');
    if (saveSessionBtn) {
        saveSessionBtn.addEventListener('click', saveSession);
    }

    // Set up the download PDF button
    const downloadPdfBtn = document.getElementById('download-pdf-btn');
    if (downloadPdfBtn) {
        downloadPdfBtn.addEventListener('click', () => {
            // This function should be defined in timetable-pdf.js
            if (typeof generatePDF === 'function') {
                generatePDF('Exam Schedule');
            } else {
                console.error('generatePDF function not found');
                alert('PDF generation is not available');
            }
        });
    }
});