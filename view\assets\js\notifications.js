// Global variables and functions
// Fonction pour récupérer dynamiquement le chemin de base
function getBasePath(routeFile = 'notificationsRoute.php') {
    // Obtenir le chemin de base à partir de l'URL actuelle
    const currentPath = window.location.pathname;
    // Trouver l'index du dossier "view" ou "admin"
    const viewIndex = currentPath.indexOf('/view/');
    const adminIndex = currentPath.indexOf('/admin/');

    // Déterminer le chemin de base en fonction de la structure de l'URL
    let basePath = '';
    if (viewIndex !== -1) {
        // Si nous sommes dans un sous-dossier de "view"
        basePath = currentPath.substring(0, viewIndex);
    } else if (adminIndex !== -1) {
        // Si nous sommes dans un sous-dossier de "admin"
        basePath = currentPath.substring(0, adminIndex);
    }

    return `${basePath}/route/${routeFile}`;
}

const basePath = getBasePath('notificationsRoute.php');
const adminId = null;



// Make createNewNotification function globally accessible
function createNewNotification() {
    console.log('createNewNotification function called');

    // Get the send button
    const sendButton = document.getElementById('sendNotificationBtn');

    // Check if the button is already disabled (to prevent duplicate submissions)
    if (sendButton && sendButton.disabled) {
        console.log('Send button is already disabled, preventing duplicate submission');
        return;
    }

    // Get form values
    const title = document.getElementById('notificationTitle').value.trim();
    const type = document.getElementById('notificationType').value.trim();
    const content = document.getElementById('notificationContent').value.trim();
    const mediaUrl = document.getElementById('mediaUrl').value.trim();

    console.log('Form values:', { title, type, content, mediaUrl });

    // Validate form
    if (!title || !content) {
        showErrorModal("Veuillez remplir tous les champs obligatoires");
        return;
    }

    // Disable the send button and show loading state
    if (sendButton) {
        sendButton.disabled = true;
        sendButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';
    }

    // Check if we have a file to upload
    if (window.selectedFile) {
        // Upload the file first
        const formData = new FormData();
        formData.append('file', window.selectedFile);

        console.log('Uploading file:', window.selectedFile.name);

        fetch('../../route/fileUploadRoute.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Upload response status:', response.status);

            // Même si la réponse n'est pas OK, on essaie de récupérer le texte
            return response.text().then(text => {
                console.log('Upload response text:', text);

                // Essayer de parser le texte en JSON
                try {
                    const data = JSON.parse(text);
                    // Si la réponse n'est pas OK, on lance une erreur avec le message d'erreur
                    if (!response.ok) {
                        throw new Error(data.error || 'Erreur serveur: ' + response.status);
                    }
                    return data;
                } catch (e) {
                    console.error('Error parsing JSON:', e);
                    // Si on ne peut pas parser le JSON, on affiche le texte brut
                    if (!response.ok) {
                        throw new Error('Erreur serveur: ' + response.status + ' - ' + text.substring(0, 100));
                    } else {
                        throw new Error('Erreur de parsing JSON: ' + text.substring(0, 100));
                    }
                }
            });
        })
        .then(data => {
            if (data.success) {
                console.log('File uploaded successfully:', data.file_path);
                // Now send the notification with the file path
                sendNotificationWithFilePath(title, type, content, mediaUrl, data.file_path);
            } else {
                throw new Error('Failed to upload file: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error uploading file:', error);
            showErrorModal("Erreur lors du téléchargement du fichier: " + error.message);

            // Re-enable the send button
            if (sendButton) {
                sendButton.disabled = false;
                sendButton.textContent = "Send Notification";
            }
        });
    } else {
        // No file to upload, send the notification directly
        sendNotificationWithFilePath(title, type, content, mediaUrl, null);
    }
}

function sendNotificationWithFilePath(title, type, content, mediaUrl, filePath) {
    const sendButton = document.getElementById('sendNotificationBtn');

    // Log pour le débogage
    console.log("Sending notification with file path:", {
        title,
        type,
        content,
        mediaUrl,
        filePath
    });

    // Send the request to create a new notification
    fetch(`${basePath}?action=create`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
            title: title,
            message: content,
            type: type,
            media_url: mediaUrl || null,
            file_path: filePath || null
        })
    })
    .then(response => response.text())
    .then(text => {
        console.log('Response text:', text);
        try {
            const data = JSON.parse(text);
            if (data.success) {
                // Hide the create notification modal
                const createModalElement = document.getElementById('createNotificationModal');
                const createModal = bootstrap.Modal.getInstance(createModalElement);
                if (createModal) {
                    createModal.hide();

                    // Supprimez tous les backdrops après la fermeture
                    setTimeout(() => {
                        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                            backdrop.remove();
                        });

                        // Réinitialisez le style du body
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    }, 300);
                }

                // Reset form
                document.getElementById('createNotificationForm').reset();

                // Reset hidden fields
                document.getElementById('mediaUrl').value = '';
                document.getElementById('filePath').value = '';

                // Reset selected file
                window.selectedFile = null;

                // Remove attachment indicators
                const indicators = document.querySelectorAll('.message-attachment-indicator');
                indicators.forEach(indicator => indicator.remove());

                // Set success message
                document.getElementById('successMessage').textContent = "Notification envoyée avec succès!";

                // Show success modal
                const successModalElement = document.getElementById('successModal');
                if (successModalElement) {
                    // Utiliser notre fonction utilitaire pour afficher le modal proprement
                    showCleanModal(successModalElement, 3000);
                }

                // Reload notifications
                loadNotifications(currentPage);
            } else {
                // Show error in modal
                showErrorModal("Échec de l'envoi de la notification: " + (data.error || "Erreur inconnue"));
            }
        } catch (e) {
            // Show parsing error in modal
            showErrorModal("Erreur de traitement de la réponse: " + e.message);
        }
    })
    .catch(error => {
        // Show network error in modal
        showErrorModal("Erreur d'envoi de la notification: " + error.message);
    })
    .finally(() => {
        // Re-enable the send button
        if (sendButton) {
            sendButton.disabled = false;
            sendButton.textContent = "Send Notification";
        }
    });
}

// Fonction utilitaire pour afficher un modal de manière propre
function showCleanModal(modalElement, autoHideDelay = 0) {
    if (!modalElement) return;

    // Assurez-vous qu'aucun modal n'est déjà ouvert
    const existingModal = bootstrap.Modal.getInstance(modalElement);
    if (existingModal) {
        existingModal.dispose();
    }

    // Supprimez tous les backdrops existants
    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
        backdrop.remove();
    });

    // Réinitialisez le style du body
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // Créez et affichez le nouveau modal
    const modal = new bootstrap.Modal(modalElement, {
        backdrop: 'static',
        keyboard: false
    });

    // Ajoutez un événement pour nettoyer après la fermeture
    modalElement.addEventListener('hidden.bs.modal', function () {
        // Supprimez tous les backdrops
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });

        // Réinitialisez le style du body
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }, { once: true });

    modal.show();

    // Auto-hide si un délai est spécifié
    if (autoHideDelay > 0) {
        setTimeout(() => {
            modal.hide();

            // Supprimez tous les backdrops après la fermeture
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.remove();
            });

            // Réinitialisez le style du body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }, autoHideDelay);
    }

    return modal;
}

// Fonction pour afficher un message d'erreur dans le modal
function showErrorModal(errorMessage) {
    // Configurer le modal pour afficher une erreur
    document.getElementById('successModalLabel').innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>Erreur';
    document.getElementById('successModalLabel').parentNode.classList.remove('bg-success');
    document.getElementById('successModalLabel').parentNode.classList.add('bg-danger');
    document.getElementById('successMessage').textContent = errorMessage;
    document.querySelector('#successModal .modal-body i').classList.remove('fa-check-circle', 'text-success');
    document.querySelector('#successModal .modal-body i').classList.add('fa-exclamation-circle', 'text-danger');
    document.querySelector('#successModal .modal-footer button').classList.remove('btn-success');
    document.querySelector('#successModal .modal-footer button').classList.add('btn-danger');

    // Afficher le modal
    showCleanModal(document.getElementById('successModal'));
}

// Fonction pour afficher un modal de confirmation
function showConfirmationModal(message, callback) {
    // Configurer le message de confirmation
    document.getElementById('confirmationMessage').textContent = message;

    // Configurer le bouton de confirmation
    const confirmBtn = document.getElementById('confirmActionBtn');

    // Supprimer les anciens écouteurs d'événements
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

    // Ajouter un nouvel écouteur d'événement
    newConfirmBtn.addEventListener('click', function() {
        // Fermer le modal
        const confirmationModalElement = document.getElementById('confirmationModal');
        const confirmationModal = bootstrap.Modal.getInstance(confirmationModalElement);
        if (confirmationModal) {
            confirmationModal.hide();
        }

        // Exécuter le callback
        if (typeof callback === 'function') {
            callback();
        }
    });

    // Afficher le modal proprement
    showCleanModal(document.getElementById('confirmationModal'));
}

// Variable globale pour la page courante
let currentPage = 1;

// Fonction pour obtenir l'icône correspondant au type de notification
function getNotificationIcon(type) {
    const icons = {
        system: 'fa-gear',
        update: 'fa-rotate-right',
        message: 'fa-envelope',
        assignment: 'fa-clipboard-list',
        meeting: 'fa-calendar-check',
        event: 'fa-calendar-day',
        default: 'fa-bell'
    };
    return icons[type] || icons.default;
}

// Fonction globale pour charger les notifications
function loadNotifications(page = 1) {
    currentPage = page;
    console.log(`Fetching notifications from: ${basePath}?action=getAll&page=${page}`);
    fetch(`${basePath}?action=getAll`)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                return response.text().then(text => {
                    console.error('Error response text:', text);
                    throw new Error(`Server responded with status ${response.status}: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            const container = document.getElementById("notificationsList");
            if (!container) return;

            if (!data || data.length === 0) {
                container.innerHTML = "<p class='empty-state'>No notifications found.</p>";
                return;
            }

            let notificationsHTML = '';
            data.forEach(notification => {
                // Carte compacte
                notificationsHTML += `
                    <div class="me-card compact ${notification.is_read ? 'read' : ''}" data-notification-id="${notification.id}">
                        <div class="notification-icon">
                            <i class="fas ${getNotificationIcon(notification.type)}"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title-container">
                                <h3 class="notification-title">${notification.title}</h3>
                            </div>
                            <span class="notification-time">
                                ${new Date(notification.created_at).toLocaleString()}
                            </span>
                        </div>
                    </div>`;
            });

            // Ajouter le HTML pour le modal de détails
            notificationsHTML += `
                <div class="modal fade" id="notificationDetailModal" tabindex="-1" aria-labelledby="notificationDetailModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="notificationDetailModalLabel">Notification Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="notificationDetailContent">
                                <!-- Le contenu sera injecté dynamiquement -->
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>`;

            container.innerHTML = notificationsHTML;
            addEventListeners();
        })
        .catch(err => {
            console.error("Error loading notifications:", err);
            const container = document.getElementById("notificationsList");
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Error loading notifications</h4>
                        <p>${err.message}</p>
                        <p>Please check the console for more details or try again later.</p>
                    </div>
                `;
            }
        });
}

// Fonction pour afficher les détails d'une notification dans le modal
function showNotificationDetails(notificationId) {
    console.log('Showing details for notification:', notificationId);

    // Trouver la notification dans les données actuelles
    fetch(`${basePath}?action=getAll`)
        .then(response => response.json())
        .then(data => {
            const notification = data.find(n => n.id == notificationId);
            if (!notification) {
                console.error('Notification not found:', notificationId);
                return;
            }

            // Construire le contenu HTML pour le modal
            let detailsHTML = `
                <div class="message-detail-header">
                    <h3>${notification.title}</h3>
                    <div class="message-meta">
                        <span class="message-date">${new Date(notification.created_at).toLocaleString()}</span>
                        <span class="message-type">Type: ${notification.type}</span>
                    </div>
                </div>
                <div class="message-detail-content">
                    <p>${notification.message}</p>
                </div>`;

            // Ajouter les liens pour les pièces jointes
            if (notification.media_url || notification.file_path) {
                detailsHTML += `<div class="message-attachments">`;

                if (notification.media_url) {
                    detailsHTML += `
                        <a href="${notification.media_url}" target="_blank" rel="noopener noreferrer"
                            class="action-link visit-link-btn" onclick="window.open('${notification.media_url}', '_blank')">
                            Visit Link</a>`;
                }

                if (notification.file_path) {
                    detailsHTML += `
                        <a href="../../route/downloadFileRoute.php?file=${encodeURIComponent(notification.file_path)}"
                            class="action-link download-file-btn">
                            Download File</a>`;
                }

                detailsHTML += `</div>`;
            }

            // Ajouter les boutons d'action
            detailsHTML += `<div class="message-actions mt-4">`;

            // Le bouton "Mark as read" ne sera pas affiché car la notification sera automatiquement marquée comme lue
            // Nous gardons cette logique au cas où nous voudrions changer le comportement plus tard

            detailsHTML += `
                <button class="delete-btn" data-id="${notification.id}">Delete</button>
            </div>`;

            // Injecter le contenu dans le modal
            document.getElementById('notificationDetailContent').innerHTML = detailsHTML;
            document.getElementById('notificationDetailModalLabel').textContent = notification.is_read ? 'Notification' : 'New Notification';

            // Ajouter les écouteurs d'événements aux boutons dans le modal
            const modal = document.getElementById('notificationDetailModal');

            // Bouton "Delete"
            const deleteBtn = modal.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function() {
                    // Fermer le modal de détails
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }

                    // Afficher le modal de confirmation
                    showConfirmationModal("Êtes-vous sûr de vouloir supprimer cette notification?", async function() {
                        try {
                            const response = await fetch(`${basePath}?action=deleteNotification`, {
                                method: "POST",
                                headers: { "Content-Type": "application/json" },
                                body: JSON.stringify({ id: notificationId })
                            });
                            const data = await response.json();
                            if (data.success) {
                                // Afficher le message de succès
                                document.getElementById('successModalLabel').innerHTML = '<i class="fas fa-check-circle me-2"></i>Succès';
                                document.getElementById('successModalLabel').parentNode.classList.remove('bg-danger');
                                document.getElementById('successModalLabel').parentNode.classList.add('bg-success');
                                document.getElementById('successMessage').textContent = "Notification supprimée avec succès!";
                                document.querySelector('#successModal .modal-body i').classList.remove('fa-exclamation-circle', 'text-danger');
                                document.querySelector('#successModal .modal-body i').classList.add('fa-check-circle', 'text-success');
                                document.querySelector('#successModal .modal-footer button').classList.remove('btn-danger');
                                document.querySelector('#successModal .modal-footer button').classList.add('btn-success');

                                const successModalElement = document.getElementById('successModal');
                                if (successModalElement) {
                                    showCleanModal(successModalElement, 2000);
                                }

                                // Recharger les notifications
                                loadNotifications(currentPage);
                            }
                        } catch (error) {
                            console.error("Error deleting notification:", error);
                            showErrorModal("Erreur lors de la suppression de la notification");
                        }
                    });
                });
            }

            // Si la notification n'est pas encore lue, la marquer comme lue
            if (!notification.is_read) {
                console.log('Marking notification as read automatically:', notificationId);
                fetch(`${basePath}?action=markAsRead`, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ id: notificationId })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Server responded with status ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        console.log('Notification marked as read successfully');

                        // Mettre à jour l'apparence de la carte dans la liste
                        const notificationCard = document.querySelector(`.me-card[data-notification-id="${notificationId}"]`);
                        if (notificationCard) {
                            notificationCard.classList.add('read');
                            const icon = notificationCard.querySelector('.notification-icon i');
                            if (icon) {
                                // Mettre à jour l'icône si nécessaire
                            }
                        }

                        // Cacher le bouton "Mark as read" dans le modal
                        const markReadBtn = modal.querySelector('.mark-read-btn');
                        if (markReadBtn) {
                            markReadBtn.style.display = 'none';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error marking notification as read:', error);
                    // Ne pas afficher d'erreur à l'utilisateur pour ne pas perturber l'expérience
                });
            }

            // Afficher le modal en utilisant notre fonction showCleanModal
            showCleanModal(modal);
        })
        .catch(error => {
            console.error("Error fetching notification details:", error);
            showToast("Error fetching notification details", true);
        });
}

// Fonction globale pour ajouter des écouteurs d'événements
function addEventListeners() {
    console.log('Adding event listeners...');

    // Create Notification button event listener
    const createNotificationBtn = document.getElementById('createNotificationBtn');
    console.log('Create Notification button found:', !!createNotificationBtn);
    if (createNotificationBtn) {
        createNotificationBtn.addEventListener('click', function() {
            console.log('Create Notification button clicked');
            // Show the create notification modal using our clean modal function
            const createNotificationModalElement = document.getElementById('createNotificationModal');

            // Assurez-vous qu'aucun modal n'est déjà ouvert
            const existingModal = bootstrap.Modal.getInstance(createNotificationModalElement);
            if (existingModal) {
                existingModal.dispose();
            }

            // Supprimez tous les backdrops existants
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.remove();
            });

            // Réinitialisez le style du body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // Créez et affichez le nouveau modal
            const createNotificationModal = new bootstrap.Modal(createNotificationModalElement);

            // Ajoutez un événement pour nettoyer après la fermeture
            createNotificationModalElement.addEventListener('hidden.bs.modal', function () {
                // Supprimez tous les backdrops
                document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                    backdrop.remove();
                });

                // Réinitialisez le style du body
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }, { once: true });

            createNotificationModal.show();

            // Make sure the Send Notification button has an event listener
            // We'll add this listener only once when the modal is shown
            const sendNotificationBtn = document.getElementById('sendNotificationBtn');
            if (sendNotificationBtn && !sendNotificationBtn.hasAttribute('data-listener-attached')) {
                console.log('Adding event listener to Send Notification button inside modal');
                sendNotificationBtn.addEventListener('click', createNewNotification);
                // Mark the button as having a listener attached
                sendNotificationBtn.setAttribute('data-listener-attached', 'true');
            }
        });
    }

    // Ajouter des écouteurs d'événements aux cartes de notifications
    document.querySelectorAll('.me-card.compact').forEach(card => {
        card.addEventListener('click', function() {
            const notificationId = this.getAttribute('data-notification-id');
            if (notificationId) {
                showNotificationDetails(notificationId);
            }
        });
    });

    // Add event listener to mark all as read button
    const markAllBtn = document.getElementById('markAllBtn');
    if (markAllBtn) {
        markAllBtn.addEventListener("click", async function(e) {
            e.preventDefault();
            try {
                const response = await fetch(`${basePath}?action=markAllAsRead`, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" }
                });
                const data = await response.json();
                if (data.success) {
                    showToast("All notifications marked as read");
                    loadNotifications(currentPage);
                }
            } catch (error) {
                console.error("Error marking all notifications as read:", error);
                showToast("Error marking all notifications as read", true);
            }
        });
    }
}

function showToast(message, isError = false) {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast ${isError ? 'error' : 'success'}`;
    toast.textContent = message;

    toastContainer.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('fade-out');
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container';
    document.body.appendChild(container);
    return container;
}

// Fonction pour afficher l'indicateur de pièce jointe
function showAttachmentIndicator(type, value) {
    console.log('Showing attachment indicator:', type, value);
    // Remove existing indicator if any
    const existingIndicator = document.querySelector(`.message-attachment-indicator[data-type="${type}"]`);
    if (existingIndicator) {
        existingIndicator.remove();
    }

    if (!value) return;

    const attachmentIndicators = document.getElementById('attachmentIndicators');
    if (!attachmentIndicators) {
        console.error('Attachment indicators container not found');
        return;
    }

    const indicator = document.createElement('span');
    indicator.className = 'message-attachment-indicator';
    indicator.setAttribute('data-type', type);

    // Tronquer la valeur si elle est trop longue
    const displayValue = value.length > 25 ? value.substring(0, 22) + '...' : value;

    if (type === 'mediaUrl') {
        indicator.innerHTML = `<i class="fas fa-link"></i> ${displayValue} <button type="button" class="btn btn-sm" onclick="removeAttachment('mediaUrl')"><i class="fas fa-times"></i></button>`;
    } else {
        indicator.innerHTML = `<i class="fas fa-paperclip"></i> ${displayValue} <button type="button" class="btn btn-sm" onclick="removeAttachment('filePath')"><i class="fas fa-times"></i></button>`;
    }

    // Ajouter l'indicateur au conteneur
    attachmentIndicators.appendChild(indicator);
}

// Fonction pour supprimer une pièce jointe
function removeAttachment(type) {
    console.log('Removing attachment:', type);
    const field = document.getElementById(type);

    // Si c'est un fichier uploadé, on réinitialise également la variable globale
    if (type === 'filePath' && window.selectedFile) {
        window.selectedFile = null;
    }

    if (field) {
        field.value = '';
    }

    const indicator = document.querySelector(`.message-attachment-indicator[data-type="${type}"]`);
    if (indicator) {
        indicator.remove();
    }
}

// Initialize when the document is ready
document.addEventListener("DOMContentLoaded", function() {
    console.log('Document ready, initializing notifications page...');

    // Initial load of notifications
    loadNotifications(currentPage);

    // Add event listeners to the page
    addEventListeners();

    // Initialize collapse sections
    const mediaUrlCollapse = document.getElementById('mediaUrlCollapse');
    const filePathCollapse = document.getElementById('filePathCollapse');

    if (mediaUrlCollapse && filePathCollapse) {
        console.log('Initializing collapse sections...');

        // Create collapse instances
        const mediaUrlCollapseInstance = new bootstrap.Collapse(mediaUrlCollapse, {
            toggle: false
        });

        const filePathCollapseInstance = new bootstrap.Collapse(filePathCollapse, {
            toggle: false
        });

        // Add event listeners for toggle buttons
        const toggleMediaUrlBtn = document.getElementById('toggleMediaUrlBtn');
        const toggleFilePathBtn = document.getElementById('toggleFilePathBtn');

        if (toggleMediaUrlBtn) {
            toggleMediaUrlBtn.addEventListener('click', function() {
                console.log('Media URL button clicked');
                if (filePathCollapse.classList.contains('show')) {
                    filePathCollapseInstance.hide();
                }
                mediaUrlCollapseInstance.toggle();
            });
        }

        if (toggleFilePathBtn) {
            toggleFilePathBtn.addEventListener('click', function() {
                console.log('File path button clicked');
                if (mediaUrlCollapse.classList.contains('show')) {
                    mediaUrlCollapseInstance.hide();
                }
                filePathCollapseInstance.toggle();
            });
        }

        // Add event listeners for cancel buttons
        const cancelMediaUrlBtn = document.getElementById('cancelMediaUrlBtn');
        const cancelFilePathBtn = document.getElementById('cancelFilePathBtn');

        if (cancelMediaUrlBtn) {
            cancelMediaUrlBtn.addEventListener('click', function() {
                mediaUrlCollapseInstance.hide();
                const mediaUrlInput = document.getElementById('mediaUrlInput');
                if (mediaUrlInput) {
                    mediaUrlInput.value = '';
                }
            });
        }

        if (cancelFilePathBtn) {
            cancelFilePathBtn.addEventListener('click', function() {
                filePathCollapseInstance.hide();
                const fileInput = document.getElementById('fileInput');
                if (fileInput) {
                    fileInput.value = '';
                }
            });
        }

        // Add event listeners for add buttons
        const addMediaUrlToNotificationBtn = document.getElementById('addMediaUrlToNotificationBtn');
        const addFileToNotificationBtn = document.getElementById('addFileToNotificationBtn');

        if (addMediaUrlToNotificationBtn) {
            addMediaUrlToNotificationBtn.addEventListener('click', function() {
                console.log('Add media URL button clicked');
                const mediaUrlInput = document.getElementById('mediaUrlInput');
                const mediaUrl = document.getElementById('mediaUrl');

                if (mediaUrlInput && mediaUrl && mediaUrlInput.value.trim()) {
                    mediaUrl.value = mediaUrlInput.value;
                    showAttachmentIndicator('mediaUrl', mediaUrlInput.value);
                    mediaUrlCollapseInstance.hide();
                    mediaUrlInput.value = '';
                }
            });
        }

        if (addFileToNotificationBtn) {
            addFileToNotificationBtn.addEventListener('click', function() {
                console.log('Add file button clicked');
                const fileInput = document.getElementById('fileInput');

                if (fileInput && fileInput.files.length > 0) {
                    const file = fileInput.files[0];
                    window.selectedFile = file;
                    showAttachmentIndicator('filePath', file.name);
                    filePathCollapseInstance.hide();
                    fileInput.value = '';
                } else {
                    if (typeof showErrorModal === 'function') {
                        showErrorModal('Veuillez sélectionner un fichier à télécharger');
                    } else {
                        console.error('Veuillez sélectionner un fichier à télécharger');
                    }
                }
            });
        }
    }
});