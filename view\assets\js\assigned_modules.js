/**
 * Script pour la gestion des modules assignés aux enseignants
 *
 * Ce script utilise fetch pour récupérer les données des modules assignés
 * et gère l'affichage et le filtrage des données.
 */

// Variables globales
let teacherId = null;
let currentFilters = {
    semester: '',
    level: '',
    filiere: '',
    type: '',
    academic_year: getCurrentAcademicYear()
};

// Initialisation au chargement du document
document.addEventListener('DOMContentLoaded', function() {
    // Récupérer l'ID de l'enseignant depuis un attribut data sur le body
    teacherId = document.body.getAttribute('data-teacher-id');

    if (!teacherId) {
        showError("ID d'enseignant non trouvé. Veuillez vous déconnecter et vous reconnecter.");
        return;
    }

    // Initialiser les filtres
    initializeFilters();

    // Charger les données initiales
    loadAssignedModules();

    // Ajouter des écouteurs d'événements pour les filtres
    addFilterEventListeners();

    // Ajouter un écouteur d'événement pour le bouton de réinitialisation des filtres
    const resetButton = document.getElementById('reset-filters');
    if (resetButton) {
        resetButton.addEventListener('click', function(e) {
            e.preventDefault();
            resetFilters();
        });
    }
});

/**
 * Initialise les filtres avec les valeurs par défaut
 */
function initializeFilters() {
    console.log("Initialisation des filtres");

    // Charger les semestres (par défaut, limités à S1-S5)
    console.log("Chargement des semestres par défaut (S1-S5)");
    loadFilterOptions('semester', '../../route/commonRoute.php?action=getSemesters');

    // Charger les niveaux
    console.log("Chargement de tous les niveaux");
    loadFilterOptions('level', '../../route/commonRoute.php?action=getLevels');

    // Charger les filières
    console.log("Chargement de toutes les filières");
    loadFilterOptions('filiere', '../../route/commonRoute.php?action=getFilieres');

    // Initialiser le filtre d'année académique
    initializeAcademicYearFilter();

    // Définir les valeurs initiales des filtres depuis l'URL
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.has('semester')) {
        currentFilters.semester = urlParams.get('semester');
        document.getElementById('semester').value = currentFilters.semester;
    }

    if (urlParams.has('level')) {
        currentFilters.level = urlParams.get('level');
        document.getElementById('level').value = currentFilters.level;
    }

    if (urlParams.has('filiere')) {
        currentFilters.filiere = urlParams.get('filiere');
        document.getElementById('filiere').value = currentFilters.filiere;
    }

    if (urlParams.has('type')) {
        currentFilters.type = urlParams.get('type');
        document.getElementById('type').value = currentFilters.type;
    }

    if (urlParams.has('academic_year')) {
        currentFilters.academic_year = urlParams.get('academic_year');
        document.getElementById('academic_year').value = currentFilters.academic_year;
    }
}

/**
 * Charge les options pour un filtre donné
 *
 * @param {string} filterId - L'ID de l'élément select
 * @param {string} url - L'URL pour récupérer les données
 */
async function loadFilterOptions(filterId, url) {
    console.log(`Chargement des options pour ${filterId} depuis ${url}`);

    try {
        const response = await fetch(url);

        // Vérifier si la réponse est OK
        if (!response.ok) {
            const contentType = response.headers.get('content-type');

            // Si la réponse est du JSON, essayer de l'analyser pour obtenir le message d'erreur
            if (contentType && contentType.includes('application/json')) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Erreur HTTP: ${response.status}`);
            } else {
                // Si ce n'est pas du JSON, récupérer le texte de la réponse
                const errorText = await response.text();
                console.error(`Réponse non-JSON reçue pour ${filterId}:`, errorText);
                throw new Error(`Erreur HTTP: ${response.status}. Le serveur a renvoyé une réponse non-JSON.`);
            }
        }

        // Récupérer le texte de la réponse
        const responseText = await response.text();

        // Vérifier si la réponse est vide
        if (!responseText.trim()) {
            throw new Error('La réponse du serveur est vide');
        }

        // Essayer de parser le JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error(`Erreur de parsing JSON pour ${filterId}:`, parseError);
            console.error('Réponse reçue:', responseText);
            throw new Error('Impossible de parser la réponse JSON du serveur.');
        }

        if (data.error) {
            console.error(`Erreur lors du chargement des options pour ${filterId}:`, data.error);
            return;
        }

        const selectElement = document.getElementById(filterId);
        if (!selectElement) {
            console.error(`Élément select avec ID ${filterId} non trouvé`);
            return;
        }

        // Vider les options existantes sauf la première (option par défaut)
        while (selectElement.options.length > 1) {
            selectElement.remove(1);
        }

        // Vérifier si data.data existe et est un tableau
        if (!data.data || !Array.isArray(data.data)) {
            console.error(`Les données reçues pour ${filterId} ne sont pas au format attendu:`, data);
            return;
        }

        console.log(`${data.data.length} options reçues pour ${filterId}`);

        // Ajouter les nouvelles options
        data.data.forEach(item => {
            const option = document.createElement('option');

            // Déterminer les propriétés à utiliser en fonction du type de filtre
            if (filterId === 'semester') {
                option.value = item.id;
                option.textContent = item.nom;
            } else if (filterId === 'level') {
                option.value = item.id;
                option.textContent = item.nom;
            } else if (filterId === 'filiere') {
                option.value = item.id_filiere;
                option.textContent = item.nom_filiere;
            }

            selectElement.appendChild(option);
        });

        // Sélectionner l'option correspondant au filtre actuel
        if (currentFilters[filterId]) {
            selectElement.value = currentFilters[filterId];
        }

        console.log(`Options chargées avec succès pour ${filterId}`);

        // Déclencher un événement change pour que les filtres en cascade se mettent à jour
        // si la valeur a été modifiée programmatiquement
        if (selectElement.value !== '' && (filterId === 'filiere' || filterId === 'level')) {
            console.log(`Déclenchement d'un événement change pour ${filterId} avec la valeur ${selectElement.value}`);
            const event = new Event('change');
            selectElement.dispatchEvent(event);
        }
    } catch (error) {
        console.error(`Erreur lors du chargement des options pour ${filterId}:`, error);
        // Ne pas afficher d'erreur à l'utilisateur pour les filtres, juste logger l'erreur
    }
}

/**
 * Initialise le filtre d'année académique
 */
function initializeAcademicYearFilter() {
    const selectElement = document.getElementById('academic_year');
    if (!selectElement) {
        console.error("Élément select pour l'année académique non trouvé");
        return;
    }

    // Vider les options existantes sauf la première (option par défaut)
    while (selectElement.options.length > 1) {
        selectElement.remove(1);
    }

    // Ajouter les options d'année académique (année courante et 5 années précédentes)
    const currentYear = new Date().getFullYear();
    for (let i = 0; i < 6; i++) {
        const year = currentYear - i;
        const academicYear = `${year}-${year + 1}`;

        const option = document.createElement('option');
        option.value = academicYear;
        option.textContent = academicYear;

        selectElement.appendChild(option);
    }

    // Sélectionner l'année académique courante par défaut
    if (currentFilters.academic_year) {
        selectElement.value = currentFilters.academic_year;
    }
}

/**
 * Récupère l'année académique courante
 *
 * @returns {string} L'année académique au format "YYYY-YYYY"
 */
function getCurrentAcademicYear() {
    const currentYear = new Date().getFullYear();
    return `${currentYear}-${currentYear + 1}`;
}

/**
 * Charge les modules assignés à l'enseignant
 */
async function loadAssignedModules() {
    try {
        // Afficher un indicateur de chargement
        const resultsContainer = document.getElementById('results-container');
        if (resultsContainer) {
            resultsContainer.innerHTML = '<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Chargement...</span></div><p class="mt-3">Chargement des données...</p></div>';
        }

        // Construire l'URL avec les filtres
        let url = `../../route/assignedModulesRoute.php?action=getAssignedModules&teacherId=${teacherId}`;

        // Ajouter les filtres à l'URL
        Object.keys(currentFilters).forEach(key => {
            if (currentFilters[key]) {
                url += `&${key}=${encodeURIComponent(currentFilters[key])}`;
            }
        });

        console.log('Chargement des modules depuis:', url);

        const response = await fetch(url);

        // Vérifier si la réponse est OK
        if (!response.ok) {
            const contentType = response.headers.get('content-type');

            // Si la réponse est du JSON, essayer de l'analyser pour obtenir le message d'erreur
            if (contentType && contentType.includes('application/json')) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Erreur HTTP: ${response.status}`);
            } else {
                // Si ce n'est pas du JSON, récupérer le texte de la réponse
                const errorText = await response.text();
                console.error('Réponse non-JSON reçue:', errorText);
                throw new Error(`Erreur HTTP: ${response.status}. Le serveur a renvoyé une réponse non-JSON.`);
            }
        }

        // Récupérer le texte de la réponse
        const responseText = await response.text();

        // Vérifier si la réponse est vide
        if (!responseText.trim()) {
            throw new Error('La réponse du serveur est vide');
        }

        // Essayer de parser le JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('Erreur de parsing JSON:', parseError);
            console.error('Réponse reçue:', responseText);
            throw new Error('Impossible de parser la réponse JSON du serveur. Veuillez contacter l\'administrateur.');
        }

        // Vérifier si la réponse contient une erreur
        if (data.error) {
            showError(data.error);
            return;
        }

        // Mettre à jour le compteur de modules
        const moduleCountElement = document.getElementById('module-count');
        if (moduleCountElement) {
            moduleCountElement.textContent = `${data.count} unité(s) d'enseignement trouvée(s)`;
        }

        // Afficher les modules
        displayModules(data.data);

        // Mettre à jour l'URL avec les filtres
        updateUrlWithFilters();
    } catch (error) {
        console.error('Erreur lors du chargement des modules:', error);
        showError(error.message || 'Erreur lors du chargement des modules. Veuillez réessayer.');
    }
}

/**
 * Affiche les modules dans le tableau
 *
 * @param {Array} modules - Les modules à afficher
 */
function displayModules(modules) {
    const resultsContainer = document.getElementById('results-container');
    if (!resultsContainer) {
        console.error('Conteneur de résultats non trouvé');
        return;
    }

    if (!modules || modules.length === 0) {
        resultsContainer.innerHTML = '<div class="alert alert-info" role="alert">Aucune unité d\'enseignement trouvée correspondant aux critères sélectionnés.</div>';
        return;
    }

    // Créer le tableau
    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Module</th>
                        <th>Unité d'enseignement</th>
                        <th>Filière</th>
                        <th>Niveau</th>
                        <th>Semestre</th>
                        <th>Type</th>
                        <th>Heures</th>
                        <th>Groupes</th>
                        <th>Statut</th>
                        <th>Année académique</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // Ajouter les lignes du tableau
    modules.forEach(module => {
        html += `
            <tr>
                <td>
                    <strong>${escapeHtml(module.module_name)}</strong>
                </td>
                <td>
                    ${escapeHtml(module.ue_name || `${module.module_name} - ${module.ue_type}`)}
                </td>
                <td>${escapeHtml(module.filiere_name || 'N/A')}</td>
                <td>${escapeHtml(module.niveau_name || 'N/A')}</td>
                <td>${escapeHtml(module.semestre_name || 'N/A')}</td>
                <td>
                    <span class="badge ${getTypeClass(module.ue_type)}">
                        ${escapeHtml(module.ue_type || 'N/A')}
                    </span>
                </td>
                <td>${escapeHtml(module.volume_horaire || 'N/A')}</td>
                <td>${escapeHtml(module.nb_groupes || '1')}</td>
                <td>
                    <span class="badge bg-success">Assignée</span>
                </td>
                <td>${escapeHtml(module.academic_year || 'N/A')}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    resultsContainer.innerHTML = html;
}

/**
 * Ajoute des écouteurs d'événements pour les filtres
 */
function addFilterEventListeners() {
    // Liste des filtres
    const filters = ['semester', 'level', 'filiere', 'type', 'academic_year'];

    // Ajouter un écouteur d'événement onChange pour chaque filtre
    filters.forEach(filterId => {
        const filterElement = document.getElementById(filterId);
        if (filterElement) {
            filterElement.addEventListener('change', function() {
                applyFilters();
            });
        }
    });

    // Ajouter des écouteurs pour les filtres en cascade
    setupCascadingFilters();
}

/**
 * Configure les filtres en cascade
 */
function setupCascadingFilters() {
    console.log("Configuration des filtres en cascade");

    // Filière -> Niveau
    const filiereFilter = document.getElementById('filiere');
    if (filiereFilter) {
        filiereFilter.addEventListener('change', function() {
            const filiereId = this.value;
            console.log(`Filière changée: ID=${filiereId}`);

            // Réinitialiser les filtres dépendants
            document.getElementById('level').value = '';
            document.getElementById('semester').value = '';
            currentFilters.level = '';
            currentFilters.semester = '';

            if (filiereId) {
                // Charger les niveaux pour cette filière
                console.log(`Chargement des niveaux pour la filière ID=${filiereId}`);
                loadFilterOptions('level', `../../route/commonRoute.php?action=getLevels&filiere_id=${filiereId}`);

                // Recharger les semestres par défaut (S1-S5)
                console.log("Rechargement des semestres par défaut (S1-S5) après changement de filière");
                loadFilterOptions('semester', '../../route/commonRoute.php?action=getSemesters');
            } else {
                // Réinitialiser le filtre de niveau
                console.log("Réinitialisation du filtre de niveau");
                loadFilterOptions('level', '../../route/commonRoute.php?action=getLevels');

                // Réinitialiser le filtre de semestre aux valeurs par défaut (S1-S5)
                console.log("Réinitialisation du filtre de semestre aux valeurs par défaut (S1-S5)");
                loadFilterOptions('semester', '../../route/commonRoute.php?action=getSemesters');
            }
        });
    }

    // Niveau -> Semestre
    const levelFilter = document.getElementById('level');
    if (levelFilter) {
        levelFilter.addEventListener('change', function() {
            const levelId = this.value;
            console.log(`Niveau changé: ID=${levelId}`);

            // Réinitialiser le filtre de semestre
            document.getElementById('semester').value = '';
            currentFilters.semester = '';

            if (levelId) {
                // Charger les semestres pour ce niveau
                console.log(`Chargement des semestres pour le niveau ID=${levelId}`);
                loadFilterOptions('semester', `../../route/commonRoute.php?action=getSemesters&niveau_id=${levelId}`);
            } else {
                // Réinitialiser le filtre de semestre aux valeurs par défaut (S1-S5)
                console.log("Réinitialisation du filtre de semestre aux valeurs par défaut (S1-S5)");
                loadFilterOptions('semester', '../../route/commonRoute.php?action=getSemesters');
            }
        });
    }
}

/**
 * Applique les filtres sélectionnés
 */
function applyFilters() {
    // Récupérer les valeurs des filtres
    currentFilters.semester = document.getElementById('semester').value;
    currentFilters.level = document.getElementById('level').value;
    currentFilters.filiere = document.getElementById('filiere').value;
    currentFilters.type = document.getElementById('type').value;
    currentFilters.academic_year = document.getElementById('academic_year').value;

    // Charger les modules avec les nouveaux filtres
    loadAssignedModules();
}

/**
 * Réinitialise les filtres
 */
function resetFilters() {
    console.log("Réinitialisation des filtres");

    // Réinitialiser les valeurs des filtres
    document.getElementById('semester').value = '';
    document.getElementById('level').value = '';
    document.getElementById('filiere').value = '';
    document.getElementById('type').value = '';
    document.getElementById('academic_year').value = getCurrentAcademicYear();

    // Mettre à jour les filtres courants
    currentFilters.semester = '';
    currentFilters.level = '';
    currentFilters.filiere = '';
    currentFilters.type = '';
    currentFilters.academic_year = getCurrentAcademicYear();

    // Recharger les options de semestre pour afficher uniquement S1-S5
    console.log("Rechargement des semestres par défaut (S1-S5) après réinitialisation");
    loadFilterOptions('semester', '../../route/commonRoute.php?action=getSemesters');

    // Charger les modules avec les filtres réinitialisés
    loadAssignedModules();
}

/**
 * Met à jour l'URL avec les filtres actuels
 */
function updateUrlWithFilters() {
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);

    // Mettre à jour ou supprimer les paramètres
    Object.keys(currentFilters).forEach(key => {
        if (currentFilters[key]) {
            params.set(key, currentFilters[key]);
        } else {
            params.delete(key);
        }
    });

    // Mettre à jour l'URL sans recharger la page
    const newUrl = `${url.pathname}${params.toString() ? '?' + params.toString() : ''}`;
    window.history.pushState({}, '', newUrl);
}

/**
 * Affiche un message d'erreur
 *
 * @param {string} message - Le message d'erreur à afficher
 */
function showError(message) {
    const resultsContainer = document.getElementById('results-container');
    if (resultsContainer) {
        resultsContainer.innerHTML = `<div class="alert alert-danger" role="alert">${escapeHtml(message)}</div>`;
    }
}

/**
 * Échappe les caractères HTML spéciaux
 *
 * @param {string} text - Le texte à échapper
 * @returns {string} Le texte échappé
 */
function escapeHtml(text) {
    if (!text) return '';

    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };

    return text.toString().replace(/[&<>"']/g, function(m) { return map[m]; });
}

/**
 * Tronque un texte à une longueur donnée
 *
 * @param {string} text - Le texte à tronquer
 * @param {number} length - La longueur maximale
 * @returns {string} Le texte tronqué
 */
function truncateText(text, length) {
    if (!text) return '';
    return text.length > length ? text.substring(0, length) + '...' : text;
}

/**
 * Retourne la classe CSS pour un type d'UE
 *
 * @param {string} type - Le type d'UE
 * @returns {string} La classe CSS
 */
function getTypeClass(type) {
    switch (type) {
        case 'Cours':
            return 'bg-primary';
        case 'TD':
            return 'bg-success';
        case 'TP':
            return 'bg-info';
        default:
            return 'bg-secondary';
    }
}

// La fonction getStatusBadge a été supprimée car la colonne statut n'existe pas dans la table affectation
