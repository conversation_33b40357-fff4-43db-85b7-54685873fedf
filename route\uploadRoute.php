<?php
require_once "../utils/response.php";

// Set the upload directory
$uploadDir = "../uploads/profiles/";

// Create the directory if it doesn't exist
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0777, true);
}

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if file was uploaded without errors
    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
        $tempFile = $_FILES['profile_picture']['tmp_name'];
        $fileType = $_FILES['profile_picture']['type'];
        $fileName = $_FILES['profile_picture']['name'];
        
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
        if (!in_array($fileType, $allowedTypes)) {
            jsonResponse(['error' => 'Invalid file type. Only JPG, PNG, and GIF are allowed.'], 400);
            exit;
        }
        
        // Generate a unique filename
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $uniqueName = uniqid() . '.' . $extension;
        $targetFile = $uploadDir . $uniqueName;
        
        // Move the uploaded file to the target directory
        if (move_uploaded_file($tempFile, $targetFile)) {
            // Return the file path relative to the project root
            $relativePath = "uploads/profiles/" . $uniqueName;
            jsonResponse(['success' => true, 'filePath' => $relativePath], 200);
        } else {
            jsonResponse(['error' => 'Failed to upload file.'], 500);
        }
    } else {
        $error = $_FILES['profile_picture']['error'] ?? 'No file uploaded';
        jsonResponse(['error' => 'File upload error: ' . $error], 400);
    }
} else {
    jsonResponse(['error' => 'Method not allowed'], 405);
}
