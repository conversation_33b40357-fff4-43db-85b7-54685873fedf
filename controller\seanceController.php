<?php
require_once "../model/seanceModel.php";
require_once "../utils/response.php";

// Get all seances
function getAllSeancesAPI() {
    $seances = getAllSeances();

    if (isset($seances['error'])) {
        jsonResponse(['error' => $seances['error']], 404);
    }

    jsonResponse(['data' => $seances], 200);
}

// Get seances by filters
function getSeancesByFiltersAPI($filiere = null, $niveau = null, $groupe = null, $semestre = null, $semaine = null) {
    error_log("getSeancesByFiltersAPI called with: filiere=$filiere, niveau=$niveau, groupe=$groupe, semestre=$semestre, semaine=$semaine");

    // Normalize semestre to lowercase for case-insensitive comparison
    if ($semestre) {
        $semestre = strtolower($semestre);
        error_log("Normalized semestre to lowercase: $semestre");
    }

    // Determine class type
    $isAPClass = ($niveau && $groupe && $groupe !== 'all');
    $isCycleClass = ($niveau && $filiere && $filiere !== 'none' && $semestre);

    error_log("Class type detection in API: isAPClass=$isAPClass, isCycleClass=$isCycleClass");

    if (!$niveau) {
        error_log("Error: No niveau provided, cannot identify a class");
        jsonResponse(['error' => 'Level (niveau) is required to identify a class'], 400);
        return;
    }

    if (!$isAPClass && !$isCycleClass) {
        error_log("Warning: Not enough filters to identify a specific class");
        // We'll still try to get sessions with the provided filters
    }

    $seances = getSeancesByFilters($filiere, $niveau, $groupe, $semestre, $semaine);

    if (isset($seances['error'])) {
        error_log("Error in getSeancesByFilters: " . $seances['error']);
        jsonResponse(['error' => $seances['error']], 404);
    }

    error_log("Found " . count($seances) . " seances matching the filters");

    // Add class type information to the response
    $classInfo = [
        'type' => $isAPClass ? 'AP' : ($isCycleClass ? 'Cycle' : 'Unknown'),
        'niveau' => $niveau,
        'filiere' => $filiere,
        'groupe' => $groupe,
        'semestre' => $semestre
    ];

    jsonResponse(['data' => $seances, 'class_info' => $classInfo], 200);
}

// Get seance by ID
function getSeanceByIdAPI($id) {
    $seance = getSeanceById($id);

    if (isset($seance['error'])) {
        jsonResponse(['error' => $seance['error']], 404);
    }

    jsonResponse(['data' => $seance], 200);
}

// Add a new seance
function addSeanceAPI($data) {
    error_log("addSeanceAPI called with data: " . print_r($data, true));

    // Validate required fields
    $requiredFields = ['id_module', 'id_enseignant', 'id_salle', 'type_seance', 'jour', 'heure_debut', 'heure_fin', 'id_niveau', 'semestre'];

    // Map 'jour' to 'day' and 'type_seance' to 'type' for database compatibility
    if (isset($data['jour'])) {
        $data['day'] = $data['jour'];
    }
    if (isset($data['type_seance'])) {
        $data['type'] = $data['type_seance'];
    }
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            error_log("Missing required field: $field");
            jsonResponse(['error' => "Missing required field: $field"], 400);
            return;
        }
    }

    try {
        $result = addSeance($data);
        error_log("addSeance result: " . print_r($result, true));

        if (isset($result['error'])) {
            error_log("Error in addSeance: " . $result['error']);
            jsonResponse(['error' => $result['error']], 400);
            return;
        }

        jsonResponse(['data' => $result, 'message' => 'Séance ajoutée avec succès'], 201);
    } catch (Exception $e) {
        error_log("Exception in addSeance: " . $e->getMessage());
        jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
}

// Update a seance
function updateSeanceAPI($data) {
    error_log("updateSeanceAPI called with data: " . print_r($data, true));

    // Validate required fields
    $requiredFields = ['id_seance', 'id_module', 'id_enseignant', 'id_salle', 'type_seance', 'jour', 'heure_debut', 'heure_fin'];

    // Map 'jour' to 'day' and 'type_seance' to 'type' for database compatibility
    if (isset($data['jour'])) {
        $data['day'] = $data['jour'];
    }
    if (isset($data['type_seance'])) {
        $data['type'] = $data['type_seance'];
    }
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            error_log("Missing required field for update: $field");
            jsonResponse(['error' => "Missing required field: $field"], 400);
            return;
        }
    }

    try {
        $result = updateSeance($data);
        error_log("updateSeance result: " . print_r($result, true));

        if (isset($result['error'])) {
            error_log("Error in updateSeance: " . $result['error']);
            jsonResponse(['error' => $result['error']], 400);
            return;
        }

        jsonResponse(['data' => $result, 'message' => 'Séance mise à jour avec succès'], 200);
    } catch (Exception $e) {
        error_log("Exception in updateSeance: " . $e->getMessage());
        jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
}

// Delete a seance
function deleteSeanceAPI($id) {
    error_log("deleteSeanceAPI called with id: $id");

    if (!$id) {
        error_log("Missing ID for delete");
        jsonResponse(['error' => "Missing ID parameter"], 400);
        return;
    }

    try {
        $result = deleteSeance($id);
        error_log("deleteSeance result: " . print_r($result, true));

        if (isset($result['error'])) {
            error_log("Error in deleteSeance: " . $result['error']);
            jsonResponse(['error' => $result['error']], 400);
            return;
        }

        jsonResponse(['data' => $result, 'message' => 'Séance supprimée avec succès'], 200);
    } catch (Exception $e) {
        error_log("Exception in deleteSeance: " . $e->getMessage());
        jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
}
?>