/* Styles for the exam form */

/* Loading indicator */
#loading-indicator {
    padding: 20px;
    border-radius: 8px;
    background-color: #f8f9fa;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

#loading-indicator .spinner-border {
    width: 2rem;
    height: 2rem;
    color: #4361ee;
}

/* Form styles */
#addSessionForm .form-label {
    font-weight: 500;
    color: #495057;
}

#addSessionForm .form-select,
#addSessionForm .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 10px 12px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

#addSessionForm .form-select:focus,
#addSessionForm .form-control:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

/* Read-only fields */
#addSessionForm input[readonly] {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

/* Required fields */
#addSessionForm .form-label.required::after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
}

/* Modal footer */
.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 16px 24px;
}

.modal-footer .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.modal-footer .btn-primary {
    background-color: #4361ee;
    border-color: #4361ee;
}

.modal-footer .btn-primary:hover {
    background-color: #3a56d4;
    border-color: #3a56d4;
}