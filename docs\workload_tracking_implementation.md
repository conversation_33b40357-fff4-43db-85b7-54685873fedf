# Automatic Teacher Workload Tracking Implementation

## Overview

This document describes the implementation of automatic teacher workload tracking in the department head's UE preference confirmation and assignment system. The system automatically updates each teacher's `charge_horaire_accomplie` (accomplished workload hours) when teaching units are assigned or removed.

## Database Changes

### New Column: `charge_horaire_accomplie`

A new column has been added to the `enseignant` table:

```sql
ALTER TABLE enseignant ADD COLUMN charge_horaire_accomplie INT(11) NOT NULL DEFAULT 0 COMMENT 'Total des heures d\'enseignement assignées';
```

**Column Details:**
- **Name**: `charge_horaire_accomplie`
- **Type**: `INT(11)`
- **Default**: `0`
- **Purpose**: Stores the total accumulated teaching hours assigned to each teacher

## Implementation Details

### 1. Core Functions (enseignantModel.php)

#### `ensureChargeHoraireAccomplieColumn()`
- Automatically creates the `charge_horaire_accomplie` column if it doesn't exist
- Called before any workload operations to ensure database compatibility

#### `updateTeacherWorkloadHours($teacherId, $hours)`
- Updates a teacher's workload by adding/subtracting hours
- Supports both positive (addition) and negative (subtraction) values
- Uses database transactions for data integrity
- Returns `true` on success or error array on failure

#### `getTeacherWorkloadHours($teacherId)`
- Retrieves the current workload hours for a specific teacher
- Returns integer value or `false` on error

### 2. Automatic Workload Updates (affectationModel.php)

#### UE Preference Acceptance (`acceptUePreference`)
When a department head accepts a teacher's UE preference:
1. Creates/updates the affectation record
2. Retrieves the UE's `volume_horaire` from the database
3. Automatically adds these hours to the teacher's `charge_horaire_accomplie`
4. Logs all operations for debugging

#### Manual UE Assignment (`createManualAffectation`)
When a department head manually assigns a UE to a teacher:
1. Creates/updates the affectation record
2. Retrieves the UE's `volume_horaire` from the database
3. Automatically adds these hours to the teacher's `charge_horaire_accomplie`
4. Logs all operations for debugging

### 3. Workload Adjustment Functions

#### `deleteAffectationWithWorkloadAdjustment($affectationId)`
- Safely deletes an affectation while adjusting teacher workload
- Subtracts the UE hours from the teacher's total workload
- Uses transactions to ensure data consistency

#### `updateAffectationWithWorkloadTransfer($affectationId, $newTeacherId, $commentaire)`
- **NEW**: Handles workload transfer when reassigning UEs between teachers
- Automatically detects teacher changes and performs atomic workload adjustments
- Subtracts hours from original teacher and adds to new teacher
- Includes rollback protection if workload updates fail
- Uses database transactions for complete data integrity

#### `recalculateTeacherWorkload($teacherId)`
- Recalculates a teacher's total workload from all current assignments
- Useful for data integrity checks and corrections
- Sets the workload to the exact calculated total (not additive)

## Trigger Conditions

The workload tracking system activates automatically in these scenarios:

### ✅ **Workload Increases (Hours Added)**
1. **UE Preference Acceptance**: Department head approves a teacher's preference request
2. **Manual Assignment**: Department head manually assigns a UE to a teacher
3. **Assignment Updates**: Existing assignments are modified to include additional hours
4. **Assignment Transfer (Recipient)**: When a UE is transferred TO a teacher from another teacher

### ✅ **Workload Decreases (Hours Subtracted)**
1. **Assignment Deletion**: Using `deleteAffectationWithWorkloadAdjustment()`
2. **Assignment Modifications**: When UE assignments are changed or removed
3. **Manual Corrections**: Administrative adjustments to workload
4. **Assignment Transfer (Source)**: When a UE is transferred FROM a teacher to another teacher

### ✅ **Workload Transfers (Atomic Operations)**
1. **Teacher Reassignment**: When an existing UE assignment is changed to a different teacher
2. **Automatic Detection**: System automatically detects teacher changes in assignments
3. **Atomic Processing**: Both subtraction and addition happen in a single operation
4. **Rollback Protection**: If either operation fails, the entire transfer is rolled back

## Data Integrity Features

### Transaction Safety
- All workload updates use database transactions
- Rollback capability if any operation fails
- Atomic operations ensure data consistency

### Error Handling
- Comprehensive error logging for debugging
- Graceful failure handling (workload errors don't break main operations)
- Validation of all input parameters

### Automatic Column Creation
- System automatically creates required database columns
- Backward compatibility with existing installations
- No manual database modifications required

## Usage Examples

### For Department Heads
The workload tracking is completely automatic and transparent:

```php
// When accepting a UE preference
$result = acceptUePreference($preferenceId, $academicYear, $comment, $departmentId);
// Teacher's workload is automatically updated

// When making manual assignments
$result = createManualAffectation($teacherId, $ueId, $academicYear, $comment);
// Teacher's workload is automatically updated

// When reassigning a UE to a different teacher
$result = createManualAffectation($newTeacherId, $ueId, $academicYear, $comment);
// Workload is automatically transferred from old teacher to new teacher

// When updating an assignment with dedicated function
$result = updateAffectationWithWorkloadTransfer($affectationId, $newTeacherId, $comment);
// Workload transfer is handled atomically
```

### For Administrators
Manual workload management functions:

```php
// Get current workload
$currentHours = getTeacherWorkloadHours($teacherId);

// Manually adjust workload
$result = updateTeacherWorkloadHours($teacherId, 24); // Add 24 hours
$result = updateTeacherWorkloadHours($teacherId, -12); // Subtract 12 hours

// Recalculate from assignments
$result = recalculateTeacherWorkload($teacherId);
```

## Benefits

### 1. **Automatic Tracking**
- No manual workload calculations required
- Real-time updates with each assignment
- Eliminates human error in workload management

### 2. **Data Accuracy**
- Workload always reflects current assignments
- Automatic adjustments when assignments change
- Built-in data integrity checks

### 3. **Administrative Efficiency**
- Department heads can focus on assignment decisions
- Automatic compliance with workload requirements
- Easy workload monitoring and reporting

### 4. **Audit Trail**
- Complete logging of all workload changes
- Traceable assignment history
- Debugging capabilities for data issues

## Testing

A test script (`test_workload_tracking.php`) is provided to verify functionality:
- Tests column creation
- Validates workload calculations
- Checks addition and subtraction operations
- Verifies recalculation accuracy

## API Endpoints

### New Workload Transfer Endpoint
```
POST /controller/affectationController.php?action=updateAffectationWithWorkloadTransfer

Request Body:
{
    "affectation_id": 123,
    "new_teacher_id": 456,
    "commentaire": "Optional comment"
}

Response:
{
    "message": "Affectation updated successfully with workload transfer"
}
```

This endpoint provides a dedicated way to transfer UE assignments between teachers with automatic workload adjustment.

## Future Enhancements

Potential improvements for the workload tracking system:
- Workload history tracking with timestamps
- Automatic workload limit enforcement
- Integration with reporting dashboards
- Email notifications for workload thresholds
- Academic year-specific workload tracking
- Batch workload transfer operations
- Workload transfer approval workflows
