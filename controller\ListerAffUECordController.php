<?php
// Use a more robust path resolution to find the required files
$modelPath = __DIR__ . "/../model/affectationModel.php";

// Try different paths for model
if (file_exists($modelPath)) {
    require_once $modelPath;
} else {
    $altModelPath = __DIR__ . "/../../model/affectationModel.php";
    if (file_exists($altModelPath)) {
        require_once $altModelPath;
    } else {
        die("Cannot find the affectationModel.php file.");
    }
}

// Disable error display to avoid returning HTML
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Helper function to send JSON response
 *
 * @param array $data The data to send
 * @param int $statusCode HTTP status code
 */
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

/**
 * API endpoint to get affectations by filiere for ListerAffUECord
 *
 * @param int $filiereId The filiere ID
 */
function getListerAffUECordByFiliereAPI($filiereId) {
    // Validate filiere ID
    if (!is_numeric($filiereId)) {
        jsonResponse(['error' => 'Invalid filiere ID'], 400);
    }

    $affectations = getAffectationsByFiliere($filiereId);

    if (isset($affectations['error'])) {
        jsonResponse(['error' => $affectations['error']], 500);
    }

    jsonResponse(['data' => $affectations], 200);
}

// Handle API requests based on action parameter
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getListerAffUECordByFiliere':
            if (!isset($_GET['filiere_id'])) {
                jsonResponse(['error' => 'Filiere ID is required'], 400);
            }
            getListerAffUECordByFiliereAPI($_GET['filiere_id']);
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            break;
    }
} else {
    jsonResponse(['error' => 'Action is required'], 400);
}
?>