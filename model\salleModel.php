<?php
require_once "../config/db.php";

// Get all salles
function getAllSalles() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllSalles");
        return ["error" => "Database connection error"];
    }

    $sql = "SELECT * FROM salle ORDER BY nom_salle";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        error_log("Error executing query in getAllSalles: " . mysqli_error($conn));
        mysqli_close($conn);
        return ["error" => "Error fetching salles: " . mysqli_error($conn)];
    }

    $salles = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $salles[] = $row;
    }

    mysqli_close($conn);
    return $salles;
}

// Get salle by ID
function getSalleById($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getSalleById");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);
    $sql = "SELECT * FROM salle WHERE id_salle = '$id'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        error_log("Error executing query in getSalleById: " . mysqli_error($conn));
        mysqli_close($conn);
        return ["error" => "Error fetching salle: " . mysqli_error($conn)];
    }

    $salle = mysqli_fetch_assoc($result);
    
    if (!$salle) {
        mysqli_close($conn);
        return ["error" => "Salle not found"];
    }

    mysqli_close($conn);
    return $salle;
}

// Add a new salle
function addSalle($data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in addSalle");
        return ["error" => "Database connection error"];
    }

    // Validate required fields
    if (!isset($data['nom_salle']) || empty($data['nom_salle'])) {
        mysqli_close($conn);
        return ["error" => "Room name is required"];
    }

    $nom_salle = mysqli_real_escape_string($conn, $data['nom_salle']);
    
    // Check if a room with this name already exists
    $checkSql = "SELECT * FROM salle WHERE nom_salle = '$nom_salle'";
    $checkResult = mysqli_query($conn, $checkSql);
    
    if (mysqli_num_rows($checkResult) > 0) {
        mysqli_close($conn);
        return ["error" => "A room with this name already exists"];
    }

    // Insert the new room
    $sql = "INSERT INTO salle (nom_salle) VALUES ('$nom_salle')";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        error_log("Error executing query in addSalle: " . mysqli_error($conn));
        mysqli_close($conn);
        return ["error" => "Error adding salle: " . mysqli_error($conn)];
    }

    $id = mysqli_insert_id($conn);
    mysqli_close($conn);

    return ["id" => $id, "message" => "Room added successfully"];
}

// Update a salle
function updateSalle($data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateSalle");
        return ["error" => "Database connection error"];
    }

    // Validate required fields
    if (!isset($data['id_salle']) || empty($data['id_salle'])) {
        mysqli_close($conn);
        return ["error" => "Room ID is required"];
    }

    if (!isset($data['nom_salle']) || empty($data['nom_salle'])) {
        mysqli_close($conn);
        return ["error" => "Room name is required"];
    }

    $id_salle = mysqli_real_escape_string($conn, $data['id_salle']);
    $nom_salle = mysqli_real_escape_string($conn, $data['nom_salle']);
    
    // Check if the room exists
    $checkSql = "SELECT * FROM salle WHERE id_salle = '$id_salle'";
    $checkResult = mysqli_query($conn, $checkSql);
    
    if (mysqli_num_rows($checkResult) === 0) {
        mysqli_close($conn);
        return ["error" => "Room not found"];
    }

    // Check if another room with the same name exists
    $checkNameSql = "SELECT * FROM salle WHERE nom_salle = '$nom_salle' AND id_salle != '$id_salle'";
    $checkNameResult = mysqli_query($conn, $checkNameSql);
    
    if (mysqli_num_rows($checkNameResult) > 0) {
        mysqli_close($conn);
        return ["error" => "Another room with this name already exists"];
    }

    // Update the room
    $sql = "UPDATE salle SET nom_salle = '$nom_salle' WHERE id_salle = '$id_salle'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        error_log("Error executing query in updateSalle: " . mysqli_error($conn));
        mysqli_close($conn);
        return ["error" => "Error updating salle: " . mysqli_error($conn)];
    }

    mysqli_close($conn);
    return ["id" => $id_salle, "message" => "Room updated successfully"];
}

// Delete a salle
function deleteSalle($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in deleteSalle");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);
    
    // Check if the room exists
    $checkSql = "SELECT * FROM salle WHERE id_salle = '$id'";
    $checkResult = mysqli_query($conn, $checkSql);
    
    if (mysqli_num_rows($checkResult) === 0) {
        mysqli_close($conn);
        return ["error" => "Room not found"];
    }

    // Check if the room is used in any seance
    $checkUsageSql = "SELECT COUNT(*) as count FROM seance WHERE id_salle = '$id'";
    $checkUsageResult = mysqli_query($conn, $checkUsageSql);
    $usageCount = mysqli_fetch_assoc($checkUsageResult)['count'];
    
    if ($usageCount > 0) {
        mysqli_close($conn);
        return ["error" => "Cannot delete room: it is used in $usageCount sessions"];
    }

    // Check if the room is used in any exam
    $checkExamUsageSql = "SELECT COUNT(*) as count FROM exams WHERE id_salle = '$id'";
    $checkExamUsageResult = mysqli_query($conn, $checkExamUsageSql);
    
    if ($checkExamUsageResult) {
        $examUsageCount = mysqli_fetch_assoc($checkExamUsageResult)['count'];
        
        if ($examUsageCount > 0) {
            mysqli_close($conn);
            return ["error" => "Cannot delete room: it is used in $examUsageCount exams"];
        }
    }

    // Delete the room
    $sql = "DELETE FROM salle WHERE id_salle = '$id'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        error_log("Error executing query in deleteSalle: " . mysqli_error($conn));
        mysqli_close($conn);
        return ["error" => "Error deleting salle: " . mysqli_error($conn)];
    }

    mysqli_close($conn);
    return ["message" => "Room deleted successfully"];
}
?>