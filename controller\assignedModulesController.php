<?php
/**
 * Contrôleur pour la gestion des modules assignés aux enseignants
 *
 * Ce fichier contient les fonctions de contrôle pour récupérer et traiter
 * les données des modules et unités d'enseignement assignés à un enseignant.
 */

// Inclure les modèles
require_once __DIR__ . '/../model/assignedModulesModel.php';
require_once __DIR__ . '/../model/commonModel.php';

/**
 * Récupère les données des modules assignés pour l'API
 *
 * @param int $teacherId ID de l'enseignant
 * @param array $filters Filtres à appliquer
 * @return array Données des modules assignés
 */
function getAssignedModulesData($teacherId, $filters = []) {
    // Vérifier si l'ID de l'enseignant est valide
    if (!$teacherId) {
        return [
            'error' => "ID d'enseignant non valide"
        ];
    }

    // Récupérer les modules assignés
    $assignedModules = getAssignedTeachingUnits($teacherId, $filters);

    // Vérifier s'il y a une erreur
    if (isset($assignedModules['error'])) {
        return [
            'error' => $assignedModules['error']
        ];
    }

    // Retourner les données
    return [
        'data' => $assignedModules,
        'count' => count($assignedModules)
    ];
}

/**
 * API pour récupérer les modules assignés à un enseignant
 *
 * @param int $teacherId ID de l'enseignant
 * @param array $filters Filtres à appliquer
 */
function getAssignedModulesAPI($teacherId, $filters = []) {
    // Vérifier si l'utilisateur est connecté et est un enseignant
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['user']) || ($_SESSION['user']['role'] !== 'enseignant' && $_SESSION['user']['role'] !== 'vacataire')) {
        jsonResponse(['error' => 'Accès non autorisé'], 401);
        exit;
    }

    // Vérifier si l'ID de l'enseignant est valide
    if (!$teacherId) {
        jsonResponse(['error' => "ID d'enseignant non trouvé"], 400);
        exit;
    }

    // Récupérer les données des modules assignés
    $result = getAssignedModulesData($teacherId, $filters);

    // Vérifier s'il y a une erreur
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 500);
        exit;
    }

    // Retourner les données
    jsonResponse($result, 200);
}

// Les fonctions API communes ont été déplacées vers commonController.php

// La fonction jsonResponse a été déplacée vers commonController.php
