<?php
/**
 * Modèle pour la gestion des modules assignés aux enseignants
 *
 * Ce fichier contient les fonctions d'accès aux données pour récupérer
 * les modules et unités d'enseignement assignés à un enseignant.
 */

// Inclure la connexion à la base de données et les fonctions communes
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/commonModel.php';

/**
 * Récupère toutes les unités d'enseignement assignées à un enseignant
 *
 * @param int $teacherId ID de l'enseignant
 * @param array $filters Filtres à appliquer (semestre, niveau, filière, type, année académique)
 * @return array Tableau des unités d'enseignement assignées
 */
function getAssignedTeachingUnits($teacherId, $filters = []) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    // Sécuriser les entrées
    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    // Vérifier les colonnes des tables pour utiliser les bons noms de champs
    $moduleColumns = [];
    $moduleColumnsQuery = mysqli_query($conn, "SHOW COLUMNS FROM module");
    while ($col = mysqli_fetch_assoc($moduleColumnsQuery)) {
        $moduleColumns[] = $col['Field'];
    }

    $ueColumns = [];
    $ueColumnsQuery = mysqli_query($conn, "SHOW COLUMNS FROM uniteenseignement");
    while ($col = mysqli_fetch_assoc($ueColumnsQuery)) {
        $ueColumns[] = $col['Field'];
    }

    $affectationColumns = [];
    $affectationColumnsQuery = mysqli_query($conn, "SHOW COLUMNS FROM affectation");
    while ($col = mysqli_fetch_assoc($affectationColumnsQuery)) {
        $affectationColumns[] = $col['Field'];
    }

    // Déterminer les noms de champs corrects
    $moduleIdField = in_array('id', $moduleColumns) ? 'id' : 'id_module';
    $filiereField = in_array('filiere_id', $moduleColumns) ? 'filiere_id' : 'id_filiere';
    $niveauField = in_array('id_niveau', $moduleColumns) ? 'id_niveau' : 'niveau_id';
    $semestreField = in_array('id_semestre', $moduleColumns) ? 'id_semestre' :
                    (in_array('semestre', $moduleColumns) ? 'semestre' : 'id_semestre');

    // Déterminer les noms de champs pour la table uniteenseignement
    // D'après la structure fournie, les champs sont: id, type, volume_horaire, nb_groupes, module_id
    $ueIdField = in_array('id', $ueColumns) ? 'id' : 'id_ue';
    $ueModuleIdField = in_array('module_id', $ueColumns) ? 'module_id' : 'id_module';
    $ueTypeField = in_array('type', $ueColumns) ? 'type' : 'type_ue';
    $ueVolumeHoraireField = in_array('volume_horaire', $ueColumns) ? 'volume_horaire' : 'heures';
    $ueNbGroupesField = in_array('nb_groupes', $ueColumns) ? 'nb_groupes' : 'nombre_groupes';

    $professeurIdField = in_array('professeur_id', $affectationColumns) ? 'professeur_id' : 'id_enseignant';
    $ueAffectationField = in_array('unite_enseignement_id', $affectationColumns) ? 'unite_enseignement_id' : 'id_ue';
    $anneeField = in_array('annee_academique', $affectationColumns) ? 'annee_academique' : 'annee_academique';

    // Journaliser les noms de champs pour le débogage
    error_log("Utilisation des noms de champs: moduleId=$moduleIdField, filiere=$filiereField, niveau=$niveauField, semestre=$semestreField");
    error_log("Champs UE: id=$ueIdField, moduleId=$ueModuleIdField, type=$ueTypeField, volumeHoraire=$ueVolumeHoraireField, nbGroupes=$ueNbGroupesField");
    error_log("Champs Affectation: professeurId=$professeurIdField, ueId=$ueAffectationField, annee=$anneeField");

    // Journaliser la structure de la table affectation pour le débogage
    error_log("Structure de la table affectation:");
    foreach ($affectationColumns as $col) {
        error_log(" - " . $col);
    }

    // Construire la requête - d'abord récupérer toutes les unités d'enseignement assignées à ce professeur
    $query = "SELECT DISTINCT
                ue.$ueIdField as ue_id,
                ue.$ueTypeField as ue_type,
                ue.$ueVolumeHoraireField as volume_horaire,
                ue.$ueNbGroupesField as nb_groupes,
                ue.$ueModuleIdField as module_id,
                CONCAT(m.nom, ' - ', ue.$ueTypeField) as ue_name,
                m.nom as module_name,
                m.$moduleIdField as module_id,
                f.nom_filiere as filiere_name,
                f.id_filiere as filiere_id,
                n.nom as niveau_name,
                n.id as niveau_id,
                s.nom as semestre_name,
                s.id as semestre_id,
                a.$anneeField as academic_year,
                a.id as affectation_id
              FROM affectation a
              JOIN uniteenseignement ue ON a.$ueAffectationField = ue.$ueIdField
              JOIN module m ON ue.$ueModuleIdField = m.$moduleIdField
              LEFT JOIN filiere f ON m.$filiereField = f.id_filiere
              LEFT JOIN niveaux n ON m.$niveauField = n.id
              LEFT JOIN semestre s ON m.$semestreField = s.id
              WHERE a.$professeurIdField = '$teacherId'";

    // Ajouter les filtres si fournis
    if (!empty($filters)) {
        if (!empty($filters['semester'])) {
            $semesterId = mysqli_real_escape_string($conn, $filters['semester']);
            $query .= " AND s.id = '$semesterId'";
        }

        if (!empty($filters['level'])) {
            $levelId = mysqli_real_escape_string($conn, $filters['level']);
            $query .= " AND n.id = '$levelId'";
        }

        if (!empty($filters['filiere'])) {
            $filiereId = mysqli_real_escape_string($conn, $filters['filiere']);
            $query .= " AND f.id_filiere = '$filiereId'";
        }

        if (!empty($filters['type'])) {
            $type = mysqli_real_escape_string($conn, $filters['type']);
            $query .= " AND ue.$ueTypeField = '$type'";
        }

        if (!empty($filters['academic_year'])) {
            $academicYear = mysqli_real_escape_string($conn, $filters['academic_year']);
            $query .= " AND a.$anneeField = '$academicYear'";
        }
    }

    // Trier par filière, niveau, semestre, nom du module, type d'UE
    $query .= " ORDER BY f.nom_filiere, n.nom, s.nom, m.nom, ue.$ueTypeField";

    // Journaliser la requête pour le débogage
    error_log("Exécution de la requête: " . $query);

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Erreur dans getAssignedTeachingUnits: " . $error);
        mysqli_close($conn);
        return ['error' => "Erreur lors de la récupération des unités d'enseignement: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $modules[] = $row;
    }

    error_log("Trouvé " . count($modules) . " unités d'enseignement assignées à l'enseignant ID: $teacherId");

    mysqli_close($conn);
    return $modules;
}

// Les fonctions communes ont été déplacées vers le fichier commonModel.php
