<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';

require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../model/modulesModel.php';

// Get filters from query parameters
$field_filter = isset($_GET['field']) ? intval($_GET['field']) : null;
$semester_filter = isset($_GET['semester']) ? $_GET['semester'] : null;

// Get data
$modules = getAllModules($field_filter, $semester_filter);
$fields = getAllFields();
$semesters = getAllSemesters();

// Handle module deletion
if (isset($_POST['delete_module']) && isset($_POST['id_module'])) {
    $result = deleteModule($_POST['id_module']);
    if ($result === true) {
        $success_message = "Module deleted successfully!";
        // Refresh modules list
        $modules = getAllModules($field_filter, $semester_filter);
    } else {
        $error_message = $result['error'];
    }
}

// Define module emojis based on module name and field
function getModuleEmoji($module_name, $field_name) {
    $module_name = strtolower($module_name);
    $field_name = strtolower($field_name);

    // Programming/Computer Science related modules
    if (strpos($module_name, 'programmation') !== false || strpos($module_name, 'coding') !== false) {
        return '💻';
    } elseif (strpos($module_name, 'base de données') !== false || strpos($module_name, 'database') !== false || strpos($module_name, 'sql') !== false) {
        return '🗃';
    } elseif (strpos($module_name, 'réseau') !== false || strpos($module_name, 'network') !== false) {
        return '🌐';
    } elseif (strpos($module_name, 'sécurité') !== false || strpos($module_name, 'security') !== false) {
        return '🔒';
    } elseif (strpos($module_name, 'web') !== false) {
        return '🌍';
    } elseif (strpos($module_name, 'mobile') !== false || strpos($module_name, 'android') !== false || strpos($module_name, 'ios') !== false) {
        return '📱';
    } elseif (strpos($module_name, 'intelligence artificielle') !== false || strpos($module_name, 'ai') !== false || strpos($module_name, 'machine learning') !== false) {
        return '🤖';
    } elseif (strpos($module_name, 'cloud') !== false) {
        return '☁';
    }

    // Engineering related modules
    elseif (strpos($module_name, 'structure') !== false || strpos($module_name, 'construction') !== false) {
        return '🏗';
    } elseif (strpos($module_name, 'électrique') !== false || strpos($module_name, 'electrique') !== false || strpos($module_name, 'circuit') !== false) {
        return '⚡';
    } elseif (strpos($module_name, 'mécanique') !== false || strpos($module_name, 'mecanique') !== false) {
        return '⚙';
    } elseif (strpos($module_name, 'hydraulique') !== false || strpos($module_name, 'eau') !== false) {
        return '💧';
    } elseif (strpos($module_name, 'thermique') !== false || strpos($module_name, 'chaleur') !== false) {
        return '🔥';
    } elseif (strpos($module_name, 'matériaux') !== false || strpos($module_name, 'materiaux') !== false) {
        return '🧱';
    }

    // Math and Science related modules
    elseif (strpos($module_name, 'mathématique') !== false || strpos($module_name, 'math') !== false) {
        return '🧮';
    } elseif (strpos($module_name, 'statistique') !== false || strpos($module_name, 'probabilité') !== false) {
        return '📊';
    } elseif (strpos($module_name, 'physique') !== false) {
        return '⚛';
    } elseif (strpos($module_name, 'chimie') !== false) {
        return '🧪';
    } elseif (strpos($module_name, 'biologie') !== false) {
        return '🧬';
    }

    // Languages and communication
    elseif (strpos($module_name, 'français') !== false || strpos($module_name, 'francais') !== false) {
        return '🇫🇷';
    } elseif (strpos($module_name, 'anglais') !== false || strpos($module_name, 'english') !== false) {
        return '🇬🇧';
    } elseif (strpos($module_name, 'espagnol') !== false || strpos($module_name, 'spanish') !== false) {
        return '🇪🇸';
    } elseif (strpos($module_name, 'allemand') !== false || strpos($module_name, 'german') !== false) {
        return '🇩🇪';
    } elseif (strpos($module_name, 'arabe') !== false || strpos($module_name, 'arabic') !== false) {
        return '🇲🇦';
    } elseif (strpos($module_name, 'communication') !== false) {
        return '🗣';
    }

    // Business and management
    elseif (strpos($module_name, 'gestion') !== false || strpos($module_name, 'management') !== false) {
        return '📋';
    } elseif (strpos($module_name, 'économie') !== false || strpos($module_name, 'economie') !== false) {
        return '💰';
    } elseif (strpos($module_name, 'marketing') !== false) {
        return '📢';
    } elseif (strpos($module_name, 'comptabilité') !== false || strpos($module_name, 'comptabilite') !== false) {
        return '📒';
    } elseif (strpos($module_name, 'finance') !== false) {
        return '💹';
    }

    // Default emojis based on field if no specific match
    elseif (strpos($field_name, 'informatique') !== false) {
        return '🖥';
    } elseif (strpos($field_name, 'génie civil') !== false || strpos($field_name, 'genie civil') !== false) {
        return '🏢';
    } elseif (strpos($field_name, 'électrique') !== false || strpos($field_name, 'electrique') !== false) {
        return '💡';
    } elseif (strpos($field_name, 'mécanique') !== false || strpos($field_name, 'mecanique') !== false) {
        return '🔧';
    } elseif (strpos($field_name, 'industriel') !== false) {
        return '🏭';
    } elseif (strpos($field_name, 'environnement') !== false) {
        return '🌱';
    } elseif (strpos($field_name, 'math') !== false) {
        return '📐';
    } elseif (strpos($field_name, 'langue') !== false) {
        return '📝';
    } else {
        return '📚';
    }
}

// Define softer pastel colors for modules
$pastelColors = [
    '#FFEEF2', // Softer pastel pink
    '#FFF8E1', // Softer pastel yellow
    '#E6F7FF', // Softer pastel blue
    '#E8F8EE', // Softer pastel green
    '#F0EAFF', // Softer pastel purple
    '#FFF0E6', // Softer pastel orange
    '#E6FFFE', // Softer pastel teal
    '#F5EAFF'  // Softer pastel lavender
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modules Management - UniAdmin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        .modules-content {
            padding: 20px;
        }

        .modules-header {
            margin-bottom: 30px;
            background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
            padding: 25px;
            border-radius: 15px;
            color: #2c5282;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .modules-header h1 {
            font-weight: 700;
            margin-bottom: 10px;
            font-size: 2.2rem;
        }

        .modules-header .subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .modules-filters {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .module-card {
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            height: 100%;
        }

        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
        }

        .module-emoji {
            font-size: 2.5rem;
            margin-bottom: 15px;
            text-align: center;
        }

        .module-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
        }

        .module-info {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 15px;
        }

        .module-actions {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }

        .module-actions .btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }

        .module-actions .btn-info {
            background-color: #6e8efb;
            border-color: #6e8efb;
        }

        .module-actions .btn-info:hover {
            background-color: #5a7df9;
            border-color: #5a7df9;
        }

        .module-actions .btn-danger {
            background-color: #ff6b6b;
            border-color: #ff6b6b;
        }

        .module-actions .btn-danger:hover {
            background-color: #ff5252;
            border-color: #ff5252;
        }

        /* Enhanced folded corner effect */
        .module-card {
            position: relative;
        }

        .module-card:before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            border-width: 0 40px 40px 0;
            border-style: solid;
            border-color: rgba(255, 255, 255, 0.7) rgba(255, 255, 255, 0.9);
            box-shadow: -3px 3px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 1;
        }

        .module-card:after {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 15px;
            height: 15px;
            background: rgba(0, 0, 0, 0.05);
            box-shadow: -2px 2px 5px rgba(0, 0, 0, 0.1);
            transform: rotate(-45deg) translate(10px, 22px);
            z-index: 2;
            transition: all 0.3s ease;
        }

        .module-card:hover:before {
            border-width: 0 50px 50px 0;
            border-color: rgba(255, 255, 255, 0.8) rgba(255, 255, 255, 1);
        }

        .module-card:hover:after {
            transform: rotate(-45deg) translate(12px, 27px);
        }

        .confirmation-modal .modal-content {
            border-radius: 15px;
        }

        .confirmation-modal .modal-header {
            border-bottom: none;
            padding-bottom: 0;
        }

        .confirmation-modal .modal-footer {
            border-top: none;
            padding-top: 0;
        }

        .confirmation-modal .btn-confirm {
            background-color: #ff6b6b;
            color: white;
            border: none;
        }

        .confirmation-modal .btn-cancel {
            background-color: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }

        /* Module details modal */
        .details-modal .modal-content {
            border-radius: 15px;
            overflow: hidden;
        }

        .details-modal .modal-header {
            background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
            color: #2c5282;
            border-bottom: none;
        }

        .details-modal .modal-title {
            font-weight: 600;
        }

        .details-modal .module-description {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .details-modal .module-meta {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .details-modal .meta-item {
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 8px;
        }

        .details-modal .meta-label {
            font-weight: 600;
            color: #555;
            font-size: 0.85rem;
        }

        .details-modal .meta-value {
            font-size: 1rem;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 include '../includes/header.php'; ?>

            <div class="modules-content">
                <div class="modules-header">
                    <h1>Modules Management</h1>
                    <p class="subtitle">Manage all modules across different fields and semesters</p>
                </div>

                <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 endif; ?>

                <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 endif; ?>

                <div class="modules-filters">
                    <form method="GET" action="" class="row g-3">
                        <div class="col-md-5">
                            <label for="field" class="form-label">Filter by Field</label>
                            <select class="form-select" id="field" name="field">
                                <option value="">All Fields</option>
                                <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 foreach ($fields as $field): ?>
                                    <option value="<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $field['id_filiere']; ?>" <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo ($field_filter == $field['id_filiere']) ? 'selected' : ''; ?>>
                                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $field['nom_filiere']; ?>
                                    </option>
                                <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label for="semester" class="form-label">Filter by Semester</label>
                            <select class="form-select" id="semester" name="semester">
                                <option value="">All Semesters</option>
                                <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 foreach ($semesters as $semester): ?>
                                    <option value="<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $semester; ?>" <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo ($semester_filter == $semester) ? 'selected' : ''; ?>>
                                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $semester; ?>
                                    </option>
                                <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn w-100 filter-button">
                                <i class="fas fa-filter me-2"></i>Apply Filters
                            </button>
                        </div>

                        <style>
                            .filter-button {
                                background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
                                color: #2c5282;
                                font-weight: 600;
                                border: none;
                                border-radius: 10px;
                                padding: 10px 15px;
                                box-shadow: 0 4px 10px rgba(161, 196, 253, 0.4);
                                transition: all 0.3s ease;
                                position: relative;
                                overflow: hidden;
                                z-index: 1;
                            }

                            .filter-button:before {
                                content: '';
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                background: linear-gradient(135deg, #c2e9fb, #a1c4fd);
                                opacity: 0;
                                z-index: -1;
                                transition: opacity 0.3s ease;
                            }

                            .filter-button:hover {
                                transform: translateY(-2px);
                                box-shadow: 0 6px 15px rgba(161, 196, 253, 0.5);
                                color: #1a365d;
                            }

                            .filter-button:hover:before {
                                opacity: 1;
                            }

                            .filter-button:active {
                                transform: translateY(1px);
                                box-shadow: 0 2px 5px rgba(161, 196, 253, 0.4);
                            }

                            .filter-button i {
                                font-size: 0.9rem;
                            }
                        </style>
                    </form>
                </div>

                <div class="row g-4">
                    <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 if (empty($modules) || isset($modules['error'])): ?>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo isset($modules['error']) ? $modules['error'] : 'No modules found. Try adjusting your filters.'; ?>
                            </div>
                        </div>
                    <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 else: ?>
                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 foreach ($modules as $index => $module): ?>
                            <div class="col-md-4 mb-4">
                                <div class="module-card" style="background-color: <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $pastelColors[$index % count($pastelColors)]; ?>">

                                    <div class="module-emoji">
                                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo getModuleEmoji($module['nom_module'], $module['nom_filiere']); ?>
                                    </div>

                                    <h3 class="module-title"><?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['nom_module']; ?></h3>

                                    <div class="module-info">
                                        <p><strong>Field:</strong> <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['nom_filiere']; ?></p>
                                        <p><strong>Semester:</strong> <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['semestre']; ?></p>

                                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 if (!empty($module['enseignant_nom'])): ?>
                                            <p><strong>Teacher:</strong> <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['enseignant_nom'] . ' ' . $module['enseignant_prenom']; ?></p>
                                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 endif; ?>

                                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 if (!empty($module['date_ajout'])): ?>
                                            <p><strong>Added on:</strong> <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo date('d/m/Y', strtotime($module['date_ajout'])); ?></p>
                                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 endif; ?>
                                    </div>

                                    <div class="module-actions">
                                        <button class="btn btn-sm view-details"
                                                style="background-color: #e6f7ff; color: #0077cc; border: 1px solid #b3e0ff; box-shadow: 0 2px 4px rgba(0,0,0,0.05);"
                                                data-bs-toggle="modal"
                                                data-bs-target="#detailsModal<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['id_module']; ?>">
                                            <i class="fas fa-info-circle"></i> View Details
                                        </button>
                                        <button class="btn btn-sm btn-delete-module"
                                                style="background-color: #fff0f0; color: #e74c3c; border: 1px solid #ffd6d6; box-shadow: 0 2px 4px rgba(0,0,0,0.05);"
                                                data-bs-toggle="modal"
                                                data-bs-target="#deleteModal<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['id_module']; ?>">
                                            <i class="fas fa-trash-alt"></i> Remove
                                        </button>
                                    </div>
                                </div>

                                <!-- Module Details Modal -->
                                <div class="modal fade details-modal" id="detailsModal<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['id_module']; ?>" tabindex="-1" aria-labelledby="detailsModalLabel<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['id_module']; ?>" aria-hidden="true">
                                    <div class="modal-dialog modal-dialog-centered modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="detailsModalLabel<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['id_module']; ?>">
                                                    <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo getModuleEmoji($module['nom_module'], $module['nom_filiere']); ?>
                                                    <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['nom_module']; ?>
                                                </h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="module-description">
                                                    <h6>Module Description</h6>
                                                    <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 if (!empty($module['description'])): ?>
                                                        <p><?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo nl2br($module['description']); ?></p>
                                                    <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 else: ?>
                                                        <p class="text-muted">No description available for this module.</p>
                                                    <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 endif; ?>
                                                </div>

                                                <div class="module-meta">
                                                    <div class="meta-item">
                                                        <div class="meta-label">Field</div>
                                                        <div class="meta-value"><?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['nom_filiere']; ?></div>
                                                    </div>

                                                    <div class="meta-item">
                                                        <div class="meta-label">Semester</div>
                                                        <div class="meta-value"><?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['semestre']; ?></div>
                                                    </div>

                                                    <div class="meta-item">
                                                        <div class="meta-label">Teacher</div>
                                                        <div class="meta-value">
                                                            <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo !empty($module['enseignant_nom']) ?
                                                                $module['enseignant_nom'] . ' ' . $module['enseignant_prenom'] :
                                                                '<span class="text-muted">Not assigned</span>'; ?>
                                                        </div>
                                                    </div>

                                                    <div class="meta-item">
                                                        <div class="meta-label">Added on</div>
                                                        <div class="meta-value">
                                                            <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo !empty($module['date_ajout']) ?
                                                                date('d/m/Y', strtotime($module['date_ajout'])) :
                                                                '<span class="text-muted">Unknown</span>'; ?>
                                                        </div>
                                                    </div>

                                                    <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 if (!empty($module['actif'])): ?>
                                                    <div class="meta-item">
                                                        <div class="meta-label">Status</div>
                                                        <div class="meta-value">
                                                            <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['actif'] == 1 ?
                                                                '<span class="badge bg-success">Active</span>' :
                                                                '<span class="badge bg-secondary">Inactive</span>'; ?>
                                                        </div>
                                                    </div>
                                                    <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 endif; ?>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn" style="background-color: #e6f7ff; color: #0077cc; border: 1px solid #b3e0ff; font-weight: 500;" data-bs-dismiss="modal">
                                                    <i class="fas fa-check me-1"></i> Done
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete Confirmation Modal -->
                                <div class="modal fade confirmation-modal" id="deleteModal<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['id_module']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['id_module']; ?>" aria-hidden="true">
                                    <div class="modal-dialog modal-dialog-centered">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['id_module']; ?>">Confirm Deletion</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete the module <strong><?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['nom_module']; ?></strong>?</p>
                                                <p class="text-danger">This action cannot be undone.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn" style="background-color: #f8f9fa; color: #555; border: 1px solid #ddd; font-weight: 500;" data-bs-dismiss="modal">
                                                    <i class="fas fa-times me-1"></i> Cancel
                                                </button>
                                                <form method="POST" action="">
                                                    <input type="hidden" name="id_module" value="<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 echo $module['id_module']; ?>">
                                                    <button type="submit" name="delete_module" class="btn" style="background-color: #fff0f0; color: #e74c3c; border: 1px solid #ffd6d6; font-weight: 500;">
                                                        <i class="fas fa-trash-alt me-1"></i> Delete Module
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 endforeach; ?>
                    <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
</body>
</html>