<?php
// Inclure les constantes
require_once __DIR__ . "/../config/constants.php";
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Initialisation de mot de passe - UniAdmin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <style>
        :root {
            --royal-blue: #1a73e8;
            --azure: #3a8ff7;
            --sky-blue: #64b5f6;
            --light-blue: #e8f0fe;
            --navy-blue: #0d47a1;
        }

        body {
            background-color: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .initialize-container {
            max-width: 450px;
            width: 100%;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .card-header {
            background-color: var(--royal-blue);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 20px;
            text-align: center;
        }

        .card-body {
            padding: 30px;
        }

        .form-control {
            border-radius: 5px;
            padding: 12px 15px;
            border: 1px solid #ced4da;
            margin-bottom: 20px;
        }

        .btn-primary {
            background-color: var(--royal-blue);
            border-color: var(--royal-blue);
            border-radius: 5px;
            padding: 12px 15px;
            font-weight: 500;
            width: 100%;
        }

        .btn-primary:hover {
            background-color: var(--navy-blue);
            border-color: var(--navy-blue);
        }

        .alert {
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .logo {
            max-width: 100px;
            margin-bottom: 15px;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
        }

        .password-strength {
            height: 5px;
            margin-top: -15px;
            margin-bottom: 20px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .password-feedback {
            font-size: 12px;
            margin-top: -15px;
            margin-bottom: 20px;
        }

        #initializeSuccess {
            display: none;
            text-align: center;
        }

        #initializeSuccess i {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="initialize-container">
        <div class="card">
            <div class="card-header">
                <img src="<?php echo BASE_URL; ?>/view/assets/img/logo.png" alt="UniAdmin Logo" class="logo">
                <h4>Initialisation de mot de passe</h4>
            </div>
            <div class="card-body">
                <div id="passwordForm">
                    <?php
                    // Vérifier si un token est fourni
                    if (isset($_GET['token'])) {
                        $token = $_GET['token'];
                        require_once "../model/userModel.php";

                        // Vérifier si le token est valide
                        $tokenResult = verifyPasswordResetToken($token);

                        if (isset($tokenResult['error'])) {
                            // Token invalide ou expiré
                            echo '<div class="alert alert-danger" role="alert">';
                            echo '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
                            echo 'Le lien d\'initialisation de mot de passe est invalide ou a expiré. Veuillez contacter l\'administrateur.';
                            echo '</div>';
                        } else {
                            // Token valide, afficher le formulaire
                            $username = $tokenResult['username'];
                            ?>
                            <div class="alert alert-info" role="alert">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                Bienvenue ! Veuillez initialiser votre mot de passe pour accéder à votre compte.
                            </div>

                            <div class="alert alert-danger" id="errorAlert" style="display: none;" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <span id="errorMessage"></span>
                            </div>

                            <form id="initializePasswordForm">
                                <input type="hidden" id="token" value="<?php echo htmlspecialchars($token); ?>">
                                <input type="hidden" id="username" value="<?php echo htmlspecialchars($username); ?>">

                                <div class="mb-3">
                                    <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                                    <div class="password-container">
                                        <input type="password" class="form-control" id="newPassword" name="newPassword" required>
                                        <span class="password-toggle" onclick="togglePassword('newPassword')">
                                            <i class="bi bi-eye"></i>
                                        </span>
                                    </div>
                                    <div class="password-strength" id="passwordStrength"></div>
                                    <div class="password-feedback" id="passwordFeedback"></div>
                                </div>

                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                                    <div class="password-container">
                                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                        <span class="password-toggle" onclick="togglePassword('confirmPassword')">
                                            <i class="bi bi-eye"></i>
                                        </span>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>
                                    Initialiser le mot de passe
                                </button>
                            </form>
                            <?php
                        }
                    } else {
                        // Aucun token fourni
                        echo '<div class="alert alert-danger" role="alert">';
                        echo '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
                        echo 'Aucun token d\'initialisation n\'a été fourni. Veuillez utiliser le lien complet envoyé par email.';
                        echo '</div>';
                    }
                    ?>
                </div>

                <div id="initializeSuccess">
                    <i class="bi bi-check-circle-fill"></i>
                    <h4>Mot de passe initialisé avec succès!</h4>
                    <p class="mb-4">Votre mot de passe a été créé. Vous pouvez maintenant vous connecter avec votre nom d'utilisateur et votre nouveau mot de passe.</p>

                    <a href="<?php echo BASE_URL; ?>/index.php" class="btn btn-primary">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        Se connecter
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const initializePasswordForm = document.getElementById('initializePasswordForm');

            if (initializePasswordForm) {
                const passwordForm = document.getElementById('passwordForm');
                const initializeSuccess = document.getElementById('initializeSuccess');
                const errorAlert = document.getElementById('errorAlert');
                const errorMessage = document.getElementById('errorMessage');
                const passwordStrength = document.getElementById('passwordStrength');
                const passwordFeedback = document.getElementById('passwordFeedback');
                const newPasswordInput = document.getElementById('newPassword');
                const confirmPasswordInput = document.getElementById('confirmPassword');
                const tokenInput = document.getElementById('token');
                const usernameInput = document.getElementById('username');

                // Évaluer la force du mot de passe
                newPasswordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;
                    let feedback = '';

                    if (password.length >= 8) {
                        strength += 25;
                        feedback += '<span class="text-success">✓ Au moins 8 caractères</span><br>';
                    } else {
                        feedback += '<span class="text-danger">✗ Au moins 8 caractères</span><br>';
                    }

                    if (/[A-Z]/.test(password)) {
                        strength += 25;
                        feedback += '<span class="text-success">✓ Au moins une majuscule</span><br>';
                    } else {
                        feedback += '<span class="text-danger">✗ Au moins une majuscule</span><br>';
                    }

                    if (/[0-9]/.test(password)) {
                        strength += 25;
                        feedback += '<span class="text-success">✓ Au moins un chiffre</span><br>';
                    } else {
                        feedback += '<span class="text-danger">✗ Au moins un chiffre</span><br>';
                    }

                    if (/[^A-Za-z0-9]/.test(password)) {
                        strength += 25;
                        feedback += '<span class="text-success">✓ Au moins un caractère spécial</span>';
                    } else {
                        feedback += '<span class="text-danger">✗ Au moins un caractère spécial</span>';
                    }

                    // Définir la couleur en fonction de la force
                    let color = '';
                    if (strength <= 25) {
                        color = '#dc3545'; // Rouge
                    } else if (strength <= 50) {
                        color = '#ffc107'; // Jaune
                    } else if (strength <= 75) {
                        color = '#fd7e14'; // Orange
                    } else {
                        color = '#28a745'; // Vert
                    }

                    passwordStrength.style.width = strength + '%';
                    passwordStrength.style.backgroundColor = color;
                    passwordFeedback.innerHTML = feedback;
                });

                // Gérer la soumission du formulaire
                initializePasswordForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const token = tokenInput.value;
                    const username = usernameInput.value;
                    const newPassword = newPasswordInput.value;
                    const confirmPassword = confirmPasswordInput.value;

                    // Validation des mots de passe
                    if (newPassword.length < 8) {
                        showError('Le mot de passe doit contenir au moins 8 caractères.');
                        return;
                    }

                    if (newPassword !== confirmPassword) {
                        showError('Les mots de passe ne correspondent pas.');
                        return;
                    }

                    // Vérifier la complexité du mot de passe
                    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
                    if (!passwordRegex.test(newPassword)) {
                        showError('Le mot de passe doit contenir au moins une majuscule, un chiffre et un caractère spécial.');
                        return;
                    }

                    // Désactiver le bouton pendant la requête
                    const submitBtn = initializePasswordForm.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Initialisation en cours...';

                    // Masquer les erreurs précédentes
                    errorAlert.style.display = 'none';

                    // Envoyer la requête
                    fetch('../route/userRoute.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            action: 'resetPassword',
                            token: token,
                            username: username,
                            password: newPassword,
                            confirmPassword: confirmPassword
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = '<i class="bi bi-check-circle me-2"></i>Initialiser le mot de passe';

                        if (data.success) {
                            // Afficher le message de succès
                            passwordForm.style.display = 'none';
                            initializeSuccess.style.display = 'block';
                        } else {
                            showError(data.error || 'Une erreur est survenue lors de l\'initialisation du mot de passe.');
                        }
                    })
                    .catch(error => {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = '<i class="bi bi-check-circle me-2"></i>Initialiser le mot de passe';
                        showError('Une erreur est survenue. Veuillez réessayer plus tard.');
                        console.error('Error:', error);
                    });
                });

                function showError(message) {
                    errorMessage.textContent = message;
                    errorAlert.style.display = 'block';
                }
            }
        });

        // Fonction pour afficher/masquer le mot de passe
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        }
    </script>
</body>
</html>
