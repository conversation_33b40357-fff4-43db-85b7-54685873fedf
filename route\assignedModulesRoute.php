<?php
/**
 * Route pour les modules assignés aux enseignants
 *
 * Ce fichier gère les routes pour les requêtes liées aux modules assignés aux enseignants.
 */

// Inclure les contrôleurs
require_once __DIR__ . '/../controller/assignedModulesController.php';
require_once __DIR__ . '/../controller/commonController.php';

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user'])) {
    header('Location: ../index.php');
    exit;
}

// Désactiver l'affichage des erreurs PHP
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Définir un gestionnaire d'erreurs personnalisé
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("Erreur PHP: [$errno] $errstr dans $errfile à la ligne $errline");
    jsonResponse(['error' => 'Une erreur interne est survenue. Veuillez contacter l\'administrateur.'], 500);
    exit;
});

try {
    // Traiter la requête en fonction de l'action demandée
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Récupérer l'action demandée
        $action = isset($_GET['action']) ? $_GET['action'] : '';

        switch ($action) {
            case 'getAssignedModules':
                // Récupérer l'ID de l'enseignant
                $teacherId = isset($_GET['teacherId']) ? $_GET['teacherId'] :
                            (isset($_SESSION['user']['teacher_id']) ? $_SESSION['user']['teacher_id'] : null);

                // Récupérer les filtres
                $filters = [
                    'semester' => isset($_GET['semester']) ? $_GET['semester'] : '',
                    'level' => isset($_GET['level']) ? $_GET['level'] : '',
                    'filiere' => isset($_GET['filiere']) ? $_GET['filiere'] : '',
                    'type' => isset($_GET['type']) ? $_GET['type'] : '',
                    'academic_year' => isset($_GET['academic_year']) ? $_GET['academic_year'] : ''
                ];

                // Appeler l'API
                getAssignedModulesAPI($teacherId, $filters);
                break;

            case 'getSemesters':
                // Appeler l'API pour récupérer les semestres
                getSemestersAPI();
                break;

            case 'getLevels':
                // Appeler l'API pour récupérer les niveaux
                getLevelsAPI();
                break;

            case 'getFilieres':
                // Appeler l'API pour récupérer les filières
                getFilieresAPI();
                break;

            case 'getUETypes':
                // Appeler l'API pour récupérer les types d'UE
                getUETypesAPI();
                break;

            case 'getAcademicYears':
                // Récupérer le nombre d'années à générer
                $count = isset($_GET['count']) ? intval($_GET['count']) : 6;

                // Appeler l'API pour récupérer les années académiques
                getAcademicYearsAPI($count);
                break;

            default:
                // Action non reconnue
                jsonResponse(['error' => 'Action non reconnue'], 400);
                break;
        }
    } else {
        // Méthode HTTP non autorisée
        jsonResponse(['error' => 'Méthode non autorisée'], 405);
    }
} catch (Exception $e) {
    // Journaliser l'erreur
    error_log("Exception dans assignedModulesRoute.php: " . $e->getMessage());

    // Renvoyer une réponse JSON avec l'erreur
    jsonResponse(['error' => 'Une erreur est survenue lors du traitement de la requête: ' . $e->getMessage()], 500);
}
