<?php
// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Fonction pour obtenir une connexion à la base de données
function getConnection() {
    $servername = "localhost";
    $username = "root";
    $password = "";
    $dbname = "projet_web";

    // Créer une connexion
    $conn = new mysqli($servername, $username, $password, $dbname);

    // Vérifier la connexion
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}

// Obtenir une connexion à la base de données
$conn = getConnection();

// Afficher les tables de la base de données
echo "<h2>Tables in Database</h2>";
$result = $conn->query("SHOW TABLES");
if ($result->num_rows > 0) {
    echo "<ul>";
    while ($row = $result->fetch_row()) {
        $tableName = $row[0];
        echo "<li><strong>$tableName</strong>";
        
        // Afficher la structure de la table
        $columnsResult = $conn->query("DESCRIBE $tableName");
        if ($columnsResult->num_rows > 0) {
            echo "<ul>";
            while ($column = $columnsResult->fetch_assoc()) {
                echo "<li>{$column['Field']} ({$column['Type']})</li>";
            }
            echo "</ul>";
        }
        
        // Afficher quelques exemples de données
        $dataResult = $conn->query("SELECT * FROM $tableName LIMIT 5");
        if ($dataResult->num_rows > 0) {
            echo "<h4>Sample Data:</h4>";
            echo "<table border='1'>";
            
            // En-têtes de colonnes
            echo "<tr>";
            $fields = $dataResult->fetch_fields();
            foreach ($fields as $field) {
                echo "<th>{$field->name}</th>";
            }
            echo "</tr>";
            
            // Réinitialiser le pointeur de résultat
            $dataResult->data_seek(0);
            
            // Données
            while ($dataRow = $dataResult->fetch_assoc()) {
                echo "<tr>";
                foreach ($dataRow as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            
            echo "</table>";
        }
        
        echo "</li>";
    }
    echo "</ul>";
} else {
    echo "No tables found in database.";
}

// Fermer la connexion
$conn->close();
?>
