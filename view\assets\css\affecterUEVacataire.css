/**
 * CSS for Affecter UE Vacataire page
 * Styling for teaching unit assignment to part-time lecturers
 */

/* Page Title Styling */
.page-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.page-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 2rem;
}

.title-underline {
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.page-subtitle {
    font-size: 1rem;
    margin-bottom: 0;
}

/* Statistics Cards */
.stat-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    overflow: hidden;
    position: relative;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--stat-color);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}

.stat-card.stat-vacant {
    --stat-color: #e74c3c;
}

.stat-card.stat-assigned {
    --stat-color: #27ae60;
}

.stat-card.stat-unassigned {
    --stat-color: #f39c12;
}

.stat-card.stat-vacataires {
    --stat-color: #3498db;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
    color: white;
    background: var(--stat-color);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0;
    color: #2c3e50;
}

.stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0;
    font-weight: 500;
}

/* Form Styling */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.card-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 12px 12px 0 0 !important;
    padding: 1.25rem;
}

.card-header h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0;
}

/* Teaching Units Container */
.teaching-units-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    background-color: #f8f9fa;
}

/* UE Cards */
.ue-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
}

.ue-card:hover {
    border-color: #4e73df;
    box-shadow: 0 2px 8px rgba(78, 115, 223, 0.15);
    transform: translateY(-1px);
}

.ue-card.selected {
    border-color: #27ae60;
    background: linear-gradient(135deg, #ffffff 0%, #f0f9f4 100%);
    box-shadow: 0 3px 12px rgba(39, 174, 96, 0.2);
}

.ue-card .card-body {
    padding: 1rem;
}

.form-check {
    margin-bottom: 0;
}

.form-check-input {
    margin-top: 0.1rem;
}

.form-check-label {
    cursor: pointer;
    margin-left: 0.5rem;
}

/* UE Info Styling */
.ue-info {
    width: 100%;
}

.ue-module {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.ue-details {
    margin-bottom: 0.5rem;
}

.ue-details .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.ue-meta {
    font-size: 0.8rem;
}

/* Badge Styling */
.bg-primary-light {
    background-color: rgba(78, 115, 223, 0.1) !important;
}

.text-primary {
    color: #4e73df !important;
}

.bg-info-light {
    background-color: rgba(54, 185, 204, 0.1) !important;
}

.text-info {
    color: #36b9cc !important;
}

/* Button Styling */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
    box-shadow: 0 2px 6px rgba(78, 115, 223, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #224abe 0%, #1e3a8a 100%);
    box-shadow: 0 4px 12px rgba(78, 115, 223, 0.4);
    transform: translateY(-1px);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-1px);
}

.btn-outline-primary {
    border-color: #4e73df;
    color: #4e73df;
}

.btn-outline-primary:hover {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* Form Controls */
.form-select, .form-control {
    border-radius: 8px;
    border: 1px solid #d1d3e2;
    padding: 0.6rem 0.75rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Alert Styling */
.alert {
    border: none;
    border-radius: 8px;
    padding: 1rem 1.25rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 1.5rem;
    }

    .stat-card {
        margin-bottom: 1rem;
    }

    .ue-card {
        margin-bottom: 1rem;
    }

    .teaching-units-container {
        max-height: 300px;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
    }
}

/* Loading Animation */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Scrollbar Styling for Teaching Units Container */
.teaching-units-container::-webkit-scrollbar {
    width: 6px;
}

.teaching-units-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.teaching-units-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.teaching-units-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation for page load */
.card, .stat-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stagger animation for cards */
.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
