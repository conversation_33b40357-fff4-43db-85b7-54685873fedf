<?php
// Désactiver le cache
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Inclure l'utilitaire de chemin
require_once __DIR__ . '/../utils/path_utils.php';

// Fonction pour gérer les erreurs
function handleError($message) {
    header('Content-Type: text/plain; charset=utf-8');
    echo "Erreur: " . $message;
    exit;
}

// Vérifier si le paramètre file est présent
if (!isset($_GET['file'])) {
    handleError("Aucun fichier spécifié");
}

// Récupérer le chemin du fichier
$filePath = urldecode($_GET['file']);

// Vérifier que le chemin est dans le dossier uploads/messages
if (strpos($filePath, 'uploads/messages/') !== 0) {
    handleError("Chemin de fichier non autorisé");
}

// Déterminer les chemins possibles pour le fichier
$basePath = dirname(__DIR__); // Chemin de base du projet (un niveau au-dessus du dossier route)
$possiblePaths = [
    $basePath . '/' . $filePath, // Chemin relatif à la racine du projet
    __DIR__ . '/../' . $filePath // Chemin relatif au dossier route
];

// Trouver le premier chemin qui existe
$fullPath = null;
foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        $fullPath = $path;
        break;
    }
}

// Vérifier que le fichier existe
if ($fullPath === null) {
    handleError("Le fichier n'existe pas. Chemins vérifiés: " . implode(", ", $possiblePaths));
}

// Récupérer le nom du fichier
$fileName = basename($fullPath);

// Récupérer l'extension du fichier
$fileExtension = pathinfo($fullPath, PATHINFO_EXTENSION);

// Définir le type MIME en fonction de l'extension
$mimeTypes = [
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls' => 'application/vnd.ms-excel',
    'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt' => 'application/vnd.ms-powerpoint',
    'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'txt' => 'text/plain',
    'zip' => 'application/zip',
    'rar' => 'application/x-rar-compressed',
    'mp3' => 'audio/mpeg',
    'mp4' => 'video/mp4',
];

$contentType = isset($mimeTypes[strtolower($fileExtension)])
    ? $mimeTypes[strtolower($fileExtension)]
    : 'application/octet-stream';

// Définir les en-têtes pour le téléchargement
header('Content-Description: File Transfer');
header('Content-Type: ' . $contentType);
header('Content-Disposition: attachment; filename="' . $fileName . '"');
header('Content-Transfer-Encoding: binary');
header('Expires: 0');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Pragma: public');
header('Content-Length: ' . filesize($fullPath));

// Lire et envoyer le fichier
readfile($fullPath);
exit;
