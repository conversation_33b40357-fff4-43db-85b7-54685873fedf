/* Style personnalisé pour le dashboard avec des couleurs pastel */

:root {
    --pastel-blue: #a8d8ea;
    --pastel-green: #aaf683;
    --pastel-yellow: #fff9a5;
    --pastel-orange: #ffcba5;
    --pastel-pink: #ffb7ce;
    --pastel-purple: #d3b5e5;
    --pastel-teal: #a0ded6;
    --pastel-red: #ffb3b3;
    --light-gray: #f8f9fa;
    --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

/* Style général du dashboard */
.dashboard-container {
    background-color: var(--light-gray);
    min-height: 100vh;
}



/* Style des cartes */
.dashboard-card {
    border-radius: 12px;
    border: none;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

/* Style pour les cartes carrées */
.square-card {
    aspect-ratio: 1 / 1;
}

.square-card .card-body {
    padding: 1rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.square-card .card-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-right: 0;
    margin-bottom: 1rem;
}

/* Couleurs des cartes */
.card-events {
    border-top: 4px solid var(--pastel-blue);
}

.card-notifications {
    border-top: 4px solid var(--pastel-orange);
}

.card-stats {
    border-top: 4px solid var(--pastel-green);
}

.card-schedule {
    border-top: 4px solid var(--pastel-teal);
}

.card-grades {
    border-top: 4px solid var(--pastel-red);
}

.card-staff {
    border-top: 4px solid var(--pastel-purple);
}

.card-request {
    border-top: 4px solid var(--pastel-yellow);
}

.card-visits {
    border-top: 4px solid var(--pastel-blue);
}

/* Icônes */
.card-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    margin-right: 15px;
}

.icon-events {
    background-color: rgba(168, 216, 234, 0.2);
    color: #4a9cc9;
}

.icon-notifications {
    background-color: rgba(255, 203, 165, 0.2);
    color: #ff8c42;
}

.icon-stats {
    background-color: rgba(170, 246, 131, 0.2);
    color: #5cb85c;
}

.icon-schedule {
    background-color: rgba(160, 222, 214, 0.2);
    color: #20b2aa;
}

.icon-grades {
    background-color: rgba(255, 179, 179, 0.2);
    color: #d9534f;
}

.icon-staff {
    background-color: rgba(211, 181, 229, 0.2);
    color: #8a5cb3;
}

.icon-request {
    background-color: rgba(255, 249, 165, 0.2);
    color: #d4b400;
}

.icon-visits {
    background-color: rgba(168, 216, 234, 0.2);
    color: #4a9cc9;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }
.delay-6 { animation-delay: 0.6s; }
.delay-7 { animation-delay: 0.7s; }
.delay-8 { animation-delay: 0.8s; }

/* Style des listes */
.dashboard-list {
    list-style: none;
    padding-left: 0;
}

.dashboard-list li {
    padding: 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
}

.dashboard-list li:last-child {
    border-bottom: none;
}

.dashboard-list .date-badge {
    background-color: var(--pastel-blue);
    color: #333;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-left: auto;
}

.dashboard-list .notification-badge {
    background-color: var(--pastel-orange);
    color: #333;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-left: auto;
}

/* Style des statistiques */
.stat-value {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0;
    color: #333;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.stat-card {
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

.stat-students {
    background-color: rgba(168, 216, 234, 0.2);
}

.stat-professors {
    background-color: rgba(211, 181, 229, 0.2);
}

.stat-departments {
    background-color: rgba(170, 246, 131, 0.2);
}

/* Titre de la page */
.page-title {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
    font-weight: 600;
    color: #333;
}

.page-title:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -8px;
    width: 40px;
    height: 4px;
    background: linear-gradient(to right, var(--pastel-blue), var(--pastel-purple));
    border-radius: 2px;
}

/* Styles pour le graphique */
.chart-container {
    margin-top: 20px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

#chart-type-selector {
    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 4px 12px;
    font-size: 0.85rem;
    background-color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

#chart-type-selector:focus {
    box-shadow: 0 0 0 0.2rem rgba(168, 216, 234, 0.25);
    border-color: var(--pastel-blue);
}

/* Responsive */
@media (max-width: 768px) {
    .card-body {
        padding: 15px;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .chart-container {
        height: 250px !important;
    }
}
