<?php
// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Définir le type de contenu comme JSON
header('Content-Type: application/json');

// Fonction pour obtenir une connexion à la base de données
function getConnection() {
    $servername = "localhost";
    $username = "root";
    $password = "";

    // Essayer de se connecter sans spécifier de base de données
    $conn = new mysqli($servername, $username, $password);

    // Vérifier la connexion
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // Lister les bases de données disponibles
    $result = $conn->query("SHOW DATABASES");
    $databases = [];
    while ($row = $result->fetch_row()) {
        $databases[] = $row[0];
    }

    // Essayer de trouver une base de données qui pourrait contenir nos tables
    $possibleDatabases = ['ensah'];
    $selectedDb = null;

    foreach ($possibleDatabases as $db) {
        if (in_array($db, $databases)) {
            $selectedDb = $db;
            break;
        }
    }

   
    // Si une base de données a été trouvée, l'utiliser
    if ($selectedDb !== null) {
        $conn->select_db($selectedDb);
    } else {
        // Sinon, utiliser des données simulées
        return null;
    }

    return $conn;
}

try {
    // Vérifier si la requête est une requête GET
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Vérifier si un terme de recherche a été fourni
        if (isset($_GET['query']) && !empty($_GET['query'])) {
            $query = $_GET['query'];
            $users = [];

            // Obtenir une connexion à la base de données
            $conn = getConnection();
            $users = [];

            // Si la connexion a réussi, rechercher dans la base de données
            if ($conn !== null) {
                // Tableau pour stocker les requêtes SQL à exécuter
                $queries = [];

                // Vérifier quelles tables existent
                $tables = [];
                $tablesResult = $conn->query("SHOW TABLES");
                while ($tableRow = $tablesResult->fetch_row()) {
                    $tables[] = $tableRow[0];
                }

            // Rechercher dans la table users si elle existe
            if (in_array('users', $tables)) {
                $queries[] = [
                    'sql' => "SELECT id_user as id, username, 'user' as role FROM users WHERE username LIKE ?",
                    'params' => ['s', '%' . $query . '%']
                ];
            }

            // Rechercher dans la table admin si elle existe
            if (in_array('admin', $tables)) {
                $queries[] = [
                    'sql' => "SELECT id as id, username, 'admin' as role FROM admin WHERE username LIKE ?",
                    'params' => ['s', '%' . $query . '%']
                ];
            }

            // Rechercher dans la table etudiant si elle existe
            if (in_array('etudiant', $tables)) {
                $queries[] = [
                    'sql' => "SELECT id_etudiant as id, CONCAT(prenom, ' ', nom) as username, 'student' as role FROM etudiant WHERE nom LIKE ? OR prenom LIKE ?",
                    'params' => ['ss', '%' . $query . '%', '%' . $query . '%']
                ];
            }

            // Rechercher dans la table enseignant si elle existe
            if (in_array('enseignant', $tables)) {
                $queries[] = [
                    'sql' => "SELECT id_enseignant as id, CONCAT(prenom, ' ', nom) as username, 'teacher' as role FROM enseignant WHERE nom LIKE ? OR prenom LIKE ?",
                    'params' => ['ss', '%' . $query . '%', '%' . $query . '%']
                ];
            }

            // Exécuter chaque requête
            foreach ($queries as $queryInfo) {
                try {
                    $stmt = $conn->prepare($queryInfo['sql']);
                    if ($stmt) {
                        // Extraire les paramètres
                        $paramTypes = $queryInfo['params'][0];
                        $paramValues = array_slice($queryInfo['params'], 1);

                        // Créer un tableau de références pour bind_param
                        $params = [$paramTypes];
                        foreach ($paramValues as $key => $value) {
                            $params[] = &$paramValues[$key];
                        }

                        // Appeler bind_param avec les références
                        call_user_func_array([$stmt, 'bind_param'], $params);

                        // Exécuter la requête
                        $stmt->execute();
                        $result = $stmt->get_result();

                        // Récupérer les résultats
                        while ($row = $result->fetch_assoc()) {
                            $users[] = $row;
                        }

                        $stmt->close();
                    }
                } catch (Exception $e) {
                    // Ignorer les erreurs et continuer avec les autres requêtes
                    error_log("Error executing query: " . $e->getMessage());
                }
            }

                // Fermer la connexion si elle existe
                if ($conn !== null) {
                    $conn->close();
                }
            } // Fin du if ($conn !== null)

            // Si aucun utilisateur n'a été trouvé ou si la connexion a échoué, utiliser des données simulées
            if (empty($users)) {
                // Données simulées en cas d'absence de résultats
                $mockUsers = [
                    ['id' => 1, 'username' => 'admin', 'role' => 'admin'],
                    ['id' => 2, 'username' => 'teacher1', 'role' => 'teacher'],
                    ['id' => 3, 'username' => 'teacher2', 'role' => 'teacher'],
                    ['id' => 4, 'username' => 'student1', 'role' => 'student'],
                    ['id' => 5, 'username' => 'student2', 'role' => 'student'],
                    ['id' => 6, 'username' => 'salma', 'role' => 'student'],
                    ['id' => 7, 'username' => 'salmane', 'role' => 'teacher'],
                    ['id' => 8, 'username' => 'salim', 'role' => 'admin'],
                    ['id' => 9, 'username' => 'john.doe', 'role' => 'user'],
                    ['id' => 10, 'username' => 'jane.smith', 'role' => 'user']
                ];

                // Filtrer les utilisateurs simulés qui correspondent à la requête
                $filteredUsers = array_filter($mockUsers, function($user) use ($query) {
                    return strpos(strtolower($user['username']), strtolower($query)) !== false;
                });

                $users = array_values($filteredUsers);
            }

            // Retourner les résultats au format JSON
            echo json_encode([
                'success' => true,
                'users' => $users
            ]);
        } else {
            // Aucun terme de recherche fourni
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'No search query provided'
            ]);
        }
    } else {
        // Méthode non autorisée
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
} catch (Exception $e) {
    // Erreur générale
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
