<?php
/**
 * Utilitaire pour envoyer des emails via SendGrid
 */

/**
 * Envoie un email via l'API SendGrid
 *
 * @param string $to Adresse email du destinataire
 * @param string $subject Sujet de l'email
 * @param string $message Corps de l'email
 * @param array $attachments Pièces jointes (optionnel)
 * @return array Résultat de l'envoi avec clés 'success' et 'message'
 */
function sendEmailViaSendGrid($to, $subject, $message, $attachments = []) {
    // Récupérer la clé API SendGrid depuis la configuration
    if (!defined('SENDGRID_API_KEY') || empty(SENDGRID_API_KEY)) {
        error_log("Erreur: Clé API SendGrid non définie ou vide");
        return [
            'success' => false,
            'message' => 'Clé API SendGrid non définie ou vide'
        ];
    }

    $apiKey = SENDGRID_API_KEY; // Clé API SendGrid (nom de clé: ensah-key)
    $url = 'https://api.sendgrid.com/v3/mail/send';

    // Journaliser les informations d'envoi d'email
    error_log("Tentative d'envoi d'email via SendGrid à: " . $to . " - " . date('Y-m-d H:i:s'));

    // Extraire le code de réinitialisation pour le débogage
    if (strpos($subject, "Réinitialisation") !== false) {
        preg_match('/<div class=\'code\'>(.*?)<\/div>/', $message, $matches);
        if (isset($matches[1])) {
            $resetCode = $matches[1];
            // Stocker le code dans la session pour le débogage
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['debug_reset_code'] = $resetCode;
            error_log("Code de réinitialisation: " . $resetCode);
        }
    }

    // Si la simulation est activée, ne pas envoyer réellement l'email
    if ((defined('MAIL_SIMULATE') && MAIL_SIMULATE === true) || (defined('MAIL_SIMULATE_OVERRIDE') && MAIL_SIMULATE_OVERRIDE === true)) {
        error_log("Email simulé (MAIL_SIMULATE=true dans sendgridSender.php)");
        error_log("À: " . $to);
        error_log("Sujet: " . $subject);
        error_log("Message: " . substr($message, 0, 100) . "...");
        return [
            'success' => true,
            'message' => 'Email simulé (mode simulation)'
        ];
    }

    // Journaliser les informations de configuration pour le débogage
    error_log("Configuration SendGrid:");
    error_log("API Key: " . substr($apiKey, 0, 10) . "...");
    error_log("From Email: " . (defined('MAIL_FROM_ADDRESS') ? MAIL_FROM_ADDRESS : 'non définie'));
    error_log("From Name: " . (defined('MAIL_FROM_NAME') ? MAIL_FROM_NAME : 'non défini'));

    // Vérifier que l'adresse email d'expéditeur est définie
    $fromEmail = defined('MAIL_FROM_ADDRESS') ? MAIL_FROM_ADDRESS : '<EMAIL>';
    $fromName = defined('MAIL_FROM_NAME') ? MAIL_FROM_NAME : 'ENSAH';

    error_log("Utilisation de l'adresse d'expéditeur: " . $fromEmail);

    $data = [
        'personalizations' => [
            [
                'to' => [['email' => $to]]
            ]
        ],
        'from' => [
            'email' => $fromEmail,
            'name' => $fromName
        ],
        'subject' => $subject,
        'content' => [
            ['type' => 'text/html', 'value' => $message]
        ]
    ];

    // Ajouter des pièces jointes si nécessaire
    if (!empty($attachments)) {
        $data['attachments'] = [];
        foreach ($attachments as $attachment) {
            if (isset($attachment['path']) && file_exists($attachment['path'])) {
                $content = base64_encode(file_get_contents($attachment['path']));
                $filename = isset($attachment['name']) ? $attachment['name'] : basename($attachment['path']);
                $data['attachments'][] = [
                    'content' => $content,
                    'filename' => $filename,
                    'type' => mime_content_type($attachment['path']),
                    'disposition' => 'attachment'
                ];
            }
        }
    }

    $headers = [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json'
    ];

    // Vérifier si cURL est disponible
    if (!function_exists('curl_init')) {
        error_log("cURL n'est pas disponible. Impossible d'envoyer l'email via SendGrid.");
        return [
            'success' => false,
            'message' => "cURL n'est pas disponible. Impossible d'envoyer l'email via SendGrid."
        ];
    }

    try {
        // Journaliser les données envoyées à SendGrid pour le débogage
        $jsonData = json_encode($data);
        error_log("Données envoyées à SendGrid: " . substr($jsonData, 0, 500) . (strlen($jsonData) > 500 ? "..." : ""));

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Désactiver la vérification SSL pour le développement
        curl_setopt($ch, CURLOPT_VERBOSE, true); // Activer le mode verbose pour le débogage

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Erreur cURL: ' . curl_error($ch);
            error_log($error);
            curl_close($ch);
            return [
                'success' => false,
                'message' => $error
            ];
        }

        curl_close($ch);

        // Journaliser la réponse complète pour le débogage
        error_log('Réponse SendGrid: Code ' . $httpCode . ' - ' . $response);

        if ($httpCode == 202) {
            error_log('Email envoyé avec succès via SendGrid à: ' . $to);

            // Stocker les informations dans la session pour la page de test
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['last_email_sent'] = [
                'to' => $to,
                'subject' => $subject,
                'date' => date('Y-m-d H:i:s'),
                'success' => true
            ];

            return [
                'success' => true,
                'message' => 'Email envoyé avec succès via SendGrid'
            ];
        } else {
            // Analyser la réponse JSON pour obtenir des détails sur l'erreur
            $errorDetails = '';
            $responseData = json_decode($response, true);

            if (json_last_error() === JSON_ERROR_NONE && isset($responseData['errors'])) {
                foreach ($responseData['errors'] as $err) {
                    $errorDetails .= "\n- " . (isset($err['message']) ? $err['message'] : 'Unknown error');
                    if (isset($err['field'])) {
                        $errorDetails .= " (Field: " . $err['field'] . ")";
                    }
                }
            }

            $error = 'Erreur SendGrid: Code ' . $httpCode . ' - ' . $response . $errorDetails;
            error_log($error);

            // Journaliser des informations supplémentaires pour le débogage
            error_log("Détails de la requête SendGrid:");
            error_log("URL: " . $url);
            error_log("Headers: " . json_encode($headers));
            error_log("From Email: " . (defined('MAIL_FROM_ADDRESS') ? MAIL_FROM_ADDRESS : 'non définie'));
            error_log("To Email: " . $to);

            // Stocker les informations d'erreur dans la session pour la page de test
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['last_email_sent'] = [
                'to' => $to,
                'subject' => $subject,
                'date' => date('Y-m-d H:i:s'),
                'success' => false,
                'error' => $error,
                'http_code' => $httpCode,
                'response' => $response
            ];

            return [
                'success' => false,
                'message' => $error
            ];
        }
    } catch (Exception $e) {
        $error = 'Exception lors de l\'envoi d\'email via SendGrid: ' . $e->getMessage();
        error_log($error);
        return [
            'success' => false,
            'message' => $error
        ];
    }
}

/**
 * Envoie un email de réinitialisation de mot de passe via SendGrid
 *
 * @param string $to Adresse email du destinataire
 * @param string $code Code de réinitialisation
 * @return bool Succès de l'envoi
 */
function sendPasswordResetEmailViaSendGrid($to, $code) {
    $subject = "Réinitialisation de votre mot de passe ENSAH";

    $message = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #1a73e8; color: white; padding: 10px 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; border: 1px solid #ddd; }
            .code { font-size: 24px; font-weight: bold; text-align: center; margin: 20px 0; letter-spacing: 5px; color: #1a73e8; }
            .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #777; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>Réinitialisation de mot de passe</h2>
            </div>
            <div class='content'>
                <p>Bonjour,</p>
                <p>Vous avez demandé la réinitialisation de votre mot de passe. Veuillez utiliser le code suivant pour confirmer votre identité :</p>
                <div class='code'>$code</div>
                <p>Ce code est valable pendant 15 minutes. Si vous n'avez pas demandé cette réinitialisation, veuillez ignorer cet email.</p>
                <p>Cordialement,<br>L'équipe ENSAH</p>
            </div>
            <div class='footer'>
                <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
            </div>
        </div>
    </body>
    </html>
    ";

    return sendEmailViaSendGrid($to, $subject, $message);
}
?>
