# 📋 Système de Gestion des Services - Processus Détaillé

## 🗄️ **Structure de Stockage des Données**

### **Table Principale : `service_management`**

```sql
CREATE TABLE service_management (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL UNIQUE,        -- Nom du service
    service_key VARCHAR(50) NOT NULL UNIQUE,          -- Clé unique du service
    display_name VARCHAR(150) NOT NULL,               -- Nom d'affichage
    description TEXT,                                 -- Description du service
    is_active BOOLEAN DEFAULT FALSE,                  -- État actuel (actif/inactif)
    start_time DATETIME NULL,                         -- Heure de début d'activation
    end_time DATETIME NULL,                           -- Heure de fin programmée
    duration_hours INT NULL,                          -- Durée en heures
    auto_deactivate BOOLEAN DEFAULT TRUE,             -- Désactivation automatique
    created_by VARCHAR(50) NULL,                      -- C<PERSON>é par
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- Date de création
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_activated_at DATETIME NULL,                  -- Dernière activation
    last_deactivated_at DATETIME NULL,                -- Dernière désactivation
    activation_count INT DEFAULT 0,                   -- Nombre d'activations
    settings JSON NULL                                -- Paramètres spécifiques
);
```

### **Table de Journalisation : `service_logs`**

```sql
CREATE TABLE service_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_key VARCHAR(50) NOT NULL,                 -- Clé du service
    action VARCHAR(50) NOT NULL,                      -- Action (activated/deactivated/extended)
    performed_by VARCHAR(50) NULL,                    -- Utilisateur qui a effectué l'action
    details JSON NULL,                                -- Détails de l'action
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP    -- Horodatage
);
```

## 🔄 **Processus d'Activation**

### **1. Déclenchement de l'Activation**
```javascript
// Interface utilisateur
activateService(serviceKey) {
    // Récupération des paramètres
    const durationHours = parseInt(durationInput.value);
    const endTime = endTimeInput.value;

    // Validation
    if (!durationHours && !endTime) {
        showAlert('Durée ou heure de fin requise', 'warning');
        return;
    }

    // Appel API
    fetch('/route/serviceManagementRoute.php?action=activate', {
        method: 'POST',
        body: JSON.stringify({
            service_key: serviceKey,
            duration_hours: durationHours,
            end_time: endTime,
            activated_by: 'admin'
        })
    });
}
```

### **2. Traitement Backend**
```php
function activateService($serviceKey, $durationHours = null, $endTime = null, $activatedBy = null) {
    // 1. Connexion à la base de données
    $conn = getConnection();

    // 2. Début de transaction
    mysqli_begin_transaction($conn);

    // 3. Calcul de l'heure de fin
    $now = date('Y-m-d H:i:s');
    if ($endTime) {
        $calculatedEndTime = $endTime;
    } elseif ($durationHours) {
        $calculatedEndTime = date('Y-m-d H:i:s', strtotime("+$durationHours hours"));
    }

    // 4. Mise à jour de la base de données
    $updateQuery = "UPDATE service_management
                   SET is_active = TRUE,
                       start_time = '$now',
                       end_time = '$calculatedEndTime',
                       duration_hours = $durationHours,
                       last_activated_at = '$now',
                       activation_count = activation_count + 1
                   WHERE service_key = '$serviceKey'";

    // 5. Journalisation
    logServiceAction($serviceKey, 'activated', $activatedBy, [
        'duration_hours' => $durationHours,
        'end_time' => $calculatedEndTime
    ]);

    // 6. Validation de la transaction
    mysqli_commit($conn);
}
```

### **3. Données Stockées lors de l'Activation**
- **`is_active`** : `TRUE`
- **`start_time`** : Horodatage actuel
- **`end_time`** : Heure de fin calculée
- **`duration_hours`** : Durée spécifiée
- **`last_activated_at`** : Horodatage actuel
- **`activation_count`** : Incrémenté de 1
- **`updated_at`** : Horodatage actuel

## ⏰ **Gestion du Temps**

### **1. Calcul du Temps Restant**
```php
function calculateRemainingTime($endTime) {
    $now = new DateTime();
    $endDateTime = new DateTime($endTime);

    if ($endDateTime > $now) {
        $interval = $now->diff($endDateTime);
        return [
            'days' => $interval->days,
            'hours' => $interval->h,
            'minutes' => $interval->i,
            'total_minutes' => ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i
        ];
    }

    return null; // Service expiré
}
```

### **2. Vérification d'Expiration**
```php
function isServiceActive($serviceKey) {
    $service = getServiceByKey($serviceKey);

    if (!$service || !$service['is_active']) {
        return false;
    }

    // Vérification d'expiration
    if ($service['end_time']) {
        $now = new DateTime();
        $endTime = new DateTime($service['end_time']);

        if ($endTime <= $now) {
            // Service expiré - désactivation automatique
            deactivateService($serviceKey, 'auto_expired', 'system');
            return false;
        }
    }

    return true;
}
```

### **3. Processus de Désactivation Automatique**
```php
function checkExpiredServices() {
    $now = date('Y-m-d H:i:s');
    $query = "SELECT service_key FROM service_management
              WHERE is_active = TRUE
              AND end_time IS NOT NULL
              AND end_time <= '$now'
              AND auto_deactivate = TRUE";

    // Désactivation de chaque service expiré
    foreach ($expiredServices as $serviceKey) {
        deactivateService($serviceKey, 'auto_expired', 'system');
    }
}
```

## 🔄 **Processus de Désactivation**

### **1. Désactivation Manuelle**
```javascript
deactivateService(serviceKey) {
    if (!confirm('Êtes-vous sûr de vouloir désactiver ce service ?')) {
        return;
    }

    fetch('/route/serviceManagementRoute.php?action=deactivate', {
        method: 'POST',
        body: JSON.stringify({
            service_key: serviceKey,
            reason: 'manual',
            deactivated_by: 'admin'
        })
    });
}
```

### **2. Traitement Backend de Désactivation**
```php
function deactivateService($serviceKey, $reason = 'manual', $deactivatedBy = null) {
    // 1. Transaction de base de données
    mysqli_begin_transaction($conn);

    // 2. Mise à jour du statut
    $updateQuery = "UPDATE service_management
                   SET is_active = FALSE,
                       last_deactivated_at = '$now'
                   WHERE service_key = '$serviceKey'";

    // 3. Journalisation
    logServiceAction($serviceKey, 'deactivated', $deactivatedBy, [
        'reason' => $reason
    ]);

    // 4. Validation
    mysqli_commit($conn);
}
```

### **3. Données Stockées lors de la Désactivation**
- **`is_active`** : `FALSE`
- **`last_deactivated_at`** : Horodatage actuel
- **`updated_at`** : Horodatage actuel

## 📊 **Journalisation Complète**

### **Types d'Actions Enregistrées**
1. **`activated`** - Service activé
2. **`deactivated`** - Service désactivé
3. **`extended`** - Service prolongé
4. **`auto_expired`** - Expiration automatique

### **Détails Stockés dans les Logs**
```json
{
    "duration_hours": 24,
    "end_time": "2024-01-15 14:30:00",
    "reason": "manual",
    "previous_end_time": "2024-01-14 14:30:00",
    "extension_hours": 12
}
```

## 🎯 **Flux de Données Complet**

### **Activation → Surveillance → Désactivation**

1. **Activation**
   - Interface utilisateur → API → Base de données
   - Calcul automatique de l'heure de fin
   - Journalisation de l'action

2. **Surveillance Continue**
   - Vérification toutes les 30 secondes (interface)
   - Calcul du temps restant en temps réel
   - Vérification d'expiration à chaque accès

3. **Désactivation**
   - Manuelle (admin) ou automatique (expiration)
   - Mise à jour du statut
   - Journalisation avec raison

### **Points de Contrôle**
- ✅ **Validation des entrées** (durée, heure de fin)
- ✅ **Transactions atomiques** (cohérence des données)
- ✅ **Journalisation complète** (audit trail)
- ✅ **Gestion d'erreurs** (rollback en cas d'échec)
- ✅ **Vérification d'expiration** (automatique)

## 🔄 **Processus de Prolongement**

### **1. Déclenchement du Prolongement**
```javascript
// Interface utilisateur
extendService(serviceKey) {
    // Récupération des paramètres
    const additionalHours = parseInt(extensionHoursInput.value);
    const newEndTime = newEndTimeInput.value;

    // Validation
    if (!additionalHours && !newEndTime) {
        showAlert('Heures supplémentaires ou nouvelle heure de fin requise', 'warning');
        return;
    }

    // Appel API
    fetch('/route/serviceManagementRoute.php?action=extend', {
        method: 'POST',
        body: JSON.stringify({
            service_key: serviceKey,
            additional_hours: additionalHours,
            new_end_time: newEndTime,
            extended_by: 'admin'
        })
    });
}
```

### **2. Traitement Backend du Prolongement**
```php
function extendService($serviceKey, $additionalHours = null, $newEndTime = null, $extendedBy = null) {
    // 1. Vérification que le service est actif
    $service = getServiceByKey($serviceKey);
    if (!$service['is_active']) {
        throw new Exception("Cannot extend inactive service");
    }

    // 2. Calcul de la nouvelle heure de fin
    $currentEndTime = $service['end_time'];
    if ($newEndTime) {
        $calculatedNewEndTime = $newEndTime;
    } elseif ($additionalHours && $currentEndTime) {
        $calculatedNewEndTime = date('Y-m-d H:i:s', strtotime($currentEndTime . " +$additionalHours hours"));
    }

    // 3. Validation que la nouvelle heure est dans le futur
    $newEndTimeObj = new DateTime($calculatedNewEndTime);
    $nowObj = new DateTime();
    if ($newEndTimeObj <= $nowObj) {
        throw new Exception("New end time must be in the future");
    }

    // 4. Mise à jour de la base de données
    $updateQuery = "UPDATE service_management
                   SET end_time = '$calculatedNewEndTime'
                   WHERE service_key = '$serviceKey'";

    // 5. Journalisation du prolongement
    logServiceAction($serviceKey, 'extended', $extendedBy, [
        'additional_hours' => $additionalHours,
        'previous_end_time' => $currentEndTime,
        'new_end_time' => $calculatedNewEndTime
    ]);
}
```

### **3. Données Stockées lors du Prolongement**
- **`end_time`** : Nouvelle heure de fin calculée
- **`updated_at`** : Horodatage de la modification

## 📋 **API Endpoints Complets**

### **Activation**
- **URL** : `POST /route/serviceManagementRoute.php?action=activate`
- **Paramètres** :
  ```json
  {
    "service_key": "ue_preferences",
    "duration_hours": 24,
    "end_time": "2024-01-15 14:30:00",
    "activated_by": "admin"
  }
  ```

### **Prolongement**
- **URL** : `POST /route/serviceManagementRoute.php?action=extend`
- **Paramètres** :
  ```json
  {
    "service_key": "ue_preferences",
    "additional_hours": 12,
    "new_end_time": "2024-01-16 14:30:00",
    "extended_by": "admin"
  }
  ```

### **Désactivation**
- **URL** : `POST /route/serviceManagementRoute.php?action=deactivate`
- **Paramètres** :
  ```json
  {
    "service_key": "ue_preferences",
    "reason": "manual",
    "deactivated_by": "admin"
  }
  ```

## 🎯 **Workflow Complet Implémenté**

### **Cycle de Vie d'un Service**
1. **Création** → Service inactif par défaut
2. **Activation** → Définition de durée ou heure de fin
3. **Surveillance** → Calcul temps restant en temps réel
4. **Prolongement** (optionnel) → Extension de la durée
5. **Désactivation** → Manuelle ou automatique à l'expiration

### **Options de Gestion du Temps**
- ✅ **Durée en heures** : Activation pour X heures à partir de maintenant
- ✅ **Heure de fin spécifique** : Activation jusqu'à une date/heure précise
- ✅ **Prolongement par heures** : Ajout d'heures supplémentaires
- ✅ **Nouvelle heure de fin** : Redéfinition de l'heure de fin
- ✅ **Calcul automatique** : Temps restant calculé en temps réel

Ce processus garantit une gestion précise et fiable des services avec un suivi complet de toutes les actions, incluant maintenant les fonctionnalités de prolongement pour une flexibilité maximale.
