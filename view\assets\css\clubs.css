/* Clubs Page Styles */

.clubs-container {
    max-width: 1200px;
    margin: 0 auto;
    animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Header styles with standard blue */
.clubs-header {
    margin-bottom: 30px;
    background: #1a73e8; /* Standard royal blue */
    padding: 30px;
    border-radius: 20px;
    color: white; /* Changed for better contrast */
    box-shadow: 0 8px 25px rgba(26, 115, 232, 0.2);
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.clubs-header:before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.clubs-header:hover:before {
    opacity: 1;
    transform: scale(1);
}

.clubs-header h1 {
    font-weight: 700;
    margin-bottom: 15px;
    font-size: 2.4rem;
    position: relative;
    display: inline-block;
}

.clubs-header h1:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: white; /* Solid white line for better contrast */
    border-radius: 3px;
}

.clubs-header .subtitle {
    opacity: 0.9;
    font-size: 1.2rem;
    margin-bottom: 0;
    font-weight: 300;
    letter-spacing: 0.5px;
}

/* Action buttons */
.action-buttons {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 30px;
}

.action-buttons .btn {
    padding: 10px 20px;
    border-radius: 30px;
    font-weight: 500;
    background: #1a73e8; /* Standard royal blue */
    border: none;
    color: white; /* Changed for better contrast */
    box-shadow: 0 4px 15px rgba(26, 115, 232, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.action-buttons .btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background: #1557b0; /* Darker shade of royal blue */
    transition: all 0.3s ease;
    z-index: -1;
    border-radius: 30px;
}

.action-buttons .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(26, 115, 232, 0.3);
}

.action-buttons .btn:hover:before {
    width: 100%;
}

.action-buttons .btn i {
    margin-right: 8px;
    font-size: 1.1rem;
    vertical-align: middle;
    transition: transform 0.3s ease;
}

.action-buttons .btn:hover i {
    transform: scale(1.2);
}

/* Club cards */
.club-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
    border-left: 4px solid;
    position: relative;
    display: flex;
    flex-wrap: wrap;
}

.club-card {
    border-color: #1a73e8; /* Standard royal blue for all cards */
}

.club-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
    z-index: 1;
}

.club-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}

.club-card:hover:before {
    opacity: 1;
}

/* Student search styles */
.student-search-container {
    position: relative;
    margin-bottom: 10px;
}

.student-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: none;
}

.student-search-results.active {
    display: block;
}

.student-result-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.student-result-item:last-child {
    border-bottom: none;
}

.student-result-item:hover {
    background-color: #f5f5f5;
}

.selected-student {
    margin-top: 5px;
    padding: 8px 12px;
    background-color: #E3F2FD;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #0D47A1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: none;
}

.selected-student.active {
    display: flex;
}

.selected-student .remove-student {
    cursor: pointer;
    color: #F44336;
    transition: all 0.2s ease;
}

.selected-student .remove-student:hover {
    transform: scale(1.2);
}

/* Animation for new cards */
@keyframes cardAppear {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.col-12.mb-4 {
    animation: cardAppear 0.5s ease-out forwards;
    opacity: 0;
}

.col-12.mb-4:nth-child(1) { animation-delay: 0.1s; }
.col-12.mb-4:nth-child(2) { animation-delay: 0.2s; }
.col-12.mb-4:nth-child(3) { animation-delay: 0.3s; }
.col-12.mb-4:nth-child(4) { animation-delay: 0.4s; }
.col-12.mb-4:nth-child(5) { animation-delay: 0.5s; }
.col-12.mb-4:nth-child(6) { animation-delay: 0.6s; }
.col-12.mb-4:nth-child(7) { animation-delay: 0.7s; }
.col-12.mb-4:nth-child(8) { animation-delay: 0.8s; }
.col-12.mb-4:nth-child(9) { animation-delay: 0.9s; }
.col-12.mb-4:nth-child(n+10) { animation-delay: 1s; }

.club-header {
    padding: 12px 15px;
    border-right: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    background: #f8f9fa; /* Light gray background */
    position: relative;
    overflow: hidden;
    border-radius: 8px 0 0 8px;
    width: 25%;
}

.club-card {
    display: flex;
    flex-wrap: wrap;
}

.club-header:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: #1a73e8; /* Standard royal blue */
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.club-card:hover .club-header:after {
    transform: scaleX(1);
}

.club-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
    border: 2px solid #e0e0e0;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #6c757d;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 2;
}

.club-logo:before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: rgba(26, 115, 232, 0.2); /* Light royal blue */
    z-index: -1;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.4s ease;
}

.club-card:hover .club-logo {
    transform: scale(1.1) rotate(5deg);
    border-color: #fff;
}

.club-card:hover .club-logo:before {
    opacity: 1;
    transform: scale(1);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.club-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: all 0.4s ease;
}

.club-card:hover .club-logo img {
    transform: scale(1.1);
}

.club-title {
    flex-grow: 1;
    transition: transform 0.3s ease;
}

.club-card:hover .club-title {
    transform: translateX(5px);
}

.club-title h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
}

.club-title h3:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #1a73e8; /* Standard royal blue */
    transition: width 0.3s ease;
}

.club-card:hover .club-title h3:after {
    width: 100%;
}

.club-status {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.status-active {
    background-color: #E8F5E9;
    color: #2E7D32;
    box-shadow: 0 3px 8px rgba(46, 125, 50, 0.1);
    border-left: 3px solid #4CAF50;
}

.status-inactive {
    background-color: #FFEBEE;
    color: #C62828;
    box-shadow: 0 3px 8px rgba(198, 40, 40, 0.1);
    border-left: 3px solid #F44336;
}

.club-card:hover .club-status {
    transform: translateY(-3px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.1);
}

.club-status i {
    margin-right: 5px;
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.club-card:hover .club-status i {
    transform: rotate(360deg);
}

.club-body {
    padding: 12px 15px;
    background: white;
    transition: all 0.3s ease;
    width: 55%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.club-card:hover .club-body {
    background: #f8f9fa; /* Light gray background on hover */
}

.club-description {
    color: #555;
    margin-bottom: 8px;
    line-height: 1.4;
    font-size: 0.85rem;
    padding: 8px 10px;
    border-radius: 6px;
    background-color: rgba(248, 249, 250, 0.5);
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    max-height: 80px;
    overflow-y: auto;
}

.club-description:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom,
        rgba(255, 211, 224, 0.7), /* Pastel Pink */
        rgba(212, 240, 240, 0.7)  /* Pastel Cyan */
    );
    transition: all 0.3s ease;
}

.club-card:hover .club-description {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    background-color: rgba(248, 249, 250, 0.8);
}

.club-info {
    margin-bottom: 8px;
    padding: 6px 10px;
    border-radius: 6px;
    background-color: rgba(248, 249, 250, 0.5);
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
}

.club-card:hover .club-info {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    background-color: rgba(248, 249, 250, 0.8);
}

.club-info p {
    margin-bottom: 4px;
    color: #666;
    display: flex;
    align-items: center;
    font-size: 0.8rem;
}

.club-info i {
    margin-right: 5px;
    color: #80DEEA;
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.club-card:hover .club-info i {
    transform: scale(1.2);
}

.club-info strong {
    color: #333;
    font-weight: 600;
    margin-right: 5px;
}

.club-members {
    margin-top: 8px;
    padding: 6px 10px;
    border-radius: 6px;
    background-color: rgba(248, 249, 250, 0.5);
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
}

.club-card:hover .club-members {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    background-color: rgba(248, 249, 250, 0.8);
}

.club-members h4 {
    font-size: 0.9rem;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    display: flex;
    align-items: center;
    position: relative;
    padding-bottom: 5px;
}

.club-members h4:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(to right,
        rgba(255, 211, 224, 1), /* Pastel Pink */
        rgba(212, 240, 240, 1)  /* Pastel Cyan */
    );
    transition: width 0.3s ease;
}

.club-card:hover .club-members h4:after {
    width: 100px;
}

.member-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.member-list li {
    padding: 5px 8px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin-bottom: 3px;
}

.member-list li:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateX(5px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.member-list li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.member-role {
    font-weight: 600;
    color: #333;
    width: 100px;
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.6);
    padding: 3px 6px;
    border-radius: 12px;
    margin-right: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
    font-size: 0.75rem;
}

.member-list li:hover .member-role {
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
}

.member-role i {
    margin-right: 6px;
    transition: all 0.3s ease;
}

.member-list li:nth-child(1) .member-role i {
    color: #FF9800; /* President - Orange */
}

.member-list li:nth-child(2) .member-role i {
    color: #03A9F4; /* Vice President - Blue */
}

.member-list li:nth-child(3) .member-role i {
    color: #9C27B0; /* Secretary - Purple */
}

.member-list li:nth-child(4) .member-role i {
    color: #4CAF50; /* Treasurer - Green */
}

.member-list li:hover .member-role i {
    transform: scale(1.2) rotate(10deg);
}

.member-name {
    color: #555;
    flex-grow: 1;
    font-size: 0.75rem;
    transition: all 0.3s ease;
    padding: 3px 6px;
    border-radius: 3px;
}

.member-list li:hover .member-name {
    color: #333;
    font-weight: 500;
}

.club-actions {
    display: flex;
    justify-content: flex-end;
    padding: 20px;
    border-left: 1px solid #f0f0f0;
    background: linear-gradient(to right, rgba(248, 249, 250, 0.5), rgba(255, 255, 255, 0.9));
    border-radius: 0 16px 16px 0;
    transition: all 0.3s ease;
    width: 20%;
    flex-direction: column;
    align-items: center;
}

.club-card:hover .club-actions {
    background: linear-gradient(to right, rgba(248, 249, 250, 0.7), rgba(255, 255, 255, 1));
}

.club-actions .btn {
    margin: 5px 0;
    padding: 12px 20px;
    font-size: 0.9rem;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
    width: 90%;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.club-actions .btn-outline-primary {
    background-color: #E0F7FA;
    border: 2px solid #B2EBF2;
    color: #00838F;
}

.club-actions .btn-outline-primary:hover {
    background-color: #B2EBF2;
    color: #006064;
    box-shadow: 0 4px 10px rgba(0, 131, 143, 0.15);
    transform: translateY(-2px);
}

.club-actions .btn-outline-danger {
    background-color: #FFEBEE;
    border: 2px solid #FFCDD2;
    color: #C62828;
}

.club-actions .btn-outline-danger:hover {
    background-color: #FFCDD2;
    color: #B71C1C;
    box-shadow: 0 4px 10px rgba(198, 40, 40, 0.15);
    transform: translateY(-2px);
}

.club-actions .btn-outline-info {
    background-color: #E1F5FE;
    border: 2px solid #B3E5FC;
    color: #0288D1;
}

.club-actions .btn-outline-info:hover {
    background-color: #B3E5FC;
    color: #01579B;
    box-shadow: 0 4px 10px rgba(2, 136, 209, 0.15);
    transform: translateY(-2px);
}

.club-actions .btn-outline-success {
    background-color: #E8F5E9;
    border: 2px solid #A5D6A7;
    color: #2E7D32;
}

.club-actions .btn-outline-success:hover {
    background-color: #A5D6A7;
    color: #1B5E20;
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.15);
    transform: translateY(-2px);
}

.club-actions .btn i {
    margin-right: 8px;
    transition: all 0.3s ease;
}

.club-actions .btn:hover i {
    transform: scale(1.2);
}

/* Modal styles */
.modal-header {
    background: linear-gradient(135deg, #E0F7FA 0%, #B2EBF2 100%);
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
    padding: 20px 25px;
}

/* Events table styles */
.table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background-color: #e8f0fe; /* Light blue from style.css */
    color: #1a73e8; /* Standard royal blue */
    font-weight: 600;
    border-bottom: 2px solid #1a73e8; /* Standard royal blue */
    padding: 12px 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #F5FCFD;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
    border-bottom: 1px solid #f0f0f0;
}

.table .btn-sm {
    padding: 5px 10px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.table .btn-outline-danger {
    background-color: #FFEBEE;
    border: 1px solid #FFCDD2;
    color: #C62828;
}

.table .btn-outline-danger:hover {
    background-color: #FFCDD2;
    color: #B71C1C;
    box-shadow: 0 3px 8px rgba(198, 40, 40, 0.15);
}

.modal-header .modal-title {
    font-weight: 600;
    color: #00838F;
    display: flex;
    align-items: center;
}

.modal-header .btn-close {
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    padding: 8px;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.modal-header .btn-close:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: rotate(90deg);
    opacity: 1;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 10px 10px;
    padding: 15px 25px;
}

.modal-content {
    border-radius: 10px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border: none;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal .form-control, .modal .form-select {
    border-radius: 8px;
    padding: 10px 15px;
    border-color: #e0e0e0;
    transition: all 0.3s ease;
}

.modal .form-control:focus, .modal .form-select:focus {
    border-color: #80DEEA;
    box-shadow: 0 0 0 0.25rem rgba(128, 222, 234, 0.25);
}

.modal .form-label {
    font-weight: 500;
    color: #455A64;
    margin-bottom: 8px;
}

.modal .btn-primary {
    background: linear-gradient(135deg, #B2EBF2 0%, #80DEEA 100%);
    border: none;
    color: #00838F;
    font-weight: 500;
}

.modal .btn-primary:hover {
    background: linear-gradient(135deg, #80DEEA 0%, #4DD0E1 100%);
    box-shadow: 0 5px 15px rgba(0, 131, 143, 0.2);
}

.modal .btn-secondary {
    background-color: #f5f5f5;
    border: none;
    color: #455A64;
}

.modal .btn-secondary:hover {
    background-color: #e0e0e0;
    color: #263238;
}

.modal .btn-danger {
    background-color: #FFEBEE;
    border: none;
    color: #C62828;
}

.modal .btn-danger:hover {
    background-color: #FFCDD2;
    box-shadow: 0 5px 15px rgba(198, 40, 40, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .clubs-header h1 {
        font-size: 1.8rem;
    }

    .clubs-header .subtitle {
        font-size: 1rem;
    }

    .club-card {
        flex-direction: column;
    }

    .club-header {
        width: 100%;
        border-radius: 16px 16px 0 0;
        text-align: center;
        flex-direction: column;
    }

    .club-body {
        width: 100%;
    }

    .club-actions {
        width: 100%;
        flex-direction: row;
        justify-content: center;
        border-radius: 0 0 16px 16px;
        border-left: none;
        border-top: 1px solid #f0f0f0;
    }

    .club-logo {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .club-actions .btn {
        margin: 0 5px;
        width: auto;
    }

    .member-role {
        width: 120px;
        font-size: 0.85rem;
    }

    .member-name {
        font-size: 0.85rem;
    }
}

/* Loading animation */
@keyframes shimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

.loading-shimmer {
    animation: shimmer 1.5s infinite linear;
    background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
    background-size: 800px 104px;
    border-radius: 8px;
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: 50px 20px;
    background-color: rgba(248, 249, 250, 0.7);
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    margin: 30px 0;
    transition: all 0.3s ease;
}

.empty-state:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.empty-state i {
    font-size: 3rem;
    color: #B2EBF2;
    margin-bottom: 20px;
    display: block;
}

.empty-state p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.empty-state .btn {
    background: linear-gradient(135deg, #B2EBF2 0%, #80DEEA 100%);
    border: none;
    color: #00838F;
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0, 131, 143, 0.15);
    transition: all 0.3s ease;
}

.empty-state .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 131, 143, 0.2);
}

/* Pagination styles */
.pagination {
    margin-top: 30px;
    margin-bottom: 30px;
}

.pagination .page-item .page-link {
    color: #00838F;
    border-color: #B2EBF2;
    background-color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.pagination .page-item.active .page-link {
    background-color: #80DEEA;
    border-color: #4DD0E1;
    color: #006064;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 131, 143, 0.2);
}

.pagination .page-item .page-link:hover {
    background-color: #E0F7FA;
    color: #00838F;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 131, 143, 0.15);
}

.pagination .page-item.disabled .page-link {
    color: #aaa;
    background-color: #f8f8f8;
    border-color: #ddd;
}

.pagination .page-link:focus {
    box-shadow: 0 0 0 0.25rem rgba(128, 222, 234, 0.25);
}