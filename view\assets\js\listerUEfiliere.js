/**
 * JavaScript for the listerUEfiliere page
 *
 * This script handles loading and displaying teaching units for the coordinator's filiere,
 * as well as filtering, searching, and pagination functionality.
 */

// Global variables
let allUnites = [];
let filteredUnites = [];
let currentPage = 1;
const itemsPerPage = 10;
let niveaux = new Set();
let allNiveaux = []; // Array to store all niveaux from the API

// Get base URL dynamically
const getBaseUrl = () => {
    // Method 1: Try to get the base URL from the current page URL
    const currentUrl = window.location.href;
    const urlParts = currentUrl.split('/');

    // Find the index of 'view' in the URL
    const viewIndex = urlParts.findIndex(part => part === 'view');

    if (viewIndex !== -1) {
        // Return everything up to 'view'
        return urlParts.slice(0, viewIndex).join('/');
    }

    // Method 2: Try to get from a script tag with a known path
    const scriptTags = document.querySelectorAll('script[src*="/assets/js/"]');

    for (const script of scriptTags) {
        const src = script.getAttribute('src');
        const srcParts = src.split('/');
        const assetsIndex = srcParts.indexOf('assets');

        if (assetsIndex !== -1 && assetsIndex >= 2) {
            // We found a script with the expected path structure
            // The base URL is everything before '/view/assets/js/'
            const baseUrlParts = src.split('/view/assets/js/')[0];
            return baseUrlParts;
        }
    }

    // Method 3: Use document.baseURI as a fallback
    const baseUri = document.baseURI;
    const baseUriParts = baseUri.split('/');

    // Remove the last part (current page)
    baseUriParts.pop();

    // Check if we're in a view subdirectory
    if (baseUriParts[baseUriParts.length - 1] === 'view' ||
        baseUriParts[baseUriParts.length - 2] === 'view') {
        // Go up to the project root
        const projectRoot = baseUriParts.slice(0, baseUriParts.indexOf('view')).join('/');
        return projectRoot;
    }

    // Final fallback: return a relative path that should work in most cases
    console.warn('Could not determine base URL dynamically, using relative path');
    return '../../..';
}

// Store the base URL and log it for debugging
const baseUrl = getBaseUrl();
console.log('Determined base URL:', baseUrl);

/**
 * Get the coordinator's filiere_id from the hidden input field
 *
 * @return {string|null} The coordinator's filiere_id or null if not found
 */
function getCoordinatorFiliereId() {
    // Try to get the filiere_id from the hidden input field
    const filiereIdField = document.getElementById('coordinator-filiere-id');

    if (filiereIdField && filiereIdField.value) {
        return filiereIdField.value;
    }

    // If not found, try to get it from the session storage
    const sessionFiliereId = sessionStorage.getItem('coordinator_filiere_id');
    if (sessionFiliereId) {
        return sessionFiliereId;
    }

    console.warn('Could not find coordinator filiere_id');
    return null;
}

/**
 * Update filter event listeners
 */
function updateFilterEventListeners() {
    console.log('Updating filter event listeners');

    // Remove existing event listeners to prevent duplicates
    $('#filterNiveau').off('change');
    $('#filterSemestre').off('change');
    $('#filterType').off('change');
    $('#searchInput').off('keyup');

    // Add event listener for niveau filter
    $('#filterNiveau').on('change', function() {
        const niveauId = $(this).val();
        console.log(`Niveau filter changed to: ${niveauId}`);

        // Update semestre filter options based on selected niveau
        if (niveauId) {
            // If a niveau is selected, update the semestre options
            filterSemestresByNiveau(niveauId);
        } else {
            // If no niveau selected, reset the semestre dropdown to just the default option
            $('#filterSemestre').html('<option value="">All Semesters</option>');
            console.log('Reset semestre dropdown to default option only');

            // Also reset the semestre filter value
            $('#filterSemestre').val('');
        }

        // Apply filters
        applyFilters();
    });

    // Add event listener for semestre filter
    $('#filterSemestre').on('change', function() {
        const semestreId = $(this).val();
        console.log(`Semestre filter changed to: ${semestreId}`);

        // If a semestre is selected but no niveau is selected, this is an invalid state
        // We should ensure a niveau is selected first
        if (semestreId && !$('#filterNiveau').val()) {
            showAlert('warning', 'Please select a level first before selecting a semester.');
            $(this).val(''); // Reset the semestre selection
            return;
        }

        applyFilters();
    });

    // Add event listener for type filter
    $('#filterType').on('change', function() {
        console.log(`Type filter changed to: ${$(this).val()}`);
        applyFilters();
    });

    // Add event listener for search
    $('#searchInput').on('keyup', function() {
        console.log(`Search term changed to: ${$(this).val()}`);
        applyFilters();
    });

    // Add event listener for reset filters button if it exists
    $('#resetFilters').off('click').on('click', function() {
        console.log('Resetting all filters');

        // Reset all filter values
        $('#filterNiveau').val('');
        $('#filterSemestre').html('<option value="">All Semesters</option>').val('');
        $('#filterType').val('');
        $('#searchInput').val('');

        // Apply filters (which will now show all units)
        applyFilters();

        return false; // Prevent default action
    });

    console.log('Filter event listeners updated');
}

/**
 * Debug function to analyze the data and help identify filtering issues
 */
function debugFilterData() {
    console.log('=== DEBUG FILTER DATA ===');

    // Check if we have any units
    console.log(`Total units: ${allUnites.length}`);

    if (allUnites.length > 0) {
        // Log the structure of the first unit
        console.log('First unit structure:', allUnites[0]);

        // Count units by niveau
        const niveauCounts = {};
        allUnites.forEach(unite => {
            const niveauId = unite.id_niveau || 'undefined';
            const niveauName = unite.niveau || 'undefined';
            const key = `${niveauId}:${niveauName}`;

            niveauCounts[key] = (niveauCounts[key] || 0) + 1;
        });

        console.log('Units by niveau:', niveauCounts);

        // Count units by semestre
        const semestreCounts = {};
        allUnites.forEach(unite => {
            const semestreId = unite.id_semestre || 'undefined';
            const semestreName = unite.semestre || 'undefined';
            const key = `${semestreId}:${semestreName}`;

            semestreCounts[key] = (semestreCounts[key] || 0) + 1;
        });

        console.log('Units by semestre:', semestreCounts);
    }

    // Check the niveau dropdown
    const niveauOptions = $('#filterNiveau option').map(function() {
        return { value: $(this).val(), text: $(this).text() };
    }).get();

    console.log('Niveau dropdown options:', niveauOptions);

    // Check the semestre dropdown
    const semestreOptions = $('#filterSemestre option').map(function() {
        return { value: $(this).val(), text: $(this).text() };
    }).get();

    console.log('Semestre dropdown options:', semestreOptions);

    console.log('=== END DEBUG FILTER DATA ===');
}

// Document ready
$(document).ready(function() {
    console.log('Document ready - initializing listerUEfiliere.js');

    try {
        // Add more detailed debugging
        console.log('DOM loaded, checking for coordinator-filiere-id element');
        const filiereIdElement = document.getElementById('coordinator-filiere-id');
        console.log('Filiere ID element exists:', filiereIdElement !== null);
        if (filiereIdElement) {
            console.log('Filiere ID element value:', filiereIdElement.value);
        }

        // Store the coordinator's filiere_id in session storage for later use
        const coordinatorFiliereId = getCoordinatorFiliereId();
        console.log('Retrieved coordinator filiere ID:', coordinatorFiliereId);

        if (coordinatorFiliereId) {
            sessionStorage.setItem('coordinator_filiere_id', coordinatorFiliereId);
            console.log(`Stored coordinator's filiere_id in session storage: ${coordinatorFiliereId}`);
        } else {
            // For testing, set a default filiere_id if not found
            console.log('No filiere ID found, setting default value of 1');
            sessionStorage.setItem('coordinator_filiere_id', '1');
        }

        // Load all niveaux for the coordinator's filiere
        console.log('Loading niveaux...');
        loadNiveaux();

        // Load teaching units for the coordinator's filiere
        console.log('Loading teaching units...');
        loadUniteEnseignement();

        // Set up event listeners for filters
        console.log('Setting up event listeners...');
        updateFilterEventListeners();

        // Debug the filter data after a short delay to ensure everything is loaded
        console.log('Setting timeout for debug data...');
        setTimeout(debugFilterData, 2000);

        // Add a fallback timeout to check if data loaded
        setTimeout(function() {
            console.log('Checking if data loaded after 5 seconds...');
            if (allUnites.length === 0) {
                console.log('No units loaded after 5 seconds, trying again...');
                loadUniteEnseignement();
            }
            if (niveaux.size === 0) {
                console.log('No niveaux loaded after 5 seconds, trying again...');
                loadNiveaux();
            }
        }, 5000);
    } catch (error) {
        console.error('Error in document ready function:', error);
        // Show error in the UI
        $('#alertContainer').html(`
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                Error initializing page: ${error.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `);
    }
});

/**
 * Load all niveaux for the coordinator's filiere
 */
function loadNiveaux() {
    // Get the coordinator's filiere_id
    const coordinatorFiliereId = getCoordinatorFiliereId();

    // Prepare request data
    const requestData = {
        action: 'getNiveaux'
    };

    // Add filiere_id to the request if available
    if (coordinatorFiliereId) {
        requestData.filiere_id = coordinatorFiliereId;
        console.log(`Including coordinator's filiere_id in niveaux request: ${coordinatorFiliereId}`);
    }

    // Log the request URL for debugging
    const requestUrl = baseUrl + '/route/listerUEfiliereRoute.php?' +
        Object.entries(requestData)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&');
    console.log('Loading niveaux from URL:', requestUrl);

    $.ajax({
        url: baseUrl + '/route/listerUEfiliereRoute.php',
        type: 'GET',
        data: requestData,
        dataType: 'json',
        success: function(response) {
            console.log('Niveaux API Response:', response); // Debug

            // Debug: Log the structure of the first niveau if available
            if (response.data && response.data.length > 0) {
                console.log('First niveau structure from API:', response.data[0]);
                console.log('Sample id value:', response.data[0].id || response.data[0].id_niveau);
                console.log('Sample nom/niveau value:', response.data[0].nom || response.data[0].niveau);
                console.log('Sample cycle_id value:', response.data[0].cycle_id);
            }

            if (response.data && response.data.length > 0) {
                allNiveaux = response.data;

                // Store the filiere_id from the response if available
                if (response.filiere_id && !coordinatorFiliereId) {
                    sessionStorage.setItem('coordinator_filiere_id', response.filiere_id);
                    console.log(`Stored coordinator's filiere_id from response: ${response.filiere_id}`);
                }

                // Clear the niveaux set
                niveaux = new Set();

                // Add each niveau to the set, handling different field name formats
                allNiveaux.forEach(niveauObj => {
                    // Handle different field name formats (niveau or nom)
                    const niveauValue = niveauObj.niveau || niveauObj.nom;
                    const niveauId = niveauObj.id_niveau || niveauObj.id;

                    if (niveauValue) {
                        console.log(`Adding niveau from API: ${niveauValue} (ID: ${niveauId})`); // Debug
                        // Store the niveau with its ID for later use
                        niveaux.add({
                            id: niveauId,
                            name: niveauValue,
                            cycle_id: niveauObj.cycle_id
                        });
                    }
                });

                console.log('Niveaux set after API call:', Array.from(niveaux)); // Debug

                // Populate the niveau filter dropdown
                populateNiveauFilter();
            } else {
                console.warn('No niveaux found for this coordinator\'s department');
                // Show a message in the dropdown
                $('#filterNiveau').html('<option value="">No levels available</option>');
            }
        },
        error: function(xhr) {
            console.error('Error loading niveaux:', xhr);

            // Show error message in the dropdown
            $('#filterNiveau').html('<option value="">Error loading levels</option>');

            if (xhr.responseJSON && xhr.responseJSON.error) {
                showAlert('danger', 'Error loading levels: ' + xhr.responseJSON.error);
            } else {
                showAlert('danger', 'Error loading levels. Please try again later.');
            }
        }
    });
}

/**
 * Load teaching units for the coordinator's filiere
 */
function loadUniteEnseignement() {
    // Show loading indicator
    $('#unitesList').html('<tr><td colspan="6" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></td></tr>');

    // Get the coordinator's filiere_id
    const coordinatorFiliereId = getCoordinatorFiliereId();

    // Prepare request data
    const requestData = {
        action: 'getForCoordinator'
    };

    // Add filiere_id to the request if available
    if (coordinatorFiliereId) {
        requestData.filiere_id = coordinatorFiliereId;
        console.log(`Including coordinator's filiere_id in request: ${coordinatorFiliereId}`);
    }

    // Make API request
    $.ajax({
        url: baseUrl + '/route/listerUEfiliereRoute.php',
        type: 'GET',
        data: requestData,
        dataType: 'json',
        success: function(response) {
            console.log('API Response:', response); // Debug: Log the entire response

            // Debug: Log the structure of the first unit if available
            if (response.data && response.data.length > 0) {
                console.log('First unit structure:', response.data[0]);
                console.log('Sample niveau value:', response.data[0].niveau);
                console.log('Sample semestre value:', response.data[0].semestre);
            }

            if (response.data && response.data.length > 0) {
                allUnites = response.data;

                // Store the filiere_id from the response if available
                if (response.filiere_id && !coordinatorFiliereId) {
                    sessionStorage.setItem('coordinator_filiere_id', response.filiere_id);
                    console.log(`Stored coordinator's filiere_id from response: ${response.filiere_id}`);
                }

                // Initialize the semestre filter with just the default option
                // We'll only populate it when a niveau is selected
                $('#filterSemestre').html('<option value="">All Semesters</option>');
                console.log('Initialized empty semestre filter dropdown');

                // Apply initial filters
                applyFilters();
            } else {
                $('#unitesList').html('<tr><td colspan="7" class="text-center">No teaching units found for your filière.</td></tr>');
                $('#pagination').empty();
            }
        },
        error: function(xhr) {
            let errorMessage = 'An error occurred while loading teaching units.';

            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }

            showAlert('danger', errorMessage);
            $('#unitesList').html('<tr><td colspan="7" class="text-center">Error loading data.</td></tr>');
            $('#pagination').empty();
        }
    });
}

/**
 * Populate the niveau filter dropdown with unique values
 */
function populateNiveauFilter() {
    const niveauSelect = $('#filterNiveau');

    console.log('Populating niveau filter with:', Array.from(niveaux)); // Debug

    // Clear existing options except the first one
    niveauSelect.find('option:not(:first)').remove();

    // Add options for each unique niveau
    Array.from(niveaux).forEach(niveau => {
        console.log('Adding niveau option:', niveau); // Debug

        // Use the ID as the value and the name as the display text
        const niveauId = niveau.id;
        const niveauName = niveau.name;

        // Create the option element with data attributes for additional filtering
        const option = `<option value="${niveauId}" data-cycle-id="${niveau.cycle_id || ''}">${niveauName}</option>`;
        niveauSelect.append(option);

        console.log(`Added niveau option: ID=${niveauId}, Name=${niveauName}, HTML=${option}`);
    });

    // Log the final HTML of the dropdown for debugging
    console.log('Final niveau dropdown HTML:', niveauSelect.html());
}

/**
 * Populate the semestre filter dropdown with unique values from the data
 *
 * @param {Array} semestres Optional array of semestres to populate the dropdown with
 */
function populateSemestreFilter(semestres = null) {
    const semestreSelect = $('#filterSemestre');
    let semestresArray = [];

    // Create a map to track unique semestres by ID or name
    const uniqueSemestresMap = new Map();

    if (semestres) {
        // Use the provided semestres array
        semestresArray = semestres;
    } else {
        // Extract semestres from allUnites
        semestresArray = allUnites
            .filter(unite => unite.semestre)
            .map(unite => ({
                id: unite.id_semestre || '',
                name: unite.semestre,
                niveau_id: unite.id_niveau || ''
            }));
    }

    console.log('Initial semestres array:', semestresArray); // Debug

    // Process the array to keep only unique semestres
    // First, prioritize entries with IDs
    semestresArray.forEach(semestre => {
        if (typeof semestre === 'object') {
            const key = semestre.id ? `id:${semestre.id}` : `name:${semestre.name}`;

            // If this is a new semestre or it has an ID and the existing one doesn't
            if (!uniqueSemestresMap.has(key) ||
                (semestre.id && !uniqueSemestresMap.get(key).id)) {
                uniqueSemestresMap.set(key, semestre);
            }
        } else if (typeof semestre === 'string' && !uniqueSemestresMap.has(`name:${semestre}`)) {
            uniqueSemestresMap.set(`name:${semestre}`, semestre);
        }
    });

    // Convert the map values back to an array
    const uniqueSemestres = Array.from(uniqueSemestresMap.values());

    console.log('Unique semestres after deduplication:', uniqueSemestres); // Debug
    console.log(`Reduced from ${semestresArray.length} to ${uniqueSemestres.length} semestres`);

    // Clear existing options except the first one
    semestreSelect.find('option:not(:first)').remove();

    // Add options for each unique semestre
    uniqueSemestres.sort((a, b) => {
        // Sort by name if objects, otherwise sort directly
        if (typeof a === 'object' && typeof b === 'object') {
            return a.name.localeCompare(b.name);
        } else {
            return String(a).localeCompare(String(b));
        }
    }).forEach(semestre => {
        if (typeof semestre === 'object') {
            console.log('Adding semestre option:', semestre); // Debug
            const option = `<option value="${semestre.id}" data-niveau-id="${semestre.niveau_id || ''}">${semestre.name}</option>`;
            semestreSelect.append(option);
            console.log(`Added semestre option: ID=${semestre.id}, Name=${semestre.name}, HTML=${option}`);
        } else {
            console.log('Adding semestre option (string):', semestre); // Debug
            semestreSelect.append(`<option value="${semestre}">${semestre}</option>`);
        }
    });

    // Log the final HTML of the dropdown for debugging
    console.log('Final semestre dropdown HTML:', semestreSelect.html());
}

/**
 * Filter semestres by niveau
 *
 * @param {string|number} niveauId The ID of the niveau to filter by
 */
function filterSemestresByNiveau(niveauId) {
    if (!niveauId) {
        // If no niveau is selected, reset the semestre dropdown to just the default option
        $('#filterSemestre').html('<option value="">All Semesters</option>');
        console.log('Reset semestre dropdown to default option only');
        return;
    }

    console.log(`Filtering semestres by niveau_id: ${niveauId}`);

    // Get the semestre select element
    const semestreSelect = $('#filterSemestre');

    if (!semestreSelect.length) {
        console.error('Semestre select element not found');
        return;
    }

    // Clear the semestre dropdown
    semestreSelect.html('<option value="">All Semesters</option>');

    // Since there's no direct API endpoint for semestres, we'll filter from existing data
    console.log('Filtering semestres from existing data for niveau_id:', niveauId);

    // Use a Map to track unique semestres by ID
    const semestresMap = new Map();

    // Filter semestres from allUnites based on the selected niveau
    allUnites.forEach(unite => {
        if (String(unite.id_niveau) === String(niveauId) && unite.semestre) {
            const semestreId = unite.id_semestre || '';
            const semestreName = unite.semestre;

            // Use ID as key if available, otherwise use name
            const key = semestreId ? `id:${semestreId}` : `name:${semestreName}`;

            // Only add if not already in the map or if this one has an ID and the existing one doesn't
            if (!semestresMap.has(key) ||
                (semestreId && !semestresMap.get(key).id)) {
                semestresMap.set(key, {
                    id: semestreId,
                    name: semestreName,
                    niveau_id: unite.id_niveau
                });
            }
        }
    });

    // Convert map values to array
    const filteredSemestres = Array.from(semestresMap.values());

    console.log(`Filtered ${filteredSemestres.length} semestres from existing data for niveau_id: ${niveauId}`);

    if (filteredSemestres.length > 0) {
        // Populate the semestre dropdown with the filtered semestres
        populateSemestreFilter(filteredSemestres);
    } else {
        console.warn('No semestres found for niveau_id:', niveauId);

        // If no semestres found in existing data, try to fetch from the server using a different approach
        // This is a fallback and might not work if the server doesn't support this action
        $.ajax({
            url: baseUrl + '/route/descriptifRoute.php',
            type: 'GET',
            data: {
                action: 'getAllSemestres',
                niveau_id: niveauId
            },
            dataType: 'json',
            success: function(response) {
                console.log('Received semestres data from descriptifRoute:', response);

                if (response.data && Array.isArray(response.data)) {
                    // Use a Map to ensure unique semestres
                    const semestresMap = new Map();

                    // Process each semestre from the response
                    response.data.forEach(semestre => {
                        const semestreId = semestre.id || '';
                        const semestreName = semestre.nom || '';
                        const semestreNiveauId = semestre.niveau_id || niveauId;

                        // Use ID as key if available, otherwise use name
                        const key = semestreId ? `id:${semestreId}` : `name:${semestreName}`;

                        // Only add if not already in the map or if this one has an ID and the existing one doesn't
                        if (!semestresMap.has(key) ||
                            (semestreId && !semestresMap.get(key).id)) {
                            semestresMap.set(key, {
                                id: semestreId,
                                name: semestreName,
                                niveau_id: semestreNiveauId
                            });
                        }
                    });

                    // Convert map values to array
                    const semestres = Array.from(semestresMap.values());

                    console.log(`Processed ${response.data.length} semestres from API to ${semestres.length} unique semestres`);

                    // Populate the semestre dropdown with the filtered semestres
                    populateSemestreFilter(semestres);

                    console.log(`Added ${semestres.length} unique semestres options for niveau_id: ${niveauId}`);
                } else {
                    console.warn('No semestres data returned from descriptifRoute for niveau_id:', niveauId);
                }
            },
            error: function(xhr) {
                console.error('Error loading semestres from descriptifRoute:', xhr.statusText);
                console.error('Response:', xhr.responseText);

                // Just use the empty semestre dropdown since we already tried filtering from existing data
                console.warn('Could not load semestres from any source for niveau_id:', niveauId);
            }
        });
    }
}

/**
 * Apply filters to the teaching units
 */
function applyFilters() {
    const niveauFilter = $('#filterNiveau').val();
    const semestreFilter = $('#filterSemestre').val();
    const typeFilter = $('#filterType').val();
    const searchTerm = $('#searchInput').val().toLowerCase().trim();

    console.log('Applying filters:', { niveauFilter, semestreFilter, typeFilter, searchTerm });

    // Debug: Log all unique niveau and semestre values in the data
    const uniqueNiveauxIds = new Set(allUnites.map(u => u.id_niveau).filter(Boolean));
    const uniqueNiveauxNames = new Set(allUnites.map(u => u.niveau).filter(Boolean));
    const uniqueSemestresIds = new Set(allUnites.map(u => u.id_semestre).filter(Boolean));
    const uniqueSemestresNames = new Set(allUnites.map(u => u.semestre).filter(Boolean));

    console.log('Unique niveau IDs in data:', Array.from(uniqueNiveauxIds));
    console.log('Unique niveau names in data:', Array.from(uniqueNiveauxNames));
    console.log('Unique semestre IDs in data:', Array.from(uniqueSemestresIds));
    console.log('Unique semestre names in data:', Array.from(uniqueSemestresNames));

    // Update filter counts in the UI if elements exist
    if ($('#totalUnits').length) {
        $('#totalUnits').text(allUnites.length);
    }

    // If we have a niveau filter, log all units with their niveau values to help debug
    if (niveauFilter) {
        console.log('All units with their niveau values:');
        allUnites.slice(0, 10).forEach(unite => {
            console.log(`Unit ${unite.id}: niveau_id=${unite.id_niveau}, niveau_name=${unite.niveau}`);
        });

        // Also log the selected niveau from the dropdown
        const selectedNiveauText = $('#filterNiveau option:selected').text();
        console.log(`Selected niveau from dropdown: id=${niveauFilter}, text=${selectedNiveauText}`);
    }

    // Filter the units
    filteredUnites = allUnites.filter(unite => {
        // Apply niveau filter with more flexible matching
        if (niveauFilter) {
            // Try multiple ways to match the niveau
            const niveauIdMatch = String(unite.id_niveau || '') === String(niveauFilter);
            const niveauNameMatch = String(unite.niveau || '').toLowerCase() === String($('#filterNiveau option:selected').text()).toLowerCase();

            // Also try partial matches if exact matches fail
            const partialNiveauNameMatch = unite.niveau && $('#filterNiveau option:selected').text() &&
                                          String(unite.niveau).toLowerCase().includes(String($('#filterNiveau option:selected').text()).toLowerCase());

            // Debug: Log detailed information about the niveau comparison
            if (filteredUnites.length < 5) {
                console.log(`Niveau comparison for unit ${unite.id}:`, {
                    unit_niveau_id: unite.id_niveau,
                    filter_niveau_id: niveauFilter,
                    id_match: niveauIdMatch,
                    unit_niveau_name: unite.niveau,
                    filter_niveau_name: $('#filterNiveau option:selected').text(),
                    name_match: niveauNameMatch,
                    partial_match: partialNiveauNameMatch,
                    result: (niveauIdMatch || niveauNameMatch || partialNiveauNameMatch) ? 'MATCH' : 'NO MATCH'
                });
            }

            // If none of the matching methods work, exclude this unit
            if (!niveauIdMatch && !niveauNameMatch && !partialNiveauNameMatch) {
                return false;
            }
        }

        // Apply semestre filter with more flexible matching
        if (semestreFilter) {
            // Try multiple ways to match the semestre
            const semestreIdMatch = String(unite.id_semestre || '') === String(semestreFilter);
            const semestreNameMatch = String(unite.semestre || '').toLowerCase() === String($('#filterSemestre option:selected').text()).toLowerCase();

            // Also try partial matches if exact matches fail
            const partialSemestreNameMatch = unite.semestre && $('#filterSemestre option:selected').text() &&
                                           String(unite.semestre).toLowerCase().includes(String($('#filterSemestre option:selected').text()).toLowerCase());

            // Debug: Log detailed information about the semestre comparison
            if (filteredUnites.length < 5) {
                console.log(`Semestre comparison for unit ${unite.id}:`, {
                    unit_semestre_id: unite.id_semestre,
                    filter_semestre_id: semestreFilter,
                    id_match: semestreIdMatch,
                    unit_semestre_name: unite.semestre,
                    filter_semestre_name: $('#filterSemestre option:selected').text(),
                    name_match: semestreNameMatch,
                    partial_match: partialSemestreNameMatch,
                    result: (semestreIdMatch || semestreNameMatch || partialSemestreNameMatch) ? 'MATCH' : 'NO MATCH'
                });
            }

            // If none of the matching methods work, exclude this unit
            if (!semestreIdMatch && !semestreNameMatch && !partialSemestreNameMatch) {
                return false;
            }
        }

        // Apply type filter (case-insensitive string comparison)
        if (typeFilter && String(unite.type || '').toLowerCase() !== String(typeFilter).toLowerCase()) {
            return false;
        }

        // Apply search filter
        if (searchTerm) {
            const searchFields = [
                unite.nom_module || '',
                unite.type || '',
                unite.niveau || '',
                unite.semestre || '',
                unite.filiere || '',
                unite.departement || ''
            ].map(field => String(field).toLowerCase());

            return searchFields.some(field => field.includes(searchTerm));
        }

        return true;
    });

    // Update filter counts in the UI if elements exist
    if ($('#filteredUnits').length) {
        $('#filteredUnits').text(filteredUnites.length);
    }

    // Log filter results
    console.log(`Filtered ${allUnites.length} units down to ${filteredUnites.length} units`);

    // Reset to first page when filters change
    currentPage = 1;

    // Display the filtered units
    displayUnites();
}

/**
 * Display teaching units with pagination
 */
function displayUnites() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const displayedUnites = filteredUnites.slice(startIndex, endIndex);

    // Clear the table
    $('#unitesList').empty();

    if (filteredUnites.length === 0) {
        // Check if we have any filters applied
        const hasFilters = $('#filterNiveau').val() || $('#filterSemestre').val() ||
                          $('#filterType').val() || $('#searchInput').val();

        let message = 'No teaching units found.';
        if (hasFilters) {
            message = 'No teaching units match your filters. Try different filter criteria.';
        }

        $('#unitesList').html(`<tr><td colspan="6" class="text-center">${message}</td></tr>`);
        $('#pagination').empty();

        // Update status message if it exists
        if ($('#statusMessage').length) {
            $('#statusMessage').text(message);
        }

        return;
    }

    // Update status message if it exists
    if ($('#statusMessage').length) {
        const totalPages = Math.ceil(filteredUnites.length / itemsPerPage);
        $('#statusMessage').text(
            `Showing ${startIndex + 1} to ${Math.min(endIndex, filteredUnites.length)} of ${filteredUnites.length} units (Page ${currentPage} of ${totalPages})`
        );
    }

    // Add each unit to the table
    displayedUnites.forEach(unite => {
        // Store the filiere_id as a data attribute for validation
        const filiereId = unite.filiere_id || '';

        // Safely handle potentially missing values
        const nomModule = unite.nom_module || 'Unnamed Module';
        const type = unite.type || '-';
        const volumeHoraire = unite.volume_horaire || 0;
        const niveau = unite.niveau || '-';
        const semestre = unite.semestre || '-';

        $('#unitesList').append(`
            <tr data-filiere-id="${filiereId}" data-id="${unite.id}">
                <td>${nomModule}</td>
                <td>${type}</td>
                <td>${volumeHoraire} hours</td>
                <td>${niveau}</td>
                <td>${semestre}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-info action-btn" onclick="viewUnit(${unite.id})">
                            <i class="bi bi-eye"></i> View
                        </button>
                        <button class="btn btn-sm btn-warning action-btn" onclick="editUnit(${unite.id})">
                            <i class="bi bi-pencil"></i> Edit
                        </button>
                        <button class="btn btn-sm btn-danger action-btn" onclick="deleteUnit(${unite.id})">
                            <i class="bi bi-trash"></i> Delete
                        </button>
                    </div>
                </td>
            </tr>
        `);
    });

    // Update pagination
    updatePagination();
}

/**
 * Update the pagination controls
 */
function updatePagination() {
    const totalPages = Math.ceil(filteredUnites.length / itemsPerPage);

    // Clear pagination
    $('#pagination').empty();

    if (totalPages <= 1) {
        return;
    }

    // Ensure current page is valid
    if (currentPage < 1) {
        currentPage = 1;
    } else if (currentPage > totalPages) {
        currentPage = totalPages;
    }

    // Previous button
    $('#pagination').append(`
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
                <span class="sr-only">Previous</span>
            </a>
        </li>
    `);

    // Determine which page numbers to show
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    // Adjust if we're near the end
    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }

    // First page link if not in range
    if (startPage > 1) {
        $('#pagination').append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(1); return false;">1</a>
            </li>
        `);

        // Ellipsis if needed
        if (startPage > 2) {
            $('#pagination').append(`
                <li class="page-item disabled">
                    <a class="page-link" href="#">...</a>
                </li>
            `);
        }
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
        $('#pagination').append(`
            <li class="page-item ${currentPage === i ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
            </li>
        `);
    }

    // Last page link if not in range
    if (endPage < totalPages) {
        // Ellipsis if needed
        if (endPage < totalPages - 1) {
            $('#pagination').append(`
                <li class="page-item disabled">
                    <a class="page-link" href="#">...</a>
                </li>
            `);
        }

        $('#pagination').append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a>
            </li>
        `);
    }

    // Next button
    $('#pagination').append(`
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
                <span class="sr-only">Next</span>
            </a>
        </li>
    `);
}

/**
 * Change the current page
 *
 * @param {number} page The page number to change to
 */
function changePage(page) {
    const totalPages = Math.ceil(filteredUnites.length / itemsPerPage);

    if (page < 1 || page > totalPages) {
        return;
    }

    currentPage = page;
    displayUnites();

    // Scroll to top of table
    $('html, body').animate({
        scrollTop: $('#unitesList').offset().top - 100
    }, 200);
}

/**
 * View a teaching unit
 *
 * @param {number} id The ID of the teaching unit to view
 */
function viewUnit(id) {
    // Find the unit in the allUnites array
    const unit = allUnites.find(unit => unit.id == id);

    if (!unit) {
        showAlert('danger', 'Teaching unit not found.');
        return;
    }

    // Verify that the unit belongs to the coordinator's filiere
    // This is a redundant check since the backend already filters by filiere_id,
    // but it's good practice to have frontend validation as well
    const coordinatorFiliereId = getCoordinatorFiliereId();

    if (coordinatorFiliereId && unit.filiere_id && unit.filiere_id != coordinatorFiliereId) {
        showAlert('danger', 'You can only view teaching units from your assigned department.');
        return;
    }

    // Redirect to the view page
    window.location.href = `descriptif.php?view=${id}`;
}

/**
 * Edit a teaching unit
 *
 * @param {number} id The ID of the teaching unit to edit
 */
function editUnit(id) {
    // Find the unit in the allUnites array
    const unit = allUnites.find(unit => unit.id == id);

    if (!unit) {
        showAlert('danger', 'Teaching unit not found.');
        return;
    }

    // Verify that the unit belongs to the coordinator's filiere
    const coordinatorFiliereId = getCoordinatorFiliereId();

    if (coordinatorFiliereId && unit.filiere_id && unit.filiere_id != coordinatorFiliereId) {
        showAlert('danger', 'You can only edit teaching units from your assigned department.');
        return;
    }

    // Redirect to the edit page
    window.location.href = `descriptif.php?edit=${id}`;
}

/**
 * Delete a teaching unit
 *
 * @param {number} id The ID of the teaching unit to delete
 */
function deleteUnit(id) {
    // Find the unit in the allUnites array
    const unit = allUnites.find(unit => unit.id == id);

    if (!unit) {
        showAlert('danger', 'Teaching unit not found.');
        return;
    }

    // Verify that the unit belongs to the coordinator's filiere
    const coordinatorFiliereId = getCoordinatorFiliereId();

    if (coordinatorFiliereId && unit.filiere_id && unit.filiere_id != coordinatorFiliereId) {
        showAlert('danger', 'You can only delete teaching units from your assigned department.');
        return;
    }

    if (confirm('Are you sure you want to delete this teaching unit?')) {
        // Make API request to delete the unit
        $.ajax({
            url: baseUrl + '/route/descriptifRoute.php',
            type: 'POST',
            data: {
                action: 'deleteUnite',
                id: id,
                coordinator_filiere_id: coordinatorFiliereId // Pass the coordinator's filiere_id for additional validation
            },
            dataType: 'json',
            success: function() {
                showAlert('success', 'Teaching unit deleted successfully.');
                loadUniteEnseignement();
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred while deleting the teaching unit.';

                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                }

                showAlert('danger', errorMessage);
            }
        });
    }
}

/**
 * Show an alert message
 *
 * @param {string} type The type of alert (success, danger, warning, info)
 * @param {string} message The message to display
 */
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    $('#alertContainer').html(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}