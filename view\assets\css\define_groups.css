.module-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.module-header {
    background-color: #f8f9fa;
    border-radius: 10px 10px 0 0;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}

.module-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.btn-save {
    background-color: #a3cfbb;
    border-color: #a3cfbb;
    color: #fff;
    transition: background-color 0.3s ease;
}

.btn-save:hover {
    background-color: #8bc0a8;
    border-color: #8bc0a8;
    color: #fff;
}

.niveau-section {
    margin-bottom: 30px;
    padding: 20px;
    border-radius: 10px;
    background-color: #f8f9fa;
}

.niveau-title {
    margin-bottom: 20px;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
}

.filters {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.submit-all-btn {
    margin-top: 20px;
    margin-bottom: 30px;
}

/* Cycle card styles */
.cycle-card {
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.cycle-card .card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.cycle-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.cycle-card .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.cycle-card .card-body {
    padding: 20px;
}

.cycle-card .modules-list {
    margin-top: 15px;
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
    display: none;
}

.sample-modules {
    margin-top: 10px;
}

.sample-modules .badge {
    font-size: 0.85rem;
    padding: 0.4rem 0.6rem;
    margin-right: 5px;
    margin-bottom: 5px;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sample-modules .badge.bg-light {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef;
}

.sample-modules .badge.bg-secondary {
    background-color: #6c757d !important;
}

.cycle-card .btn-primary {
    background-color: #6c9eaf;
    border-color: #6c9eaf;
    transition: all 0.3s ease;
}

.cycle-card .btn-primary:hover {
    background-color: #5a8a9a;
    border-color: #5a8a9a;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Pastel colors for different cycles */
.cycle1 .card-header {
    background-color: #e6f7ff;
}

.cycle2 .card-header {
    background-color: #fff2e6;
}

.cycle3 .card-header {
    background-color: #e6ffe6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .module-card-container {
        margin-bottom: 15px;
    }

    .niveau-section {
        padding: 15px;
    }

    .filters .form-group {
        margin-bottom: 10px;
    }
}

/* Page title animation */
.page-title-animation {
    animation: slideInFromTop 0.7s ease-out forwards;
}

.page-subtitle-animation {
    animation: slideInFromTop 0.7s ease-out 0.1s forwards;
    opacity: 0;
}

@keyframes slideInFromTop {
    0% {
        transform: translateY(-30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Alert styles */
.alert {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
    animation: fadeInOut 5s ease-in-out;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    animation: fadeInOut 7s ease-in-out;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-message {
    line-height: 1.5;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-20px); }
    5% { opacity: 1; transform: translateY(0); }
    90% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-20px); }
}

/* Custom styling for number inputs */
input[type="number"] {
    padding-right: 5px;
}

/* Remove spinner buttons from number inputs for a cleaner look */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

/* Focus styles */
.form-control:focus {
    border-color: #a3cfbb;
    box-shadow: 0 0 0 0.25rem rgba(163, 207, 187, 0.25);
}

/* Hover effect for the submit all button */
.btn-primary {
    background-color: #6c9eaf;
    border-color: #6c9eaf;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #5a8a9a;
    border-color: #5a8a9a;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Loading spinner styles */
.spinner-border {
    width: 3rem;
    height: 3rem;
}
