<?php
require_once __DIR__ . '/../config/db.php';

/**
 * Ensure the ue_preferences table exists, create it if it doesn't
 *
 * @return bool True if the table exists or was created successfully, false otherwise
 */
function ensureUePreferencesTable() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in ensureUePreferencesTable");
        return false;
    }

    // Check if the table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'ue_preferences'");

    if (!$tableExists) {
        error_log("Error checking if ue_preferences table exists: " . $conn->error);
        $conn->close();
        return false;
    }

    // Check if the table exists
    $exists = $tableExists->num_rows > 0;

    if (!$exists) {
        error_log("ue_preferences table does not exist, creating it now");

        // Create the table
        $createTableSQL = "CREATE TABLE `ue_preferences` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `id_enseignant` int(11) NOT NULL,
            `id_ue` int(11) NOT NULL,
            `reason` text COLLATE utf8mb4_general_ci DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_enseignant_ue` (`id_enseignant`, `id_ue`),
            KEY `fk_ue_preferences_enseignant` (`id_enseignant`),
            KEY `fk_ue_preferences_ue` (`id_ue`),
            CONSTRAINT `fk_ue_preferences_enseignant` FOREIGN KEY (`id_enseignant`) REFERENCES `enseignant` (`id_enseignant`) ON DELETE CASCADE ON UPDATE CASCADE,
            CONSTRAINT `fk_ue_preferences_ue` FOREIGN KEY (`id_ue`) REFERENCES `uniteenseignement` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $result = $conn->query($createTableSQL);

        if (!$result) {
            error_log("Error creating ue_preferences table: " . $conn->error);
            $conn->close();
            return false;
        }

        error_log("ue_preferences table created successfully");
        $conn->close();
        return true;
    } else {
        error_log("ue_preferences table exists");
        $conn->close();
        return true;
    }
}

/**
 * Get modules and their teaching units matching a teacher's specialty
 *
 * @param int $teacherId The teacher's ID
 * @return array Array of modules with their teaching units matching the teacher's specialty
 */
function getModulesAndUEsByTeacherSpecialty($teacherId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getModulesAndUEsByTeacherSpecialty");
        return ["error" => "Database connection error"];
    }

    // Ensure the teacher ID is safe
    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    // Get the teacher's specialty ID
    $specialtyQuery = "SELECT id_specialite FROM enseignant WHERE id_enseignant = '$teacherId'";
    $specialtyResult = mysqli_query($conn, $specialtyQuery);

    if (!$specialtyResult) {
        $error = mysqli_error($conn);
        error_log("Error getting teacher specialty: " . $error);
        mysqli_close($conn);
        return ["error" => "Error getting teacher specialty: " . $error];
    }

    if (mysqli_num_rows($specialtyResult) == 0) {
        mysqli_close($conn);
        return ["error" => "Teacher not found"];
    }

    $specialtyRow = mysqli_fetch_assoc($specialtyResult);
    $specialtyId = $specialtyRow['id_specialite'];

    // Get modules matching the teacher's specialty
    $sql = "SELECT m.*, f.nom_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre
            FROM module m
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id
            WHERE m.specialite_id = '$specialtyId'
            ORDER BY m.nom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getModulesAndUEsByTeacherSpecialty: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching modules: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $moduleId = $row['id'];

        // Get teaching units for this module
        $ueQuery = "SELECT ue.*
                    FROM uniteenseignement ue
                    WHERE ue.module_id = '$moduleId'
                    ORDER BY ue.type";

        $ueResult = mysqli_query($conn, $ueQuery);

        if (!$ueResult) {
            $error = mysqli_error($conn);
            error_log("Error fetching teaching units for module $moduleId: " . $error);
            continue;
        }

        $teachingUnits = [];
        while ($ueRow = mysqli_fetch_assoc($ueResult)) {
            $teachingUnits[] = $ueRow;
        }

        // Add teaching units to the module
        $row['teaching_units'] = $teachingUnits;

        $modules[] = $row;
    }

    mysqli_close($conn);
    return $modules;
}

/**
 * Save a teacher's UE preference
 *
 * @param array $data Preference data
 * @return bool|array True on success, error array on failure
 */
function saveUePreference($data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in saveUePreference");
        return ["error" => "Database connection error"];
    }

    // Check if the ue_preferences table exists
    if (!ensureUePreferencesTable()) {
        error_log("The ue_preferences table does not exist. Please create it manually.");
        mysqli_close($conn);
        return ["error" => "The ue_preferences table does not exist. Please contact the administrator."];
    }

    // Validate required fields
    if (empty($data['id_enseignant']) || empty($data['id_ue'])) {
        mysqli_close($conn);
        return ["error" => "Teacher ID and UE ID are required"];
    }

    // Check if the preference already exists
    $checkQuery = "SELECT id FROM ue_preferences
                  WHERE id_enseignant = ? AND id_ue = ?";

    $checkStmt = mysqli_prepare($conn, $checkQuery);

    if (!$checkStmt) {
        $error = mysqli_error($conn);
        error_log("Error preparing check statement: " . $error);
        mysqli_close($conn);
        return ["error" => "Error preparing check statement: " . $error];
    }

    mysqli_stmt_bind_param($checkStmt, "ii",
        $data['id_enseignant'],
        $data['id_ue']
    );

    mysqli_stmt_execute($checkStmt);
    mysqli_stmt_store_result($checkStmt);

    if (mysqli_stmt_num_rows($checkStmt) > 0) {
        // Preference already exists, update it
        mysqli_stmt_close($checkStmt);

        $updateQuery = "UPDATE ue_preferences
                       SET reason = ?
                       WHERE id_enseignant = ? AND id_ue = ?";

        $updateStmt = mysqli_prepare($conn, $updateQuery);

        if (!$updateStmt) {
            $error = mysqli_error($conn);
            error_log("Error preparing update statement: " . $error);
            mysqli_close($conn);
            return ["error" => "Error preparing update statement: " . $error];
        }

        $reason = isset($data['reason']) ? $data['reason'] : null;

        mysqli_stmt_bind_param($updateStmt, "sii",
            $reason,
            $data['id_enseignant'],
            $data['id_ue']
        );

        $result = mysqli_stmt_execute($updateStmt);

        if (!$result) {
            $error = mysqli_stmt_error($updateStmt);
            error_log("Error updating preference: " . $error);
            mysqli_stmt_close($updateStmt);
            mysqli_close($conn);
            return ["error" => "Error updating preference: " . $error];
        }

        mysqli_stmt_close($updateStmt);
        mysqli_close($conn);
        return true;
    } else {
        // Preference doesn't exist, insert it
        mysqli_stmt_close($checkStmt);

        $insertQuery = "INSERT INTO ue_preferences (id_enseignant, id_ue, reason)
                       VALUES (?, ?, ?)";

        $insertStmt = mysqli_prepare($conn, $insertQuery);

        if (!$insertStmt) {
            $error = mysqli_error($conn);
            error_log("Error preparing insert statement: " . $error);
            mysqli_close($conn);
            return ["error" => "Error preparing insert statement: " . $error];
        }

        $reason = isset($data['reason']) ? $data['reason'] : null;

        mysqli_stmt_bind_param($insertStmt, "iis",
            $data['id_enseignant'],
            $data['id_ue'],
            $reason
        );

        $result = mysqli_stmt_execute($insertStmt);

        if (!$result) {
            $error = mysqli_stmt_error($insertStmt);
            error_log("Error inserting preference: " . $error);
            mysqli_stmt_close($insertStmt);
            mysqli_close($conn);
            return ["error" => "Error inserting preference: " . $error];
        }

        mysqli_stmt_close($insertStmt);
        mysqli_close($conn);
        return true;
    }
}

/**
 * Get a teacher's UE preferences
 *
 * @param int $teacherId The teacher's ID
 * @return array Array of the teacher's UE preferences
 */
function getTeacherUePreferences($teacherId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherUePreferences");
        return ["error" => "Database connection error"];
    }

    // Check if the ue_preferences table exists
    if (!ensureUePreferencesTable()) {
        error_log("The ue_preferences table does not exist. Please create it manually.");
        mysqli_close($conn);
        return ["error" => "The ue_preferences table does not exist. Please contact the administrator."];
    }

    // Ensure the teacher ID is safe
    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    $sql = "SELECT up.*, ue.type as ue_type, ue.volume_horaire, ue.nb_groupes,
            m.id as module_id, m.nom as module_name, m.volume_total,
            f.nom_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre
            FROM ue_preferences up
            LEFT JOIN uniteenseignement ue ON up.id_ue = ue.id
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN specialite s ON m.specialite_id = s.id
            LEFT JOIN semestre sem ON m.id_semestre = sem.id
            WHERE up.id_enseignant = '$teacherId'
            ORDER BY m.nom, ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherUePreferences: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching preferences: " . $error];
    }

    $preferences = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $preferences[] = $row;
    }

    mysqli_close($conn);
    return $preferences;
}

/**
 * Delete a teacher's UE preference
 *
 * @param int $preferenceId The preference ID
 * @return bool|array True on success, error array on failure
 */
function deleteUePreference($preferenceId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in deleteUePreference");
        return ["error" => "Database connection error"];
    }

    // Ensure the preference ID is safe
    $preferenceId = mysqli_real_escape_string($conn, $preferenceId);

    $sql = "DELETE FROM ue_preferences WHERE id = '$preferenceId'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in deleteUePreference: " . $error);
        mysqli_close($conn);
        return ["error" => "Error deleting preference: " . $error];
    }

    mysqli_close($conn);
    return true;
}

/**
 * Save multiple UE preferences for a teacher
 *
 * @param int $teacherId The teacher's ID
 * @param array $ueIds Array of UE IDs
 * @param string $reason Optional reason for all preferences
 * @return bool|array True on success, error array on failure
 */
function saveMultipleUePreferences($teacherId, $ueIds, $reason = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in saveMultipleUePreferences");
        return ["error" => "Database connection error"];
    }

    // Check if the ue_preferences table exists
    if (!ensureUePreferencesTable()) {
        error_log("The ue_preferences table does not exist or could not be created");
        mysqli_close($conn);
        return ["error" => "The ue_preferences table does not exist or could not be created"];
    }

    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        // First, delete all existing preferences for this teacher
        $teacherId = mysqli_real_escape_string($conn, $teacherId);
        $deleteQuery = "DELETE FROM ue_preferences WHERE id_enseignant = '$teacherId'";
        $deleteResult = mysqli_query($conn, $deleteQuery);

        if (!$deleteResult) {
            throw new Exception("Error deleting existing preferences: " . mysqli_error($conn));
        }

        // If no UE IDs provided, just return success (all preferences were deleted)
        if (empty($ueIds)) {
            mysqli_commit($conn);
            mysqli_close($conn);
            return true;
        }

        // Prepare the insert statement
        $insertQuery = "INSERT INTO ue_preferences (id_enseignant, id_ue, reason) VALUES (?, ?, ?)";
        $insertStmt = mysqli_prepare($conn, $insertQuery);

        if (!$insertStmt) {
            throw new Exception("Error preparing insert statement: " . mysqli_error($conn));
        }

        // Insert each preference
        foreach ($ueIds as $ueId) {
            mysqli_stmt_bind_param($insertStmt, "iis", $teacherId, $ueId, $reason);
            $result = mysqli_stmt_execute($insertStmt);

            if (!$result) {
                throw new Exception("Error inserting preference for UE $ueId: " . mysqli_stmt_error($insertStmt));
            }
        }

        mysqli_stmt_close($insertStmt);
        mysqli_commit($conn);
        mysqli_close($conn);
        return true;
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        error_log("Error in saveMultipleUePreferences: " . $e->getMessage());
        mysqli_close($conn);
        return ["error" => $e->getMessage()];
    }
}
/**
 * Get the current academic year
 *
 * @return string Current academic year in format YYYY-YYYY
 */
if (!function_exists('getCurrentAcademicYear')) {
    function getCurrentAcademicYear() {
        $currentMonth = date('n'); // 1-12
        $currentYear = date('Y');

        // If we're in the second half of the calendar year (August-December),
        // the academic year is currentYear/currentYear+1
        if ($currentMonth >= 8) {
            return $currentYear . '-' . ($currentYear + 1);
        }
        // If we're in the first half of the calendar year (January-July),
        // the academic year is currentYear-1/currentYear
        else {
            return ($currentYear - 1) . '-' . $currentYear;
        }
    }
}
?>
