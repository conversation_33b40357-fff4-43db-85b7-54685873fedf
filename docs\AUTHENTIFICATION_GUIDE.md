# Guide d'authentification unifié

Ce guide explique comment utiliser le système d'authentification unifié pour toutes les pages de votre application.

## 1. Fichier de vérification d'authentification unifié

Le fichier `auth_check_unified.php` a été créé dans le répertoire `view/includes/`. Ce fichier gère l'authentification pour tous les types d'utilisateurs (admin, enseignant, chef de departement, coordinateur, vacataire, etudiant) et détermine automatiquement les rôles autorisés en fonction du chemin du fichier.

**Note importante**: Le système est configuré pour rediriger vers `/Projet-Web/index.php` pour la page de connexion. Si votre projet est déployé dans un autre répertoire, vous devrez modifier les chemins de redirection dans le fichier `auth_check_unified.php`.

## 2. Fonctionnement du système d'authentification

Le système d'authentification unifié fonctionne de la manière suivante :

1. Il vérifie d'abord si l'utilisateur est connecté
2. Il vérifie l'expiration de la session (30 minutes d'inactivité)
3. Il vérifie si l'adresse IP de l'utilisateur a changé
4. Il détermine ensuite les rôles autorisés pour la page actuelle :
   - Pour les pages dans `/view/admin/` : seul le rôle 'admin' est autorisé par défaut
   - Pour les pages dans `/view/enseignant/` : seul le rôle 'enseignant' est autorisé par défaut
   - Pour les pages dans `/view/chef/` : seul le rôle 'chef de departement' est autorisé par défaut
   - Pour les pages dans `/view/coordinator/` : seul le rôle 'coordinateur' est autorisé par défaut
   - Pour les pages dans `/view/vacataire/` : seul le rôle 'vacataire' est autorisé par défaut
   - Pour les pages dans `/view/etudiant/` : seul le rôle 'etudiant' est autorisé par défaut
   - Pour les autres pages : tous les utilisateurs connectés sont autorisés par défaut
5. Si l'utilisateur n'a pas un rôle autorisé, il est redirigé vers une page d'erreur

## 3. Ajouter la vérification d'authentification à une page

Pour chaque fichier PHP qui nécessite une authentification, ajoutez le code suivant au début du fichier :

```php
<?php
// Vérifier l'authentification
require_once '../includes/auth_check_unified.php';
?>
```

Si vous souhaitez spécifier manuellement les rôles autorisés pour une page, vous pouvez définir la variable `$allowed_roles` avant d'inclure le fichier d'authentification :

```php
<?php
// Définir les rôles autorisés pour cette page
$allowed_roles = ['admin', 'enseignant', 'chef de departement'];

// Vérifier l'authentification
require_once '../includes/auth_check_unified.php';
?>
```

## 4. Fonctions disponibles

Le fichier d'authentification unifié met à disposition plusieurs fonctions utiles :

- `isLoggedIn()` : Vérifie si l'utilisateur est connecté
- `isAdmin()` : Vérifie si l'utilisateur est un administrateur
- `isTeacher()` : Vérifie si l'utilisateur est un enseignant
- `isCoordinator()` : Vérifie si l'utilisateur est un coordinateur
- `isDepartmentHead()` : Vérifie si l'utilisateur est un chef de département
- `isVacataire()` : Vérifie si l'utilisateur est un vacataire
- `isStudent()` : Vérifie si l'utilisateur est un étudiant
- `hasRole($roles)` : Vérifie si l'utilisateur a l'un des rôles spécifiés
- `getCurrentUserRole()` : Obtient le rôle de l'utilisateur actuel
- `getCurrentUserId()` : Obtient l'ID de l'utilisateur actuel
- `getCurrentUsername()` : Obtient le nom d'utilisateur de l'utilisateur actuel
- `getCurrentUserName()` : Obtient le nom complet de l'utilisateur actuel

## 5. Exemple

Voici un exemple de fichier avant et après l'ajout de la vérification d'authentification :

### Avant :

```php
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page</title>
    <!-- ... -->
</head>
<body>
    <!-- ... -->
</body>
</html>
```

### Après (authentification standard) :

```php
<?php
// Vérifier l'authentification
require_once '../includes/auth_check_unified.php';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page</title>
    <!-- ... -->
</head>
<body>
    <!-- ... -->
</body>
</html>
```

### Après (avec rôles spécifiques) :

```php
<?php
// Définir les rôles autorisés pour cette page
$allowed_roles = ['admin', 'chef de departement'];

// Vérifier l'authentification
require_once '../includes/auth_check_unified.php';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page</title>
    <!-- ... -->
</head>
<body>
    <!-- ... -->
</body>
</html>
```

## 6. Test

Après avoir ajouté la vérification d'authentification à toutes les pages, vous devez tester que :

1. Les utilisateurs non authentifiés sont redirigés vers la page de connexion
2. Les utilisateurs authentifiés avec un rôle non autorisé sont redirigés vers la page d'erreur
3. Les utilisateurs authentifiés avec un rôle autorisé peuvent accéder aux pages correspondantes
4. La session expire après 30 minutes d'inactivité
5. La session est invalidée si l'adresse IP change
6. Le lien de déconnexion fonctionne correctement
