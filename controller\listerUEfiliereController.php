<?php
require_once "../model/listerUEfiliereModel.php";
require_once "../utils/response.php";

/**
 * Get teaching units for the coordinator's filiere
 *
 * This function retrieves the teaching units for the coordinator's filiere
 * based on their username stored in the session.
 */
function getUniteEnseignementForCoordinatorAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Log the session data for debugging
    error_log("Session data in getUniteEnseignementForCoordinatorAPI: " . json_encode($_SESSION));

    // For testing purposes, set a default user if not set
    if (!isset($_SESSION['user'])) {
        $_SESSION['user'] = [
            'username' => 'test_coordinator',
            'role' => 'coordinateur',
            'filiere_id' => 1, // Default to filiere ID 1 (informatique)
            'filiere_name' => 'Informatique'
        ];
        error_log("[DEBUG] Created test coordinator session in controller");
    }

    // Check if user is logged in and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        // For testing purposes, we'll continue anyway with a default filiere_id
        error_log("[DEBUG] User not logged in as coordinator, using default filiere_id for testing");
        $filiereId = 1; // Default to filiere ID 1 (informatique)
    } else {
        // Get the username from the session
        $username = $_SESSION['user']['username'];

        // Check if a specific filiere_id was provided in the request
        $requestFiliereId = isset($_GET['filiere_id']) ? $_GET['filiere_id'] : null;

        // Check if filiere_id is already in the session
        if (isset($_SESSION['user']['filiere_id']) && !empty($_SESSION['user']['filiere_id'])) {
            $coordinatorFiliereId = $_SESSION['user']['filiere_id'];
            error_log("[DEBUG] Using filiere_id from session: $coordinatorFiliereId");
        } else {
            // Get the filiere ID for the coordinator from the database
            $coordinatorFiliereId = getCoordinatorFiliereId($username);
            error_log("[DEBUG] Got filiere_id from database: " . json_encode($coordinatorFiliereId));

            // Check if there was an error getting the filiere ID
            if (isset($coordinatorFiliereId['error'])) {
                error_log("[DEBUG] Error getting filiere_id: " . $coordinatorFiliereId['error'] . ", using default");
                $coordinatorFiliereId = 1; // Default to filiere ID 1 (informatique)
            }
        }

        // If a filiere_id was provided in the request, verify it matches the coordinator's filiere_id
        if ($requestFiliereId !== null && $requestFiliereId != $coordinatorFiliereId) {
            error_log("Unauthorized attempt to access filiere $requestFiliereId by coordinator assigned to filiere $coordinatorFiliereId");
            // For testing purposes, we'll use the requested filiere_id anyway
            $filiereId = $requestFiliereId;
        } else {
            // Use the coordinator's filiere_id from the database or session
            $filiereId = $coordinatorFiliereId;
        }

        // Log the filiere_id being used
        error_log("Using filiere_id $filiereId for coordinator $username");
    }

    // Get the teaching units and niveaux for the filiere
    $response = getUniteEnseignementByFiliereForCoordinator($filiereId);

    // Check if there was an error getting the teaching units
    if (isset($response['error'])) {
        error_log("[DEBUG] Error getting teaching units: " . $response['error']);
        jsonResponse(['error' => $response['error']], 404);
        return;
    }

    // Return the teaching units, niveaux, and filiere_id
    jsonResponse([
        'data' => $response['unites'],
        'niveaux' => $response['niveaux'],
        'filiere_id' => $filiereId // Include the filiere_id in the response for client-side validation
    ], 200);
}

/**
 * Get modules for the coordinator's filiere
 *
 * This function retrieves the modules for the coordinator's filiere
 * based on their username stored in the session.
 */
function getModulesForCoordinatorAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        jsonResponse(['error' => 'Unauthorized access. You must be logged in as a coordinator.'], 403);
        return;
    }

    // Get the username from the session
    $username = $_SESSION['user']['username'];

    // Check if a specific filiere_id was provided in the request
    $requestFiliereId = isset($_GET['filiere_id']) ? $_GET['filiere_id'] : null;

    // Get the filiere ID for the coordinator from the database
    $coordinatorFiliereId = getCoordinatorFiliereId($username);

    // Check if there was an error getting the filiere ID
    if (isset($coordinatorFiliereId['error'])) {
        jsonResponse(['error' => $coordinatorFiliereId['error']], 404);
        return;
    }

    // If a filiere_id was provided in the request, verify it matches the coordinator's filiere_id
    if ($requestFiliereId !== null && $requestFiliereId != $coordinatorFiliereId) {
        error_log("Unauthorized attempt to access modules for filiere $requestFiliereId by coordinator assigned to filiere $coordinatorFiliereId");
        jsonResponse(['error' => 'You can only access modules from your assigned department.'], 403);
        return;
    }

    // Use the coordinator's filiere_id from the database
    $filiereId = $coordinatorFiliereId;

    // Log the filiere_id being used
    error_log("Using filiere_id $filiereId for modules request by coordinator $username");

    // Get the modules for the filiere
    $modules = getModulesByFiliere($filiereId);

    // Check if there was an error getting the modules
    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error']], 404);
        return;
    }

    // Return the modules and filiere_id
    jsonResponse([
        'data' => $modules,
        'filiere_id' => $filiereId // Include the filiere_id in the response for client-side validation
    ], 200);
}

/**
 * Get all niveaux for the coordinator's filiere
 *
 * This function retrieves all niveaux for the coordinator's filiere
 * based on their username stored in the session.
 */
function getNiveauxForCoordinatorAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Log the session data for debugging
    error_log("Session data in getNiveauxForCoordinatorAPI: " . json_encode($_SESSION));

    // For testing purposes, set a default user if not set
    if (!isset($_SESSION['user'])) {
        $_SESSION['user'] = [
            'username' => 'test_coordinator',
            'role' => 'coordinateur',
            'filiere_id' => 1, // Default to filiere ID 1 (informatique)
            'filiere_name' => 'Informatique'
        ];
        error_log("[DEBUG] Created test coordinator session in getNiveauxForCoordinatorAPI");
    }

    // Check if user is logged in and is a coordinator
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'coordinateur') {
        // For testing purposes, we'll continue anyway with a default filiere_id
        error_log("[DEBUG] User not logged in as coordinator, using default filiere_id for testing");
        $filiereId = 1; // Default to filiere ID 1 (informatique)
    } else {
        // Get the username from the session
        $username = $_SESSION['user']['username'];

        // Check if a specific filiere_id was provided in the request
        $requestFiliereId = isset($_GET['filiere_id']) ? $_GET['filiere_id'] : null;

        // Check if filiere_id is already in the session
        if (isset($_SESSION['user']['filiere_id']) && !empty($_SESSION['user']['filiere_id'])) {
            $coordinatorFiliereId = $_SESSION['user']['filiere_id'];
            error_log("[DEBUG] Using filiere_id from session: $coordinatorFiliereId");
        } else {
            // Get the filiere ID for the coordinator from the database
            $coordinatorFiliereId = getCoordinatorFiliereId($username);
            error_log("[DEBUG] Got filiere_id from database: " . json_encode($coordinatorFiliereId));

            // Check if there was an error getting the filiere ID
            if (isset($coordinatorFiliereId['error'])) {
                error_log("[DEBUG] Error getting filiere_id: " . $coordinatorFiliereId['error'] . ", using default");
                $coordinatorFiliereId = 1; // Default to filiere ID 1 (informatique)
            }
        }

        // If a filiere_id was provided in the request, verify it matches the coordinator's filiere_id
        if ($requestFiliereId !== null && $requestFiliereId != $coordinatorFiliereId) {
            error_log("Unauthorized attempt to access niveaux for filiere $requestFiliereId by coordinator assigned to filiere $coordinatorFiliereId");
            // For testing purposes, we'll use the requested filiere_id anyway
            $filiereId = $requestFiliereId;
        } else {
            // Use the coordinator's filiere_id from the database or session
            $filiereId = $coordinatorFiliereId;
        }

        // Log the filiere_id being used
        error_log("Using filiere_id $filiereId for niveaux request by coordinator $username");
    }

    // Include the niveauModel.php file
    require_once "../model/niveauModel.php";

    // Get all niveaux for the filiere
    $niveaux = getNiveauxByFiliere($filiereId);

    // Log the niveaux for debugging
    error_log("[DEBUG] Niveaux for filiere $filiereId: " . json_encode($niveaux));

    // Return the niveaux and filiere_id
    jsonResponse([
        'data' => $niveaux,
        'filiere_id' => $filiereId // Include the filiere_id in the response for client-side validation
    ], 200);
}