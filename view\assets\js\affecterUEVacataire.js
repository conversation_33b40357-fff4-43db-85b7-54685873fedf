/**
 * JavaScript for the Affecter UE Vacataire page
 * Handles assignment of teaching units to part-time lecturers
 */

// Global variables
let vacantUEs = [];
let availableVacataires = [];
let selectedUEs = [];

// DOM elements
const vacataireSelect = document.getElementById('vacataireSelect');
const teachingUnitsContainer = document.getElementById('teachingUnitsContainer');
const assignmentForm = document.getElementById('assignmentForm');
const assignButton = document.getElementById('assignButton');
const commentsInput = document.getElementById('commentsInput');
const alertContainer = document.getElementById('alertContainer');

// Statistics elements
const totalVacantEl = document.getElementById('totalVacant');
const totalAssignedEl = document.getElementById('totalAssigned');
const totalUnassignedEl = document.getElementById('totalUnassigned');
const totalVacatairesEl = document.getElementById('totalVacataires');

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    loadStatistics();
    loadVacataires();
    loadVacantUEs();

    // Set up form submission
    assignmentForm.addEventListener('submit', handleAssignment);

    // Set up vacataire selection change
    vacataireSelect.addEventListener('change', updateAssignButtonState);
});

/**
 * Load assignment statistics
 */
async function loadStatistics() {
    try {
        const response = await fetch('../../route/affecterUEVacataireRoute.php?action=getStatistics');

        // Check if response is ok
        if (!response.ok) {
            console.error('HTTP error! status:', response.status);
            return;
        }

        // Get response text first to check if it's valid JSON
        const responseText = await response.text();
        console.log('Statistics response:', responseText);

        // Try to parse as JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('Failed to parse JSON:', parseError);
            console.error('Response text:', responseText);
            return;
        }

        if (data.error) {
            console.error('Error loading statistics:', data.error);
            return;
        }

        const stats = data.data;
        totalVacantEl.textContent = stats.total_vacant || 0;
        totalAssignedEl.textContent = stats.total_assigned || 0;
        totalUnassignedEl.textContent = stats.unassigned || 0;
        totalVacatairesEl.textContent = stats.total_vacataires || 0;

    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}

/**
 * Load available vacataires
 */
async function loadVacataires() {
    try {
        const response = await fetch('../../route/affecterUEVacataireRoute.php?action=getVacataires');

        if (!response.ok) {
            console.error('HTTP error! status:', response.status);
            showAlert('Error loading vacataires: HTTP ' + response.status, 'danger');
            return;
        }

        const responseText = await response.text();
        console.log('Vacataires response:', responseText);

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('Failed to parse JSON:', parseError);
            showAlert('Error: Invalid response format', 'danger');
            return;
        }

        if (data.error) {
            showAlert('Error loading vacataires: ' + data.error, 'danger');
            return;
        }

        availableVacataires = data.data || [];
        populateVacataireSelect();

    } catch (error) {
        console.error('Error loading vacataires:', error);
        showAlert('Error loading vacataires. Please try again.', 'danger');
    }
}

/**
 * Populate the vacataire select dropdown
 */
function populateVacataireSelect() {
    // Clear existing options except the first one
    vacataireSelect.innerHTML = '<option value="">Choose a vacataire...</option>';

    if (availableVacataires.length === 0) {
        vacataireSelect.innerHTML += '<option value="" disabled>No vacataires available</option>';
        return;
    }

    availableVacataires.forEach(vacataire => {
        const option = document.createElement('option');
        option.value = vacataire.id_enseignant;
        option.textContent = vacataire.full_name + ' (' + (vacataire.specialite_nom || 'No specialty') + ')';
        vacataireSelect.appendChild(option);
    });
}

/**
 * Load vacant teaching units
 */
async function loadVacantUEs() {
    try {
        const response = await fetch('../../route/affecterUEVacataireRoute.php?action=getVacantUEs');

        if (!response.ok) {
            console.error('HTTP error! status:', response.status);
            showAlert('Error loading vacant teaching units: HTTP ' + response.status, 'danger');
            return;
        }

        const responseText = await response.text();
        console.log('Vacant UEs response:', responseText);

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('Failed to parse JSON:', parseError);
            showAlert('Error: Invalid response format', 'danger');
            return;
        }

        if (data.error) {
            showAlert('Error loading vacant teaching units: ' + data.error, 'danger');
            return;
        }

        vacantUEs = data.data || [];
        renderVacantUEs();

    } catch (error) {
        console.error('Error loading vacant teaching units:', error);
        showAlert('Error loading vacant teaching units. Please try again.', 'danger');
    }
}

/**
 * Render vacant teaching units as checkboxes
 */
function renderVacantUEs() {
    if (vacantUEs.length === 0) {
        teachingUnitsContainer.innerHTML =
            '<div class="alert alert-info text-center">' +
                '<i class="fas fa-info-circle me-2"></i>' +
                'No vacant teaching units available for assignment in your program.' +
            '</div>';
        return;
    }

    let html = '<div class="row">';

    vacantUEs.forEach((ue, index) => {
        const ueTypeDisplay = translateUEType(ue.ue_type);

        html += '<div class="col-md-6 col-lg-4 mb-3">' +
                '<div class="card ue-card" data-ue-id="' + ue.ue_id + '">' +
                    '<div class="card-body">' +
                        '<div class="form-check">' +
                            '<input class="form-check-input ue-checkbox" type="checkbox" ' +
                                   'value="' + ue.ue_id + '" id="ue_' + ue.ue_id + '" ' +
                                   'onchange="handleUESelection(this)">' +
                            '<label class="form-check-label w-100" for="ue_' + ue.ue_id + '">' +
                                '<div class="ue-info">' +
                                    '<h6 class="ue-module">' + escapeHtml(ue.module_name) + '</h6>' +
                                    '<div class="ue-details">' +
                                        '<span class="badge bg-primary-light text-primary">' + ueTypeDisplay + '</span>' +
                                        '<span class="text-muted ms-2">' + ue.volume_horaire + 'h</span>' +
                                        '<span class="text-muted ms-2">' + ue.nb_groupes + ' groups</span>' +
                                    '</div>' +
                                    '<div class="ue-meta">' +
                                        '<small class="text-muted">' +
                                            escapeHtml(ue.niveau) + ' - S' + escapeHtml(ue.semestre) +
                                        '</small>' +
                                    '</div>' +
                                '</div>' +
                            '</label>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>';
    });

    html += '</div>';
    teachingUnitsContainer.innerHTML = html;
}

/**
 * Handle teaching unit selection
 */
function handleUESelection(checkbox) {
    const ueId = parseInt(checkbox.value);
    const card = checkbox.closest('.ue-card');

    if (checkbox.checked) {
        selectedUEs.push(ueId);
        card.classList.add('selected');
    } else {
        selectedUEs = selectedUEs.filter(id => id !== ueId);
        card.classList.remove('selected');
    }

    updateAssignButtonState();
}

/**
 * Update the state of the assign button
 */
function updateAssignButtonState() {
    const hasVacataire = vacataireSelect.value !== '';
    const hasSelectedUEs = selectedUEs.length > 0;

    assignButton.disabled = !(hasVacataire && hasSelectedUEs);

    // Update button text to show count
    if (hasSelectedUEs) {
        assignButton.innerHTML =
            '<i class="fas fa-save me-1"></i>' +
            'Assign ' + selectedUEs.length + ' Unit' + (selectedUEs.length > 1 ? 's' : '');
    } else {
        assignButton.innerHTML =
            '<i class="fas fa-save me-1"></i>' +
            'Assign Selected Units';
    }
}

/**
 * Handle form submission for assignment
 */
async function handleAssignment(event) {
    event.preventDefault();

    if (selectedUEs.length === 0) {
        showAlert('Please select at least one teaching unit to assign.', 'warning');
        return;
    }

    if (!vacataireSelect.value) {
        showAlert('Please select a vacataire.', 'warning');
        return;
    }

    // Confirm assignment
    const vacataireName = vacataireSelect.options[vacataireSelect.selectedIndex].text;
    const confirmMessage = 'Are you sure you want to assign ' + selectedUEs.length + ' teaching unit(s) to ' + vacataireName + '?';

    if (!confirm(confirmMessage)) {
        return;
    }

    // Disable form during submission
    assignButton.disabled = true;
    assignButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Assigning...';

    try {
        const response = await fetch('../../route/affecterUEVacataireRoute.php?action=assignUEs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                vacataire_id: vacataireSelect.value,
                ue_ids: selectedUEs,
                comments: commentsInput.value.trim()
            })
        });

        const data = await response.json();

        if (data.error) {
            showAlert('Assignment failed: ' + data.error, 'danger');
        } else {
            showAlert(data.message, 'success');

            // Reset form and reload data
            clearSelection();
            loadStatistics();
            loadVacantUEs();
        }

    } catch (error) {
        console.error('Error during assignment:', error);
        showAlert('Assignment failed. Please try again.', 'danger');
    } finally {
        updateAssignButtonState();
    }
}

/**
 * Clear all selections
 */
function clearSelection() {
    selectedUEs = [];
    vacataireSelect.value = '';
    commentsInput.value = '';

    // Uncheck all checkboxes and remove selected class
    document.querySelectorAll('.ue-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.closest('.ue-card').classList.remove('selected');
    });

    updateAssignButtonState();
}

/**
 * Refresh all data
 */
function refreshData() {
    loadStatistics();
    loadVacataires();
    loadVacantUEs();
    clearSelection();
}

/**
 * Translate UE type to English
 */
function translateUEType(type) {
    const translations = {
        'Cours': 'Lecture',
        'TD': 'Tutorial',
        'TP': 'Practical'
    };
    return translations[type] || type;
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertHtml =
        '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
            '<i class="fas fa-' + getAlertIcon(type) + ' me-2"></i>' +
            message +
            '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
        '</div>';

    alertContainer.innerHTML = alertHtml;

    // Auto-dismiss after 5 seconds for success messages
    if (type === 'success') {
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}

/**
 * Get appropriate icon for alert type
 */
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
