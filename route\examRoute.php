<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

try {
    require_once "../controller/examController.php";

    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');

    // Handle preflight requests
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Get exam by ID
        if (isset($_GET['id'])) {
            getExamByIdAPI($_GET['id']);
        }
        // Get exams by filters
        else if (isset($_GET['filiere']) || isset($_GET['niveau']) || isset($_GET['groupe']) || isset($_GET['semestre'])) {
            $filiere = isset($_GET['filiere']) ? $_GET['filiere'] : null;
            $niveau = isset($_GET['niveau']) ? $_GET['niveau'] : null;
            $groupe = isset($_GET['groupe']) ? $_GET['groupe'] : null;
            $semestre = isset($_GET['semestre']) ? $_GET['semestre'] : null;

            getExamsByFiltersAPI($filiere, $niveau, $groupe, $semestre);
        }
        // Get all exams
        else {
            getAllExamsAPI();
        }
        break;

    case 'POST':
        // Add a new exam
        $data = json_decode(file_get_contents("php://input"), true);
        if (!$data) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data']);
            break;
        }
        addExamAPI($data);
        break;

    case 'PUT':
        // Update an exam
        $data = json_decode(file_get_contents("php://input"), true);
        if (!$data) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data']);
            break;
        }
        updateExamAPI($data);
        break;

    case 'DELETE':
        // Delete an exam
        if (isset($_GET['id'])) {
            deleteExamAPI($_GET['id']);
        } else {
            http_response_code(400);
            echo json_encode(['error' => 'Missing ID parameter']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

} catch (Exception $e) {
    // Clean any output that might have been generated
    ob_clean();

    // Set headers
    header('Content-Type: application/json');
    http_response_code(500);

    // Log the error
    error_log("Exception in examRoute.php: " . $e->getMessage());

    // Return a JSON error response
    echo json_encode([
        'error' => 'Server error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}

// End output buffering
ob_end_flush();
?>