-- Script pour créer la table configuration_charge si elle n'existe pas déjà
-- Cette table stocke les configurations GLOBALES de charge de travail minimale pour les enseignants

CREATE TABLE IF NOT EXISTS `configuration_charge` (
  `id_config` int(11) NOT NULL AUTO_INCREMENT,
  `annee_universitaire` varchar(9) COLLATE utf8mb4_general_ci NOT NULL,
  `role` varchar(20) COLLATE utf8mb4_general_ci NOT NULL,
  `charge_minimale` int(11) NOT NULL,
  PRIMARY KEY (`id_config`),
  UNIQUE KEY `unique_annee_role` (`annee_universitaire`, `role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Commentaires sur les colonnes :
-- id_config : Identifiant unique de la configuration
-- annee_universitaire : Année universitaire au format YYYY-YYYY (ex: 2024-2025)
-- role : R<PERSON>le d'enseignant concerné (enseignant ou vacataire)
-- charge_minimale : Nombre d'heures minimum que doit enseigner un professeur par année

-- Index et contraintes :
-- unique_annee_role : Empêche les doublons pour une même année et un même rôle
-- Les configurations sont GLOBALES et s'appliquent à tous les départements

-- Exemples d'insertion :
-- INSERT INTO configuration_charge (annee_universitaire, role, charge_minimale)
-- VALUES ('2024-2025', 'enseignant', 192);

-- INSERT INTO configuration_charge (annee_universitaire, role, charge_minimale)
-- VALUES ('2024-2025', 'vacataire', 96);

-- INSERT INTO configuration_charge (annee_universitaire, role, charge_minimale)
-- VALUES ('2025-2026', 'enseignant', 200);

-- INSERT INTO configuration_charge (annee_universitaire, role, charge_minimale)
-- VALUES ('2025-2026', 'vacataire', 100);
