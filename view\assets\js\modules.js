// Debug message to check if this script is loaded
console.log('Modules.js loaded successfully');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded event fired');
    // Filter functionality
    const filterButtons = document.querySelectorAll('.filter-btn');
    const moduleCategories = document.querySelectorAll('.module-category');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');

            // Show/hide categories based on filter
            if (filter === 'all') {
                moduleCategories.forEach(category => {
                    category.style.display = 'block';
                });
            } else {
                moduleCategories.forEach(category => {
                    if (category.id === filter) {
                        category.style.display = 'block';
                    } else {
                        category.style.display = 'none';
                    }
                });
            }
        });
    });

    // View module details
    const viewButtons = document.querySelectorAll('.btn-view-module');
    const moduleDetailsModal = new bootstrap.Modal(document.getElementById('moduleDetailsModal'));
    const moduleDetailsContent = document.getElementById('moduleDetailsContent');

    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const moduleId = this.getAttribute('data-id');
            const moduleCard = this.closest('.module-card');
            const moduleTitle = moduleCard.querySelector('.module-title').textContent;
            const moduleEmoji = moduleCard.querySelector('.module-emoji').textContent;
            const moduleColor = moduleCard.style.backgroundColor;

            // For now, we'll just display some placeholder content
            // In the future, this would fetch data from the backend
            moduleDetailsContent.innerHTML = `
                <div class="module-detail-header" style="background-color: ${moduleColor}; padding: 1.5rem; border-radius: 10px; margin-bottom: 1.5rem;">
                    <div style="font-size: 3rem; text-align: center; margin-bottom: 1rem;">${moduleEmoji}</div>
                    <h3 style="text-align: center; margin-bottom: 0;">${moduleTitle}</h3>
                </div>
                <div class="module-detail-content">
                    <p><strong>Module ID:</strong> ${moduleId}</p>
                    <p><strong>Description:</strong> This is a placeholder description for the ${moduleTitle} module. In the future, this would be fetched from the database.</p>
                    <p><strong>Professor:</strong> Dr. Example Professor</p>
                    <p><strong>Schedule:</strong> Monday and Wednesday, 10:00 AM - 12:00 PM</p>
                    <p><strong>Location:</strong> Room 101, Building A</p>
                    <div class="module-resources">
                        <h4>Resources</h4>
                        <ul>
                            <li><a href="#">Syllabus</a></li>
                            <li><a href="#">Lecture Notes</a></li>
                            <li><a href="#">Assignments</a></li>
                        </ul>
                    </div>
                </div>
            `;

            moduleDetailsModal.show();
        });
    });

    // Add hover effects to module cards
    const moduleCards = document.querySelectorAll('.module-card');

    moduleCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.1)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.05)';
        });
    });
});