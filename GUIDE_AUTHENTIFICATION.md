# Guide d'authentification unifié

Ce guide explique comment utiliser le système d'authentification unifié pour toutes les pages de votre application.

## 1. Fichier de vérification d'authentification unifié

Un fichier `auth_check_unified.php` a été créé dans le répertoire `view/includes/`. Ce fichier gère l'authentification pour tous les types d'utilisateurs (admin, enseignant, étudiant) et détermine automatiquement les rôles autorisés en fonction du chemin du fichier.

## 2. Fonctionnement du système d'authentification

Le système d'authentification unifié fonctionne de la manière suivante :

1. Il vérifie d'abord si l'utilisateur est connecté
2. Il détermine ensuite les rôles autorisés pour la page actuelle :
   - Pour les pages dans `/view/admin/` : seul le rôle 'admin' est autorisé par défaut
   - Pour les pages dans `/view/teacher/` : seul le rôle 'enseignant' est autorisé par défaut
   - Pour les pages dans `/view/student/` : seul le rôle 'etudiant' est autorisé par défaut
   - Pour les autres pages : tous les utilisateurs connectés sont autorisés par défaut
3. Si l'utilisateur n'a pas un rôle autorisé, il est redirigé vers une page d'erreur

## 3. Ajouter la vérification d'authentification à une page

Pour chaque fichier PHP qui nécessite une authentification, ajoutez le code suivant au début du fichier :

```php
<?php
// Vérifier l'authentification
require_once '../includes/auth_check_unified.php';
?>
```

Si vous souhaitez spécifier manuellement les rôles autorisés pour une page, vous pouvez définir la variable `$allowed_roles` avant d'inclure le fichier d'authentification :

```php
<?php
// Définir les rôles autorisés pour cette page
$allowed_roles = ['admin', 'enseignant'];

// Vérifier l'authentification
require_once '../includes/auth_check_unified.php';
?>
```

## 4. Exemple

Voici un exemple de fichier avant et après l'ajout de la vérification d'authentification :

### Avant :

```php
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniAdmin - Page</title>
    <!-- ... -->
</head>
<body>
    <!-- ... -->
</body>
</html>
```

### Après (authentification standard) :

```php
<?php
// Vérifier l'authentification
require_once '../includes/auth_check_unified.php';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniAdmin - Page</title>
    <!-- ... -->
</head>
<body>
    <!-- ... -->
</body>
</html>
```

### Après (avec rôles spécifiques) :

```php
<?php
// Définir les rôles autorisés pour cette page
$allowed_roles = ['admin', 'enseignant'];

// Vérifier l'authentification
require_once '../includes/auth_check_unified.php';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniAdmin - Page</title>
    <!-- ... -->
</head>
<body>
    <!-- ... -->
</body>
</html>
```

## 5. Test

Après avoir ajouté la vérification d'authentification à toutes les pages, vous devez tester que :

1. Les utilisateurs non authentifiés sont redirigés vers la page de connexion
2. Les utilisateurs authentifiés avec un rôle non autorisé sont redirigés vers la page d'erreur
3. Les utilisateurs authentifiés avec un rôle autorisé peuvent accéder aux pages correspondantes
4. Le lien de déconnexion fonctionne correctement

## 6. Enregistrement des visites

Le système d'authentification enregistre automatiquement les visites des utilisateurs dans la base de données. Ces données peuvent être utilisées pour générer des statistiques sur l'utilisation de l'application.
