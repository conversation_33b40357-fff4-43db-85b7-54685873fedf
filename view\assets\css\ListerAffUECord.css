/* ===================================
   ListerAffUECord - Coordinator Teaching Unit Assignments
   Beautiful pastel design with smooth animations
   =================================== */

:root {
    /* Pastel Color Palette */
    --pastel-blue: #E3F2FD;
    --pastel-blue-dark: #BBDEFB;
    --pastel-purple: #F3E5F5;
    --pastel-purple-dark: #E1BEE7;
    --pastel-green: #E8F5E8;
    --pastel-green-dark: #C8E6C9;
    --pastel-pink: #FCE4EC;
    --pastel-pink-dark: #F8BBD9;
    --pastel-orange: #FFF3E0;
    --pastel-orange-dark: #FFE0B2;
    --pastel-yellow: #FFFDE7;
    --pastel-yellow-dark: #FFF9C4;
    --pastel-teal: #E0F2F1;
    --pastel-teal-dark: #B2DFDB;
    --pastel-lavender: #F1F8E9;
    --pastel-lavender-dark: #DCEDC8;

    /* Primary Colors */
    --primary-color: #6366F1;
    --primary-light: #E0E7FF;
    --primary-dark: #4F46E5;

    /* Text Colors */
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --text-muted: #9CA3AF;

    /* Background Colors */
    --bg-primary: #FFFFFF;
    --bg-secondary: #F9FAFB;
    --bg-tertiary: #F3F4F6;

    /* Shadow Colors */
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-heavy: rgba(0, 0, 0, 0.15);

    /* Border Colors */
    --border-light: #E5E7EB;
    --border-medium: #D1D5DB;

    /* Animation Timings */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===================================
   Page Layout & Container Styles
   =================================== */

.lister-aff-container {
    background: linear-gradient(135deg, var(--pastel-blue) 0%, var(--pastel-lavender) 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.page-header {
    background: var(--bg-primary);
    padding: 2rem;
    margin-bottom: 2rem;
    border: none;
}

.page-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-title::before {
    content: '📋';
    font-size: 2rem;
    background: linear-gradient(135deg, var(--pastel-blue) 0%, var(--primary-light) 100%);
    padding: 0.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--pastel-blue) 100%);
    border-radius: 2px;
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 400;
}

.academic-year-badge {
    background: linear-gradient(135deg, var(--pastel-teal) 0%, var(--pastel-green) 100%);
    color: var(--primary-dark);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    border: 2px solid var(--pastel-teal-dark);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
    transition: all var(--transition-medium);
    position: relative;
    z-index: 2;
}

.academic-year-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.3);
}

/* ===================================
   Filter Section Styles
   =================================== */

.filters-card {
    background: var(--bg-primary);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px var(--shadow-light);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.filters-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        var(--pastel-blue) 0%,
        var(--pastel-purple) 25%,
        var(--pastel-pink) 50%,
        var(--pastel-orange) 75%,
        var(--pastel-green) 100%);
    animation: shimmer 3s ease-in-out infinite;
}

.filter-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-select {
    background: var(--bg-secondary);
    border: 2px solid var(--border-light);
    border-radius: 15px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    color: var(--text-primary);
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.filter-select::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.filter-select:hover::before {
    left: 100%;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px var(--primary-light);
    transform: translateY(-2px);
}

.filter-select.filter-active {
    background: linear-gradient(135deg, var(--pastel-purple) 0%, var(--pastel-pink) 100%);
    border-color: var(--primary-color);
    color: var(--primary-dark);
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
}

/* ===================================
   Affectations Container Styles
   =================================== */

.affectations-main-card {
    background: var(--bg-primary);
    border-radius: 20px;
    box-shadow: 0 8px 32px var(--shadow-light);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.affectations-header {
    background: linear-gradient(135deg, var(--pastel-blue) 0%, var(--pastel-purple) 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-light);
    position: relative;
}

.affectations-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.affectation-count {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 700;
    font-size: 0.85rem;
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
    animation: pulse-glow 2s ease-in-out infinite;
}

.filter-status {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-style: italic;
    margin-left: 1rem;
}

.affectations-body {
    padding: 2rem;
    background: var(--bg-secondary);
}

/* ===================================
   Semester Section Styles
   =================================== */

.semester-section {
    margin-bottom: 3rem;
    animation: slideInUp 0.6s ease-out;
}

.semester-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid;
    border-image: linear-gradient(90deg, var(--primary-color), var(--pastel-purple)) 1;
    position: relative;
    display: inline-block;
}

.semester-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--pastel-pink), var(--pastel-orange));
    animation: expandWidth 1s ease-out 0.5s forwards;
}

/* ===================================
   Module Card Styles
   =================================== */

.module-card {
    background: var(--bg-primary);
    border-radius: 20px;
    border: none;
    box-shadow: 0 8px 32px var(--shadow-light);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.module-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--pastel-blue) 0%, var(--pastel-purple) 100%);
    border-bottom: 1px solid var(--border-light);
}

.module-name {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
}

.module-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
    font-weight: 500;
}

.module-body {
    padding: 1.5rem;
    background: var(--bg-primary);
}

/* ===================================
   UE List & Item Styles
   =================================== */

.ue-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ue-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light);
}

.ue-item:last-child {
    border-bottom: none;
}

/* ===================================
   UE Type Badge Styles
   =================================== */

.ue-type {
    font-weight: 700;
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    min-width: 60px;
    text-align: center;
    margin-right: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Lecture Type (Cours) */
.ue-type:contains("LECTURE"),
.ue-item .ue-type[data-type="Lecture"] {
    background: linear-gradient(135deg, var(--pastel-green) 0%, var(--pastel-green-dark) 100%);
    color: #2E7D32;
    border: 2px solid var(--pastel-green-dark);
}

/* Tutorial Type (TD) */
.ue-type:contains("TUTORIAL"),
.ue-item .ue-type[data-type="Tutorial"] {
    background: linear-gradient(135deg, var(--pastel-orange) 0%, var(--pastel-orange-dark) 100%);
    color: #E65100;
    border: 2px solid var(--pastel-orange-dark);
}

/* Practical Type (TP) */
.ue-type:contains("PRACTICAL"),
.ue-item .ue-type[data-type="Practical"] {
    background: linear-gradient(135deg, var(--pastel-pink) 0%, var(--pastel-pink-dark) 100%);
    color: #C2185B;
    border: 2px solid var(--pastel-pink-dark);
}

/* Default fallback for dynamic content */
.ue-type {
    background: linear-gradient(135deg, var(--pastel-blue) 0%, var(--pastel-blue-dark) 100%);
    color: var(--primary-dark);
    border: 2px solid var(--pastel-blue-dark);
}

.ue-details {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ue-teacher {
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-primary);
}

.ue-teacher i {
    margin-right: 0.75rem;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.ue-hours {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
    background: var(--pastel-yellow);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    border: 1px solid var(--pastel-yellow-dark);
}

/* ===================================
   Empty State & Loading Styles
   =================================== */

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
    background: linear-gradient(135deg, var(--pastel-blue) 0%, var(--pastel-purple) 100%);
    border-radius: 20px;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.empty-state::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, var(--pastel-pink) 0%, transparent 70%);
    opacity: 0.2;
    animation: rotate 20s linear infinite;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    opacity: 0.7;
    animation: bounce 2s ease-in-out infinite;
    position: relative;
    z-index: 2;
}

.empty-state h5 {
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.5rem;
    position: relative;
    z-index: 2;
}

.empty-state p {
    font-size: 1rem;
    color: var(--text-secondary);
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

.loading-state {
    text-align: center;
    padding: 3rem 2rem;
    background: var(--bg-primary);
    border-radius: 20px;
    margin: 2rem 0;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3rem;
    border-color: var(--primary-color);
    border-right-color: transparent;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 1rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 500;
}

/* ===================================
   Alert & Notification Styles
   =================================== */

.alert-container {
    margin-bottom: 2rem;
}

.alert {
    border: none;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    font-weight: 500;
    box-shadow: 0 4px 15px var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-info {
    background: linear-gradient(135deg, var(--pastel-blue) 0%, var(--pastel-teal) 100%);
    color: #0277BD;
    border-left: 4px solid #0277BD;
}

.alert-success {
    background: linear-gradient(135deg, var(--pastel-green) 0%, var(--pastel-teal) 100%);
    color: #2E7D32;
    border-left: 4px solid #2E7D32;
}

.alert-warning {
    background: linear-gradient(135deg, var(--pastel-yellow) 0%, var(--pastel-orange) 100%);
    color: #F57C00;
    border-left: 4px solid #F57C00;
}

.alert-danger {
    background: linear-gradient(135deg, var(--pastel-pink) 0%, var(--pastel-orange) 100%);
    color: #C62828;
    border-left: 4px solid #C62828;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

.btn-close:hover {
    opacity: 1;
}

/* ===================================
   Responsive Design
   =================================== */

@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }

    .page-header {
        padding: 1.5rem;
    }

    .filters-card {
        padding: 1.5rem;
    }

    .affectations-body {
        padding: 1.5rem;
    }

    .module-card {
        margin-bottom: 1rem;
    }

    .module-header {
        padding: 1rem;
    }

    .module-body {
        padding: 1rem;
    }

    .ue-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .ue-type {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .ue-details {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .semester-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .lister-aff-container {
        padding: 1rem 0;
    }

    .page-title {
        font-size: 1.8rem;
    }

    .academic-year-badge {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .filter-select {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .module-name {
        font-size: 1.1rem;
    }

    .ue-teacher {
        font-size: 0.9rem;
    }

    .ue-hours {
        font-size: 0.85rem;
    }
}

/* ===================================
   Animation Keyframes
   =================================== */

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes pulse-glow {
    0% {
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
    }
    50% {
        box-shadow: 0 4px 25px rgba(99, 102, 241, 0.5);
    }
    100% {
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-10px) rotate(1deg);
    }
    66% {
        transform: translateY(5px) rotate(-1deg);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes expandWidth {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes wiggle {
    0%, 7% {
        transform: rotateZ(0);
    }
    15% {
        transform: rotateZ(-15deg);
    }
    20% {
        transform: rotateZ(10deg);
    }
    25% {
        transform: rotateZ(-10deg);
    }
    30% {
        transform: rotateZ(6deg);
    }
    35% {
        transform: rotateZ(-4deg);
    }
    40%, 100% {
        transform: rotateZ(0);
    }
}

/* ===================================
   Animation Classes
   =================================== */

.animated-card {
    animation: fadeIn 0.6s ease-out forwards;
}

.animate-delay-1 { animation-delay: 0.1s; }
.animate-delay-2 { animation-delay: 0.2s; }
.animate-delay-3 { animation-delay: 0.3s; }
.animate-delay-4 { animation-delay: 0.4s; }
.animate-delay-5 { animation-delay: 0.5s; }

.hover-lift:hover {
    transform: translateY(-5px);
    transition: transform var(--transition-medium);
}

.hover-scale:hover {
    transform: scale(1.05);
    transition: transform var(--transition-medium);
}

.hover-glow:hover {
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
    transition: box-shadow var(--transition-medium);
}

/* ===================================
   Utility Classes
   =================================== */

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.bg-gradient-pastel {
    background: linear-gradient(135deg, var(--pastel-blue) 0%, var(--pastel-purple) 100%);
}

.shadow-soft {
    box-shadow: 0 8px 32px var(--shadow-light);
}

.shadow-medium {
    box-shadow: 0 12px 40px var(--shadow-medium);
}

.shadow-heavy {
    box-shadow: 0 16px 48px var(--shadow-heavy);
}

.border-radius-lg {
    border-radius: 20px;
}

.border-radius-xl {
    border-radius: 25px;
}

.transition-all {
    transition: all var(--transition-medium);
}

.transition-fast {
    transition: all var(--transition-fast);
}

.transition-slow {
    transition: all var(--transition-slow);
}