/* Timetable Display Styles */
.schedule-container {
    margin: 30px auto;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    max-width: 95%;
    width: 100%;
    transition: all 0.3s ease;
}

@media (min-width: 1200px) {
    .schedule-container {
        max-width: 90%;
    }
}

@media (min-width: 1600px) {
    .schedule-container {
        max-width: 85%;
    }
}

.schedule-header {
    display: flex;
    align-items: center;
    padding: 18px 24px;
    border-bottom: 1px solid #eaeaea;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.schedule-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.schedule-title i {
    font-size: 1.5rem;
    color: #0f172a;
}

.schedule-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.schedule-header .schedule-actions {
    margin-left: auto;
    display: flex;
    gap: 10px;
}

.schedule-header .add-session-btn {
    background-color: #4361ee;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 16px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(67, 97, 238, 0.3);
}

.schedule-header .add-session-btn:hover {
    background-color: #3a56d4;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(67, 97, 238, 0.4);
}

.schedule-header .download-pdf-btn {
    background-color: #e63946;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 16px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(230, 57, 70, 0.3);
}

.schedule-header .download-pdf-btn:hover {
    background-color: #d62839;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(230, 57, 70, 0.4);
}

/* Removed testing buttons CSS */

.timetable-wrapper {
    overflow-x: auto;
    width: 100%; /* Ensure full width */
}

.timetable {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 0 1px #eaeaea;
    table-layout: fixed; /* Ensure columns have consistent widths */
}

.timetable th, .timetable td {
    border: 1px solid #eaeaea;
    padding: 12px;
    text-align: center;
    vertical-align: top;
    transition: background-color 0.2s ease;
}

.timetable th {
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    font-weight: 600;
    color: #343a40;
    padding: 14px 14px 5px 14px;
    border-bottom: 2px solid #dee2e6;
    position: relative;
}

.day-date {
    font-size: 0.8rem;
    font-weight: normal;
    color: #6c757d;
    margin-top: 5px;
    padding-top: 3px;
    border-top: 1px dashed #dee2e6;
}

.timetable th:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, #4361ee, #3a56d4);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.timetable th:hover:after {
    transform: scaleX(1);
}

.timetable th:first-child {
    width: 100px;
}

/* Ensure all time columns have the same width */
.timetable th.time-header {
    width: calc((100% - 100px) / 4); /* Divide remaining space equally among 4 time columns */
}

.time-slot {
    font-weight: 600;
    color: #343a40;
    background: linear-gradient(to right, #f8f9fa, #e9ecef);
    border-right: 2px solid #dee2e6;
    position: relative;
}

.time-slot:after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 2px;
    background: linear-gradient(to bottom, #4361ee, #3a56d4);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.time-slot:hover:after {
    transform: scaleY(1);
}

.class-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 12px;
    margin: 5px;
    text-align: left;
    min-height: 130px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    border-top: 4px solid #e9ecef;
}

.class-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

/* Card header with actions */
.class-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.class-actions {
    display: flex;
    gap: 5px;
}

/* Delete button */
.btn-delete {
    background-color: transparent;
    border: none;
    color: #dc3545;
    font-size: 16px;
    padding: 2px 5px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.btn-delete:hover {
    background-color: #dc3545;
    color: white;
}

/* Session type-based colors */
/* Default/other session type */
.class-card {
    background-color: #f8f9fa;
    border-top: 4px solid #6c757d;
}

.class-card .class-subject {
    background-color: #6c757d;
}
.class-card.lecture {
    background-color: #f0f7ff;
    border-top: 4px solid #4361ee;
}

.class-card.lecture .class-subject {
    background-color: #4361ee;
}

.class-card.tp {
    background-color: #e6ffe6;
    border-top: 4px solid #2b9348;
}

.class-card.tp .class-subject {
    background-color: #2b9348;
}

.class-card.td {
    background-color: #fff7e6;
    border-top: 4px solid #ff9f1c;
}

.class-card.td .class-subject {
    background-color: #ff9f1c;
}

.class-card.exam {
    background-color: #ffe6e6;
    border-top: 4px solid #e63946;
}

.class-card.exam .class-subject {
    background-color: #e63946;
}

.class-card.workshop {
    background-color: #f0e6ff;
    border-top: 4px solid #7209b7;
}

.class-card.workshop .class-subject {
    background-color: #7209b7;
}

.class-card.seminar {
    background-color: #e6f9ff;
    border-top: 4px solid #118ab2;
}

.class-card.seminar .class-subject {
    background-color: #118ab2;
}

.class-title {
    font-weight: 700;
    margin-bottom: 10px;
    color: #212529;
    font-size: 1rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.class-info {
    font-size: 0.85rem;
    color: #495057;
    margin: 6px 0;
    line-height: 1.4;
    display: flex;
    align-items: center;
}

.class-info:before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #adb5bd;
    margin-right: 6px;
}

.class-subject {
    font-style: normal;
    color: #fff;
    font-size: 0.8rem;
    font-weight: 700;
    background-color: #6c757d;
    display: inline-block;
    padding: 4px 10px;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* For cards without the header structure, keep the absolute positioning */
.class-card:not(:has(.class-card-header)) .class-subject {
    position: absolute;
    bottom: 10px;
    right: 10px;
    margin-top: 10px;
}

/* Empty cell styling */
.empty-cell {
    background-color: #f8f9fa;
    min-height: 130px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.empty-cell:before {
    content: '+';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #dee2e6;
    font-size: 2rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.empty-cell:hover {
    background-color: #e9ecef;
}

.empty-cell:hover:before {
    opacity: 0.7;
}

/* Toast messages */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 9999;
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.3s, transform 0.3s;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.toast-message.show {
    opacity: 1;
    transform: translateY(0);
}

.success-toast {
    background-color: #52c41a;
}

.error-toast {
    background-color: #f5222d;
}

/* PDF Loading Overlay */
.pdf-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.pdf-loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4361ee;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.pdf-loading-text {
    color: white;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    max-width: 80%;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Filter icon styling */
.filter-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.filter-icon i {
    color: #1890ff;
}

/* Applied filters styling */
.applied-filters {
    padding: 10px 0;
}

.filter-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.filter-badge {
    background-color: #f0f7ff;
    border-radius: 6px;
    padding: 8px 14px;
    font-size: 0.95rem;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
    border-left: 3px solid #1890ff;
    transition: all 0.2s ease;
}

.filter-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.15);
}

.filter-label {
    font-weight: 600;
    color: #333;
    margin-right: 6px;
}

.filter-value {
    color: #1890ff;
    font-weight: 500;
}

/* Class title styling */
#class-title {
    display: none !important; /* Hide the class title completely */
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #0050b3;
    font-weight: 500;
    font-size: 1.1rem;
    text-align: center;
    border-radius: 8px;
    box-shadow: 0 3px 6px rgba(24, 144, 255, 0.12);
    padding: 14px 20px;
    border-left: 4px solid #1890ff;
    transition: all 0.2s ease;
}

#class-title:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(24, 144, 255, 0.18);
}

/* Animation */
.animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modal styling */
.modal-content {
    border-radius: 8px;
    border: none;
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eaeaea;
}

.modal-title {
    font-weight: 600;
    color: #333;
}

.form-label {
    font-weight: 500;
    color: #495057;
}

.form-select:focus, .form-control:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 0.25rem rgba(24, 144, 255, 0.25);
}

/* PDF Generation Styles */
.generating-pdf {
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    border: none !important;
}

.generating-pdf .timetable {
    border-collapse: collapse !important;
    width: 100% !important;
}

.generating-pdf .timetable th,
.generating-pdf .timetable td {
    border: 1px solid #dee2e6 !important;
}

.generating-pdf .class-card {
    box-shadow: none !important;
    border-width: 3px !important;
    min-height: 120px !important;
    padding: 10px !important;
}

.generating-pdf .class-subject {
    position: static !important;
    margin-top: 8px !important;
    display: inline-block !important;
    box-shadow: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .timetable {
        font-size: 0.85rem;
    }

    .class-card {
        padding: 8px;
        min-height: 110px;
    }

    .class-title {
        font-size: 0.9rem;
        margin-bottom: 6px;
    }

    .class-info {
        font-size: 0.75rem;
        margin: 4px 0;
    }

    .class-subject {
        font-size: 0.7rem;
        padding: 3px 8px;
        bottom: 8px;
        right: 8px;
    }

    .filter-badges {
        flex-direction: column;
        gap: 5px;
    }
}