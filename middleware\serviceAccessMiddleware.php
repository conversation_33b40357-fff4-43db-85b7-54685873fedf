<?php
/**
 * Service Access Middleware - Version Améliorée
 * Vérifie l'accès aux services avec interface utilisateur cohérente
 */

require_once __DIR__ . '/../model/serviceManagementModel.php';
require_once __DIR__ . '/../config/constants.php';
require_once __DIR__ . '/../utils/serviceStatusHelper.php';

/**
 * Middleware générique pour vérifier l'accès à un service avec inclusion de contenu
 */
function requireServiceAccess($serviceKey, $serviceName, $allowedRoles = null) {
    // Vérifier l'authentification
    if (!isset($_SESSION['user_id'])) {
        header("Location: " . BASE_URL . "/view/auth/login.php");
        exit();
    }

    // Vérifier les permissions de rôle si spécifiées
    if ($allowedRoles && isset($_SESSION['user_role'])) {
        $userRole = $_SESSION['user_role'];
        if (!in_array($userRole, $allowedRoles)) {
            includeServiceStatusContent($serviceKey, $serviceName, 'access_denied', [
                'required_roles' => $allowedRoles,
                'user_role' => $userRole
            ]);
        }
    }

    // Vérifier si le service est actif
    $isActive = isServiceActive($serviceKey);

    if (!$isActive) {
        $service = getServiceByKey($serviceKey);
        $reason = 'not_activated';
        $additionalInfo = [];

        if ($service) {
            if ($service['last_deactivated_at']) {
                $reason = 'period_closed';
                $additionalInfo['last_deactivated'] = $service['last_deactivated_at'];
            }
            $additionalInfo['activation_count'] = $service['activation_count'] ?? 0;
        }

        // Inclure le contenu de la page d'état dans la page actuelle
        includeServiceStatusContent($serviceKey, $serviceName, $reason, $additionalInfo);
    }

    return true;
}

/**
 * Vérifie l'accès au service des préférences UE avec interface cohérente
 */
function requireUEPreferencesAccess($redirectUrl = null) {
    return requireServiceAccess('ue_preferences', 'Collecte des Préférences UE', ['enseignant', 'chef de departement']);
}

/**
 * Check UE preferences service access without redirecting
 */
function checkUEPreferencesServiceAccess() {
    $isActive = isServiceActive('ue_preferences');

    if (!$isActive) {
        $service = getServiceByKey('ue_preferences');
        $message = "La période de soumission des préférences UE est actuellement fermée.";

        if ($service && $service['last_deactivated_at']) {
            $lastDeactivated = new DateTime($service['last_deactivated_at']);
            $message .= " Dernière fermeture: " . $lastDeactivated->format('d/m/Y à H:i');
        }

        $message .= " Veuillez contacter votre chef de département pour plus d'informations.";

        return [
            'allowed' => false,
            'message' => $message,
            'service' => $service
        ];
    }

    $service = getServiceByKey('ue_preferences');
    $message = "La période de soumission des préférences UE est active.";

    if ($service && $service['end_time']) {
        $endTime = new DateTime($service['end_time']);
        $now = new DateTime();

        if ($endTime > $now) {
            $interval = $now->diff($endTime);

            if ($interval->days > 0) {
                $message .= " Temps restant: " . $interval->days . " jour(s) " . $interval->h . " heure(s)";
            } elseif ($interval->h > 0) {
                $message .= " Temps restant: " . $interval->h . " heure(s) " . $interval->i . " minute(s)";
            } else {
                $message .= " Temps restant: " . $interval->i . " minute(s)";
            }

            $message .= " (jusqu'au " . $endTime->format('d/m/Y à H:i') . ")";
        }
    }

    return [
        'allowed' => true,
        'message' => $message,
        'service' => $service
    ];
}

/**
 * Vérifie l'accès au service de soumission des notes
 */
function requireGradeSubmissionAccess($redirectUrl = null) {
    return requireServiceAccess('grade_submission', 'Saisie des Notes', ['enseignant', 'coordinateur']);
}

/**
 * Vérifie l'accès au service d'évaluation des cours
 */
function requireCourseEvaluationAccess($redirectUrl = null) {
    return requireServiceAccess('course_evaluation', 'Évaluation des Cours', ['etudiant', 'enseignant']);
}

/**
 * Vérifie l'accès au service de modification des emplois du temps
 */
function requireScheduleModificationAccess($redirectUrl = null) {
    return requireServiceAccess('schedule_modification', 'Modification des Emplois du Temps', ['chef de departement', 'coordinateur']);
}

/**
 * Display service access message if exists in session
 */
function displayServiceAccessMessage() {
    session_start();

    if (isset($_SESSION['service_access_message'])) {
        $message = $_SESSION['service_access_message'];
        $type = isset($_SESSION['service_access_type']) ? $_SESSION['service_access_type'] : 'info';

        echo "<div class='alert alert-{$type} alert-dismissible fade show' role='alert'>";
        echo "<i class='bi bi-info-circle'></i> " . htmlspecialchars($message);
        echo "<button type='button' class='btn-close' data-bs-dismiss='alert'></button>";
        echo "</div>";

        // Clear the message after displaying
        unset($_SESSION['service_access_message']);
        unset($_SESSION['service_access_type']);
    }
}

/**
 * Get service status for display in UI
 */
function getServiceStatusForUI($serviceKey) {
    $service = getServiceByKey($serviceKey);

    if (!$service) {
        return [
            'exists' => false,
            'active' => false,
            'message' => 'Service non configuré'
        ];
    }

    $isActive = isServiceActive($serviceKey);

    $status = [
        'exists' => true,
        'active' => $isActive,
        'service' => $service
    ];

    if ($isActive && $service['end_time']) {
        $endTime = new DateTime($service['end_time']);
        $now = new DateTime();

        if ($endTime > $now) {
            $interval = $now->diff($endTime);
            $status['remaining_time'] = [
                'days' => $interval->days,
                'hours' => $interval->h,
                'minutes' => $interval->i,
                'formatted' => formatTimeRemaining($interval)
            ];
            $status['end_time'] = $endTime->format('d/m/Y à H:i');
        }
    }

    return $status;
}

/**
 * Format time remaining for display
 */
function formatTimeRemaining($interval) {
    if ($interval->days > 0) {
        return $interval->days . " jour(s) " . $interval->h . " heure(s)";
    } elseif ($interval->h > 0) {
        return $interval->h . " heure(s) " . $interval->i . " minute(s)";
    } else {
        return $interval->i . " minute(s)";
    }
}

/**
 * Create a service status widget for UI
 */
function renderServiceStatusWidget($serviceKey, $serviceName) {
    $status = getServiceStatusForUI($serviceKey);

    if (!$status['exists']) {
        return "<div class='alert alert-secondary'><i class='bi bi-gear'></i> Service non configuré</div>";
    }

    if ($status['active']) {
        $html = "<div class='alert alert-success'>";
        $html .= "<i class='bi bi-check-circle'></i> <strong>$serviceName</strong> est actif";

        if (isset($status['remaining_time'])) {
            $html .= "<br><small><i class='bi bi-clock'></i> Temps restant: " . $status['remaining_time']['formatted'];
            $html .= " (jusqu'au " . $status['end_time'] . ")</small>";
        }

        $html .= "</div>";
        return $html;
    } else {
        return "<div class='alert alert-warning'><i class='bi bi-x-circle'></i> <strong>$serviceName</strong> est inactif</div>";
    }
}

?>
