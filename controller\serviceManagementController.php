<?php
/**
 * Service Management Controller
 * Handles API requests for service management
 */

require_once __DIR__ . '/../model/serviceManagementModel.php';
require_once __DIR__ . '/../utils/response.php';

/**
 * Get all services API
 */
function getAllServicesAPI() {
    try {
        $services = getAllServices();

        if (isset($services['error'])) {
            jsonResponse(['success' => false, 'error' => $services['error']], 500);
            return;
        }

        jsonResponse(['success' => true, 'services' => $services]);
    } catch (Exception $e) {
        error_log('Error in getAllServicesAPI: ' . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Error fetching services'], 500);
    }
}

/**
 * Get service by key API
 */
function getServiceByKeyAPI($serviceKey) {
    try {
        if (empty($serviceKey)) {
            jsonResponse(['success' => false, 'error' => 'Service key is required'], 400);
            return;
        }

        $service = getServiceByKey($serviceKey);

        if (!$service) {
            jsonResponse(['success' => false, 'error' => 'Service not found'], 404);
            return;
        }

        jsonResponse(['success' => true, 'service' => $service]);
    } catch (Exception $e) {
        error_log('Error in getServiceByKeyAPI: ' . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Error fetching service'], 500);
    }
}

/**
 * Check service status API
 */
function checkServiceStatusAPI($serviceKey) {
    try {
        if (empty($serviceKey)) {
            jsonResponse(['success' => false, 'error' => 'Service key is required'], 400);
            return;
        }

        $isActive = isServiceActive($serviceKey);
        $service = getServiceByKey($serviceKey);

        $response = [
            'success' => true,
            'service_key' => $serviceKey,
            'is_active' => $isActive,
            'service' => $service
        ];

        if ($service && $service['is_active'] && $service['end_time']) {
            $now = new DateTime();
            $endTime = new DateTime($service['end_time']);
            if ($endTime > $now) {
                $interval = $now->diff($endTime);
                $response['remaining_time'] = [
                    'days' => $interval->days,
                    'hours' => $interval->h,
                    'minutes' => $interval->i,
                    'total_minutes' => ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i
                ];
            }
        }

        jsonResponse($response);
    } catch (Exception $e) {
        error_log('Error in checkServiceStatusAPI: ' . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Error checking service status'], 500);
    }
}

/**
 * Activate service API
 */
function activateServiceAPI() {
    try {
        // Log the request for debugging
        error_log("activateServiceAPI called");
        error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
        error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'not set'));

        $rawInput = file_get_contents('php://input');
        error_log("Raw input: " . $rawInput);

        $data = json_decode($rawInput, true);
        error_log("Decoded data: " . json_encode($data));
        error_log("JSON decode error: " . json_last_error_msg());

        if (!$data) {
            error_log("Invalid JSON data received");
            jsonResponse(['success' => false, 'error' => 'Invalid JSON data: ' . json_last_error_msg()], 400);
            return;
        }

        if (empty($data['service_key'])) {
            error_log("Service key is missing from request");
            jsonResponse(['success' => false, 'error' => 'Service key is required'], 400);
            return;
        }

        $serviceKey = $data['service_key'];
        $durationHours = isset($data['duration_hours']) ? (int)$data['duration_hours'] : null;
        $endTime = isset($data['end_time']) ? $data['end_time'] : null;
        $activatedBy = isset($data['activated_by']) ? $data['activated_by'] : null;

        // Validate duration or end time
        if (!$durationHours && !$endTime) {
            jsonResponse(['success' => false, 'error' => 'Duration hours or end time is required'], 400);
            return;
        }

        // Validate end time format if provided
        if ($endTime) {
            // Try different formats: datetime-local format (Y-m-d\TH:i) and standard format (Y-m-d H:i:s)
            $endTimeObj = DateTime::createFromFormat('Y-m-d\TH:i', $endTime);
            if (!$endTimeObj) {
                $endTimeObj = DateTime::createFromFormat('Y-m-d H:i:s', $endTime);
            }
            if (!$endTimeObj) {
                $endTimeObj = DateTime::createFromFormat('Y-m-d H:i', $endTime);
            }

            if (!$endTimeObj) {
                error_log("Invalid end time format received: " . $endTime);
                jsonResponse(['success' => false, 'error' => 'Invalid end time format. Use Y-m-d H:i:s or Y-m-d\TH:i'], 400);
                return;
            }

            // Convert to standard format for database storage
            $endTime = $endTimeObj->format('Y-m-d H:i:s');

            // Check if end time is in the future
            $now = new DateTime();
            if ($endTimeObj <= $now) {
                jsonResponse(['success' => false, 'error' => 'End time must be in the future'], 400);
                return;
            }
        }

        // Validate duration hours
        if ($durationHours && ($durationHours < 1 || $durationHours > 8760)) { // Max 1 year
            jsonResponse(['success' => false, 'error' => 'Duration hours must be between 1 and 8760'], 400);
            return;
        }

        $result = activateService($serviceKey, $durationHours, $endTime, $activatedBy);

        if (isset($result['error'])) {
            jsonResponse(['success' => false, 'error' => $result['error']], 500);
            return;
        }

        jsonResponse(['success' => true, 'message' => $result['message']]);
    } catch (Exception $e) {
        error_log('Error in activateServiceAPI: ' . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Error activating service'], 500);
    }
}

/**
 * Extend service API
 */
function extendServiceAPI() {
    try {
        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data) {
            jsonResponse(['success' => false, 'error' => 'Invalid JSON data'], 400);
            return;
        }

        if (empty($data['service_key'])) {
            jsonResponse(['success' => false, 'error' => 'Service key is required'], 400);
            return;
        }

        $serviceKey = $data['service_key'];
        $additionalHours = isset($data['additional_hours']) ? (int)$data['additional_hours'] : null;
        $newEndTime = isset($data['new_end_time']) ? $data['new_end_time'] : null;
        $extendedBy = isset($data['extended_by']) ? $data['extended_by'] : null;

        // Validate extension parameters
        if (!$additionalHours && !$newEndTime) {
            jsonResponse(['success' => false, 'error' => 'Additional hours or new end time is required'], 400);
            return;
        }

        // Validate new end time format if provided
        if ($newEndTime) {
            // Try different formats: datetime-local format (Y-m-d\TH:i) and standard format (Y-m-d H:i:s)
            $newEndTimeObj = DateTime::createFromFormat('Y-m-d\TH:i', $newEndTime);
            if (!$newEndTimeObj) {
                $newEndTimeObj = DateTime::createFromFormat('Y-m-d H:i:s', $newEndTime);
            }
            if (!$newEndTimeObj) {
                $newEndTimeObj = DateTime::createFromFormat('Y-m-d H:i', $newEndTime);
            }

            if (!$newEndTimeObj) {
                error_log("Invalid new end time format received: " . $newEndTime);
                jsonResponse(['success' => false, 'error' => 'Invalid new end time format. Use Y-m-d H:i:s or Y-m-d\TH:i'], 400);
                return;
            }

            // Convert to standard format for database storage
            $newEndTime = $newEndTimeObj->format('Y-m-d H:i:s');

            // Check if new end time is in the future
            $now = new DateTime();
            if ($newEndTimeObj <= $now) {
                jsonResponse(['success' => false, 'error' => 'New end time must be in the future'], 400);
                return;
            }
        }

        // Validate additional hours
        if ($additionalHours && ($additionalHours < 1 || $additionalHours > 8760)) { // Max 1 year
            jsonResponse(['success' => false, 'error' => 'Additional hours must be between 1 and 8760'], 400);
            return;
        }

        $result = extendService($serviceKey, $additionalHours, $newEndTime, $extendedBy);

        if (isset($result['error'])) {
            jsonResponse(['success' => false, 'error' => $result['error']], 500);
            return;
        }

        jsonResponse(['success' => true, 'message' => $result['message'], 'new_end_time' => $result['new_end_time']]);
    } catch (Exception $e) {
        error_log('Error in extendServiceAPI: ' . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Error extending service'], 500);
    }
}

/**
 * Deactivate service API
 */
function deactivateServiceAPI() {
    try {
        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data) {
            jsonResponse(['success' => false, 'error' => 'Invalid JSON data'], 400);
            return;
        }

        if (empty($data['service_key'])) {
            jsonResponse(['success' => false, 'error' => 'Service key is required'], 400);
            return;
        }

        $serviceKey = $data['service_key'];
        $reason = isset($data['reason']) ? $data['reason'] : 'manual';
        $deactivatedBy = isset($data['deactivated_by']) ? $data['deactivated_by'] : null;

        $result = deactivateService($serviceKey, $reason, $deactivatedBy);

        if (isset($result['error'])) {
            jsonResponse(['success' => false, 'error' => $result['error']], 500);
            return;
        }

        jsonResponse(['success' => true, 'message' => $result['message']]);
    } catch (Exception $e) {
        error_log('Error in deactivateServiceAPI: ' . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Error deactivating service'], 500);
    }
}

/**
 * Check expired services API
 */
function checkExpiredServicesAPI() {
    try {
        $expiredServices = checkExpiredServices();

        if ($expiredServices === false) {
            jsonResponse(['success' => false, 'error' => 'Error checking expired services'], 500);
            return;
        }

        jsonResponse([
            'success' => true,
            'expired_services' => $expiredServices,
            'count' => count($expiredServices)
        ]);
    } catch (Exception $e) {
        error_log('Error in checkExpiredServicesAPI: ' . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Error checking expired services'], 500);
    }
}

/**
 * Get service logs API
 */
function getServiceLogsAPI($serviceKey = null) {
    try {
        $conn = getConnection();

        if (!$conn) {
            jsonResponse(['success' => false, 'error' => 'Database connection error'], 500);
            return;
        }

        $whereClause = '';
        if ($serviceKey) {
            $serviceKey = mysqli_real_escape_string($conn, $serviceKey);
            $whereClause = "WHERE service_key = '$serviceKey'";
        }

        $query = "SELECT sl.*, sm.display_name
                 FROM service_logs sl
                 LEFT JOIN service_management sm ON sl.service_key = sm.service_key
                 $whereClause
                 ORDER BY sl.created_at DESC
                 LIMIT 100";

        $result = mysqli_query($conn, $query);

        if (!$result) {
            error_log("Error getting service logs: " . mysqli_error($conn));
            mysqli_close($conn);
            jsonResponse(['success' => false, 'error' => 'Error fetching service logs'], 500);
            return;
        }

        $logs = [];
        while ($row = mysqli_fetch_assoc($result)) {
            if ($row['details']) {
                $row['details'] = json_decode($row['details'], true);
            }
            $logs[] = $row;
        }

        mysqli_close($conn);
        jsonResponse(['success' => true, 'logs' => $logs]);
    } catch (Exception $e) {
        error_log('Error in getServiceLogsAPI: ' . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Error fetching service logs'], 500);
    }
}

/**
 * Middleware to check if UE preferences service is active
 */
function checkUEPreferencesAccess() {
    $isActive = isServiceActive('ue_preferences');

    if (!$isActive) {
        $service = getServiceByKey('ue_preferences');
        $message = "La période de soumission des préférences UE est actuellement fermée.";

        if ($service && $service['last_deactivated_at']) {
            $message .= " Dernière fermeture: " . date('d/m/Y H:i', strtotime($service['last_deactivated_at']));
        }

        $message .= " Veuillez contacter votre chef de département pour plus d'informations.";

        return [
            'allowed' => false,
            'message' => $message,
            'service' => $service
        ];
    }

    return [
        'allowed' => true,
        'service' => getServiceByKey('ue_preferences')
    ];
}

/**
 * Get service access status for user interface
 */
function getServiceAccessStatusAPI($serviceKey) {
    try {
        if (empty($serviceKey)) {
            jsonResponse(['success' => false, 'error' => 'Service key is required'], 400);
            return;
        }

        switch ($serviceKey) {
            case 'ue_preferences':
                $accessStatus = checkUEPreferencesAccess();
                break;
            default:
                $isActive = isServiceActive($serviceKey);
                $service = getServiceByKey($serviceKey);
                $accessStatus = [
                    'allowed' => $isActive,
                    'message' => $isActive ? 'Service disponible' : 'Service non disponible',
                    'service' => $service
                ];
                break;
        }

        jsonResponse(['success' => true, 'access_status' => $accessStatus]);
    } catch (Exception $e) {
        error_log('Error in getServiceAccessStatusAPI: ' . $e->getMessage());
        jsonResponse(['success' => false, 'error' => 'Error checking service access'], 500);
    }
}

?>
