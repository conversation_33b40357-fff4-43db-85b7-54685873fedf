<?php
/**
 * Route commune pour les fonctions partagées
 *
 * Ce fichier gère les routes pour les requêtes liées aux fonctionnalités communes
 * comme la récupération des semestres, niveaux, filières, etc.
 */

// Inclure le contrôleur commun
require_once __DIR__ . '/../controller/commonController.php';

// Désactiver l'affichage des erreurs PHP
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Définir un gestionnaire d'erreurs personnalisé
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("Erreur PHP: [$errno] $errstr dans $errfile à la ligne $errline");
    jsonResponse(['error' => 'Une erreur interne est survenue. Veuillez contacter l\'administrateur.'], 500);
    exit;
});

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    // Traiter la requête en fonction de l'action demandée
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Récupérer l'action demandée
        $action = isset($_GET['action']) ? $_GET['action'] : '';

        switch ($action) {
            case 'getSemesters':
                // Récupérer l'ID du niveau (optionnel)
                $niveauId = isset($_GET['niveau_id']) ? $_GET['niveau_id'] : null;

                // Appeler l'API pour récupérer les semestres
                getSemestersAPI($niveauId);
                break;

            case 'getLevels':
                // Récupérer l'ID du cycle ou de la filière (optionnel)
                $cycleId = isset($_GET['cycle_id']) ? $_GET['cycle_id'] : null;
                $filiereId = isset($_GET['filiere_id']) ? $_GET['filiere_id'] : null;

                // Journaliser les paramètres reçus
                error_log("Route getLevels - Paramètres reçus: cycle_id=$cycleId, filiere_id=$filiereId");

                // Appeler l'API pour récupérer les niveaux
                // La fonction getLevels dans le modèle s'occupera de récupérer le cycle_id si nécessaire
                getLevelsAPI($cycleId, $filiereId);
                break;

            case 'getFilieres':
                // Récupérer l'ID du département (optionnel)
                $departementId = isset($_GET['departement_id']) ? $_GET['departement_id'] : null;

                // Appeler l'API pour récupérer les filières
                getFilieresAPI($departementId);
                break;

            case 'getDepartements':
                // Appeler l'API pour récupérer les départements
                getDepartementsAPI();
                break;

            case 'getCycles':
                // Appeler l'API pour récupérer les cycles
                getCyclesAPI();
                break;

            case 'getUETypes':
                // Appeler l'API pour récupérer les types d'UE
                getUETypesAPI();
                break;

            case 'getAcademicYears':
                // Récupérer le nombre d'années à générer
                $count = isset($_GET['count']) ? intval($_GET['count']) : 6;

                // Appeler l'API pour récupérer les années académiques
                getAcademicYearsAPI($count);
                break;

            default:
                // Action non reconnue
                jsonResponse(['error' => 'Action non reconnue'], 400);
                break;
        }
    } else {
        // Méthode HTTP non autorisée
        jsonResponse(['error' => 'Méthode non autorisée'], 405);
    }
} catch (Exception $e) {
    // Journaliser l'erreur
    error_log("Exception dans commonRoute.php: " . $e->getMessage());

    // Renvoyer une réponse JSON avec l'erreur
    jsonResponse(['error' => 'Une erreur est survenue lors du traitement de la requête: ' . $e->getMessage()], 500);
}
