/**
 * JavaScript for the upload_grades page
 *
 * This script handles loading and displaying students and their grades
 * based on the selected filters, as well as saving updated grades.
 */

// Global variables
// currentTeacherId is declared in the HTML file
// If it's not defined yet, we'll initialize it in getCurrentTeacherId()
let studentsData = [];

// Make sure currentTeacherId exists
if (typeof currentTeacherId === 'undefined') {
    var currentTeacherId = null;
}

// Document ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Upload Grades page loaded');

    // Get the current teacher ID from the session
    getCurrentTeacherId();

    // Initialize filters
    initializeFilters();

    // Set up event listeners
    setupEventListeners();
});

/**
 * Get the current teacher ID from the session
 */
async function getCurrentTeacherId() {
    try {
        // Check if currentTeacherId is already defined and has a value (from PHP)
        if (currentTeacherId) {
            console.log('Using teacher ID from PHP:', currentTeacherId);

            // Load all modules for this teacher directly
            loadAllTeacherModules();
            return;
        }

        // If not defined or null, try to get it from the session
        const response = await fetch('../../route/profileRoute.php?action=getCurrentUser');
        const data = await response.json();

        if (data.success && data.user) {
            // Check if teacher_id is available in the session
            if (data.user.teacher_id) {
                currentTeacherId = data.user.teacher_id;
                console.log('Current teacher ID from session:', currentTeacherId);
            }
            // If not, check if id is available
            else if (data.user.id) {
                currentTeacherId = data.user.id;
                console.log('Current teacher ID from API:', currentTeacherId);
            }

            // If we have a teacher ID, load all modules
            if (currentTeacherId) {
                loadAllTeacherModules();
            } else {
                console.error('No teacher ID found in user data:', data.user);
                showError('Failed to get teacher information. Please refresh the page.');
            }
        } else {
            console.error('Failed to get current user:', data);
            showError('Failed to get teacher information. Please refresh the page.');
        }
    } catch (error) {
        console.error('Error getting current teacher ID:', error);
        showError('An error occurred while loading teacher information. Please refresh the page.');
    }
}

/**
 * Initialize filters
 */
function initializeFilters() {
    // Session is static, already in the HTML

    // Module will be loaded directly from the server
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // Add null checks for all elements before attaching event listeners

    // Module change event - to update module header
    const moduleElement = document.getElementById('module');
    if (moduleElement) {
        moduleElement.addEventListener('change', function() {
            const moduleId = this.value;

            // If we have module data and this module was selected directly
            if (moduleId && window.moduleData && window.moduleData[moduleId]) {
                // Update module header with selected module data
                updateModuleHeader(moduleId);
            } else {
                // Reset module header if no module is selected
                const moduleNameDisplay = document.getElementById('module-name-display');
                const filiereDisplay = document.getElementById('filiere-display');
                const semestreDisplay = document.getElementById('semestre-display');

                if (moduleNameDisplay) moduleNameDisplay.textContent = 'Sélectionnez un module';
                if (filiereDisplay) filiereDisplay.textContent = '-';
                if (semestreDisplay) semestreDisplay.textContent = '-';

                // Remove highlight classes
                if (moduleNameDisplay) moduleNameDisplay.classList.remove('highlight');
                if (filiereDisplay) filiereDisplay.classList.remove('highlight');
                if (semestreDisplay) semestreDisplay.classList.remove('highlight');
            }
        });
    }

    // Session change event - to update session in module header
    const sessionElement = document.getElementById('session');
    if (sessionElement) {
        sessionElement.addEventListener('change', function() {
            const moduleId = document.getElementById('module')?.value;

            // If a module is selected, update the header
            if (moduleId && window.moduleData && window.moduleData[moduleId]) {
                updateModuleHeader(moduleId);
            } else {
                // Just update the session display
                const sessionDisplay = document.getElementById('session-display');
                if (sessionDisplay) {
                    const sessionValue = this.value;
                    const sessionText = this.options[this.selectedIndex]?.text || '-';

                    sessionDisplay.textContent = sessionValue ? sessionText : '-';

                    if (sessionValue) {
                        sessionDisplay.classList.add('highlight');
                    } else {
                        sessionDisplay.classList.remove('highlight');
                    }
                }
            }
        });
    }

    // Display button click event
    const displayButton = document.getElementById('display-button');
    if (displayButton) {
        displayButton.addEventListener('click', function(event) {
            displayStudents(event);
        });
    }

    // Save grades button click event
    const saveGradesButton = document.getElementById('save-grades-button');
    if (saveGradesButton) {
        saveGradesButton.addEventListener('click', function() {
            saveGrades();
        });
    }
}

/**
 * Load fields (filieres) for the current teacher
 */
async function loadTeacherFields() {
    try {
        if (!currentTeacherId) {
            console.error('No teacher ID available');
            return;
        }

        // Use the new endpoint that gets fields from the affectation table
        const response = await fetch(`../../route/enseignantRoute.php?action=getTeacherFieldsFromAffectation&id=${currentTeacherId}`);
        const data = await response.json();

        if (data.success && data.data) {
            const filiereSelect = document.getElementById('filiere');
            filiereSelect.innerHTML = '<option value="" disabled selected>Select field</option>';

            data.data.forEach(filiere => {
                filiereSelect.innerHTML += `<option value="${filiere.id_filiere || filiere.filiere_id}">${filiere.nom_filiere}</option>`;
            });

            console.log('Fields loaded:', data.data.length);

            // If there's only one field, select it automatically
            if (data.data.length === 1) {
                const filiereId = data.data[0].id_filiere || data.data[0].filiere_id;
                filiereSelect.value = filiereId;

                // Trigger the change event to load levels
                const event = new Event('change');
                filiereSelect.dispatchEvent(event);

                console.log('Auto-selected the only available field:', filiereId);
            }
        } else {
            console.error('Failed to load fields:', data);

            // Try fallback to the old endpoint
            console.log('Trying fallback to old endpoint for fields');
            const fallbackResponse = await fetch(`../../route/enseignantRoute.php?action=getTeacherFields&id=${currentTeacherId}`);
            const fallbackData = await fallbackResponse.json();

            if (fallbackData.success && fallbackData.data) {
                const filiereSelect = document.getElementById('filiere');
                filiereSelect.innerHTML = '<option value="" disabled selected>Select field</option>';

                fallbackData.data.forEach(filiere => {
                    filiereSelect.innerHTML += `<option value="${filiere.id_filiere}">${filiere.nom_filiere}</option>`;
                });

                console.log('Fields loaded from fallback:', fallbackData.data.length);

                // If there's only one field, select it automatically
                if (fallbackData.data.length === 1) {
                    filiereSelect.value = fallbackData.data[0].id_filiere;

                    // Trigger the change event to load levels
                    const event = new Event('change');
                    filiereSelect.dispatchEvent(event);

                    console.log('Auto-selected the only available field:', fallbackData.data[0].id_filiere);
                }
            } else {
                showError('Failed to load fields. Please refresh the page.');
            }
        }
    } catch (error) {
        console.error('Error loading fields:', error);
        showError('An error occurred while loading fields. Please refresh the page.');
    }
}

/**
 * Load levels based on the selected field
 *
 * @param {string} filiereId The selected field ID
 */
async function loadLevels(filiereId) {
    try {
        if (!filiereId) {
            console.error('No field ID provided');
            return;
        }

        const response = await fetch(`../../route/niveauRoute.php?id_filiere=${filiereId}`);
        const data = await response.json();

        if (data.data) {
            const niveauSelect = document.getElementById('niveau');
            niveauSelect.innerHTML = '<option value="" disabled selected>Select level</option>';

            data.data.forEach(niveau => {
                niveauSelect.innerHTML += `<option value="${niveau.id_niveau}">${niveau.niveau}</option>`;
            });

            console.log('Levels loaded:', data.data.length);
        } else {
            console.error('Failed to load levels:', data);
            showError('Failed to load levels. Please try again.');
        }
    } catch (error) {
        console.error('Error loading levels:', error);
        showError('An error occurred while loading levels. Please try again.');
    }
}

/**
 * Load semesters based on the selected field and level
 *
 * @param {string} filiereId The selected field ID
 * @param {string} niveauId The selected level ID
 */
async function loadSemestres(filiereId, niveauId) {
    try {
        if (!filiereId || !niveauId) {
            console.error('Missing required parameters for loading semesters');
            return;
        }

        console.log(`Loading semesters for field ID: ${filiereId}, level ID: ${niveauId}`);

        const url = `../../route/semestreRoute.php?action=getSemestresByFiliereAndLevel&filiere_id=${filiereId}&level_id=${niveauId}`;
        console.log('Fetching semesters from URL:', url);

        const response = await fetch(url);
        const data = await response.json();

        if (data.data && Array.isArray(data.data)) {
            const semestreSelect = document.getElementById('semestre');
            semestreSelect.innerHTML = '<option value="" disabled selected>Select semester</option>';

            data.data.forEach(semestre => {
                // Utiliser l'ID du semestre comme valeur et stocker le nom comme attribut data
                const id = semestre.id;
                const name = semestre.nom;
                semestreSelect.innerHTML += `<option value="${id}" data-nom="${name}">${name}</option>`;
            });

            console.log('Semesters loaded:', data.data.length);

            // Add event listener for semester change - with null check
            const semestreElement = document.getElementById('semestre');
            if (semestreElement) {
                semestreElement.addEventListener('change', function() {
                    const semestre = this.value;

                    if (filiereId && niveauId && semestre) {
                        // Load modules when semester is selected
                        loadModules(filiereId, niveauId, semestre);
                    }
                });
            }
        } else {
            console.error('Failed to load semesters or no semesters found:', data);

            if (data.error) {
                showError(`Failed to load semesters: ${data.error}`);
            } else {
                showError('No semesters found for the selected criteria. Please try different filters.');
            }
        }
    } catch (error) {
        console.error('Error loading semesters:', error);
        showError('An error occurred while loading semesters. Please try again.');
    }
}

/**
 * Load modules based on the selected field, level, semester, and teacher
 *
 * @param {string} filiereId The selected field ID
 * @param {string} niveauId The selected level ID
 * @param {string} semestre The selected semester
 */
async function loadModules(filiereId, niveauId, semestre = null) {
    try {
        if (!filiereId || !niveauId || !currentTeacherId || !semestre) {
            console.error('Missing required parameters');
            return;
        }

        console.log(`Loading modules for teacher ID: ${currentTeacherId}, field ID: ${filiereId}, level ID: ${niveauId}, semester: ${semestre}`);

        // Use the improved endpoint that uses the affectation table
        let url = `../../controller/importGradesController.php?action=getModules&teacherId=${currentTeacherId}&levelId=${niveauId}&semester=${semestre}&filiere=${filiereId}`;

        console.log('Fetching modules from URL:', url);

        const response = await fetch(url);
        const responseText = await response.text();

        console.log('Raw response:', responseText);

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (e) {
            console.error('Failed to parse JSON response:', e);
            showError('Server returned invalid data. Please try again or contact support.');
            return;
        }

        const moduleSelect = document.getElementById('module');
        moduleSelect.innerHTML = '<option value="" disabled selected>Select module</option>';

        if (data.data && Array.isArray(data.data) && data.data.length > 0) {
            console.log('Modules data:', data.data);

            // Determine which fields to use for ID and name
            let idField = 'id';
            let nameField = 'nom';

            // Check if the first module has these fields
            const firstModule = data.data[0];

            if (!firstModule.hasOwnProperty(idField)) {
                // Try alternative field names
                if (firstModule.hasOwnProperty('id_module')) {
                    idField = 'id_module';
                } else {
                    // Log all available fields for debugging
                    console.log('Available fields in module:', Object.keys(firstModule));

                    // Use the first key as a fallback
                    const keys = Object.keys(firstModule);
                    if (keys.length > 0) {
                        idField = keys[0];
                    }
                }
            }

            if (!firstModule.hasOwnProperty(nameField)) {
                // Try alternative field names
                if (firstModule.hasOwnProperty('nom_module')) {
                    nameField = 'nom_module';
                } else if (firstModule.hasOwnProperty('name')) {
                    nameField = 'name';
                } else {
                    // Log all available fields for debugging
                    console.log('Available fields in module:', Object.keys(firstModule));

                    // Use the second key as a fallback for the name
                    const keys = Object.keys(firstModule);
                    if (keys.length > 1) {
                        nameField = keys[1];
                    } else if (keys.length > 0) {
                        nameField = keys[0];
                    }
                }
            }

            console.log(`Using ${idField} for module ID and ${nameField} for module name`);

            // Store module data for later use
            window.moduleData = {};

            data.data.forEach(module => {
                const id = module[idField] || '';
                const name = module[nameField] || 'Unknown Module';

                // Store the module data for auto-population
                window.moduleData[id] = module;

                moduleSelect.innerHTML += `<option value="${id}">${name}</option>`;
            });

            console.log('Modules loaded:', data.data.length);

            // Add event listener for module change to auto-populate related fields
            // moduleSelect is already defined and checked above, so we can use it directly
            if (moduleSelect) {
                moduleSelect.addEventListener('change', function() {
                    const selectedModuleId = this.value;
                    if (selectedModuleId && window.moduleData && window.moduleData[selectedModuleId]) {
                        autoPopulateRelatedFields(selectedModuleId);
                    }
                });
            }
        } else {
            console.error('Failed to load modules or no modules found:', data);

            if (data.error) {
                showError(`Failed to load modules: ${data.error}`);
            } else {
                showError('No modules found for the selected criteria. Please try different filters.');
            }
        }
    } catch (error) {
        console.error('Error loading modules:', error);
        showError('An error occurred while loading modules. Please try again.');
    }
}

/**
 * Load all modules for the current teacher
 */
async function loadAllTeacherModules() {
    try {
        if (!currentTeacherId) {
            console.error('No teacher ID available');
            showError('Teacher ID not found. Please refresh the page and try again.');
            return;
        }

        // Show loading status
        showStatusMessage('Loading your modules...', 'info', 0);

        console.log('Loading all modules for teacher ID:', currentTeacherId);

        // Use the new endpoint that gets all modules for the teacher
        const url = `../../controller/importGradesController.php?action=getAllTeacherModules&teacherId=${currentTeacherId}`;

        const response = await fetch(url);
        const responseText = await response.text();

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (e) {
            console.error('Failed to parse JSON response:', e);
            // Hide loading status
            document.getElementById('status-message-container').style.display = 'none';
            showError('Server returned invalid data. Please try again or contact support.');
            return;
        }

        // Fetch coordinators for each filiere
        const filiereIds = new Set();
        if (data.data && Array.isArray(data.data)) {
            data.data.forEach(module => {
                if (module.filiere_id) {
                    filiereIds.add(module.filiere_id);
                }
            });
        }

        // Create a map to store coordinators
        const coordinators = {};

        // Fetch coordinators for each filiere
        for (const filiereId of filiereIds) {
            try {
                const coordResponse = await fetch(`../../controller/filiereController.php?action=getCoordinateur&filiereId=${filiereId}`);
                const coordData = await coordResponse.json();

                if (coordData.success && coordData.coordinateur) {
                    coordinators[filiereId] = coordData.coordinateur;
                }
            } catch (error) {
                console.error(`Error fetching coordinator for filiere ${filiereId}:`, error);
            }
        }

        // Add coordinators to module data
        if (data.data && Array.isArray(data.data)) {
            data.data.forEach(module => {
                if (module.filiere_id && coordinators[module.filiere_id]) {
                    module.coordinateur = coordinators[module.filiere_id];
                }
            });
        }

        const moduleSelect = document.getElementById('module');
        if (!moduleSelect) {
            console.error('Module select element not found');
            // Hide loading status
            document.getElementById('status-message-container').style.display = 'none';
            return;
        }

        moduleSelect.innerHTML = '<option value="" disabled selected>Select a module</option>';

        if (data.data && Array.isArray(data.data) && data.data.length > 0) {
            // Determine which fields to use for ID and name
            let idField = 'id';
            let nameField = 'nom';

            // Check if the first module has these fields
            const firstModule = data.data[0];

            if (!firstModule.hasOwnProperty(idField)) {
                // Try alternative field names
                if (firstModule.hasOwnProperty('id_module')) {
                    idField = 'id_module';
                } else {
                    // Use the first key as a fallback
                    const keys = Object.keys(firstModule);
                    if (keys.length > 0) {
                        idField = keys[0];
                    }
                }
            }

            if (!firstModule.hasOwnProperty(nameField)) {
                // Try alternative field names
                if (firstModule.hasOwnProperty('nom_module')) {
                    nameField = 'nom_module';
                } else if (firstModule.hasOwnProperty('name')) {
                    nameField = 'name';
                } else {
                    // Use the second key as a fallback for the name
                    const keys = Object.keys(firstModule);
                    if (keys.length > 1) {
                        nameField = keys[1];
                    } else if (keys.length > 0) {
                        nameField = keys[0];
                    }
                }
            }

            // Store module data for later use
            window.moduleData = {};

            // Group modules by field and level for better organization
            const groupedModules = {};

            data.data.forEach(module => {
                const id = module[idField] || '';
                const name = module[nameField] || 'Unknown Module';
                const filiereName = module.filiere_nom || 'Unknown Field';
                const niveauName = module.niveau_nom || 'Unknown Level';
                const groupKey = `${filiereName} - ${niveauName}`;

                // Store the module data for later use
                window.moduleData[id] = module;

                // Group modules
                if (!groupedModules[groupKey]) {
                    groupedModules[groupKey] = [];
                }
                groupedModules[groupKey].push({ id, name, module });
            });

            // Create option groups for each field-level combination
            Object.keys(groupedModules).sort().forEach(groupKey => {
                const optgroup = document.createElement('optgroup');
                optgroup.label = groupKey;

                // Add modules to the option group
                groupedModules[groupKey].sort((a, b) => a.name.localeCompare(b.name)).forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = item.name;
                    optgroup.appendChild(option);
                });

                moduleSelect.appendChild(optgroup);
            });

            // Hide loading status and show success message
            document.getElementById('status-message-container').style.display = 'none';

            if (data.data.length === 1) {
                showInfo(`1 module loaded successfully.`);
            } else {
                showInfo(`${data.data.length} modules loaded successfully.`);
            }
        } else {
            // Hide loading status
            document.getElementById('status-message-container').style.display = 'none';

            if (data.error) {
                showError(`Failed to load modules: ${data.error}`);
            } else {
                showWarning('No modules found where you teach the lecture component. Please contact your administrator if you believe this is an error.');
            }
        }
    } catch (error) {
        console.error('Error loading modules:', error);
        // Hide loading status
        document.getElementById('status-message-container').style.display = 'none';
        showError('An error occurred while loading modules. Please try again.');
    }
}

/**
 * Update module header information
 *
 * @param {string} moduleId The selected module ID
 */
function updateModuleHeader(moduleId) {
    try {
        if (!moduleId || !window.moduleData || !window.moduleData[moduleId]) {
            console.log('Missing module data for updating header - this is normal for upload_grades.php');
            return;
        }

        const moduleData = window.moduleData[moduleId];
        console.log('Updating header with module data:', moduleData);

        // Get the module header elements (these may not exist in all pages)
        const moduleNameDisplay = document.getElementById('module-name-display');
        const filiereDisplay = document.getElementById('filiere-display');
        const semestreDisplay = document.getElementById('semestre-display');
        const sessionDisplay = document.getElementById('session-display');
        const coordinateurValue = document.querySelector('.personnel-value:nth-of-type(2)');

        // Get the session value
        const sessionSelect = document.getElementById('session');
        const sessionValue = sessionSelect ? sessionSelect.value : '';
        const sessionText = sessionSelect && sessionSelect.options[sessionSelect.selectedIndex] ?
                           sessionSelect.options[sessionSelect.selectedIndex].text : '-';

        // Check if we're on a page that has module header elements
        if (!moduleNameDisplay && !filiereDisplay && !semestreDisplay && !sessionDisplay) {
            console.log('Module header elements not found - this is normal for upload_grades.php');
            // Still update hidden fields if they exist
            updateHiddenFields(moduleData);
            return;
        }

        // Update the module header elements if they exist
        if (moduleNameDisplay) {
            moduleNameDisplay.textContent = moduleData.nom_module || moduleData.nom || 'Non spécifié';
            moduleNameDisplay.classList.add('highlight');
        }

        if (filiereDisplay) {
            filiereDisplay.textContent = moduleData.filiere_nom || 'Non spécifié';
            filiereDisplay.classList.add('highlight');
        }

        if (semestreDisplay) {
            semestreDisplay.textContent = moduleData.semestre_nom || 'Non spécifié';
            semestreDisplay.classList.add('highlight');
        }

        if (sessionDisplay) {
            sessionDisplay.textContent = sessionValue ? sessionText : '-';
            if (sessionValue) {
                sessionDisplay.classList.add('highlight');
            } else {
                sessionDisplay.classList.remove('highlight');
            }
        }

        // Update coordinateur if available
        if (coordinateurValue && moduleData.coordinateur) {
            coordinateurValue.textContent = moduleData.coordinateur;
        }

        // Update hidden fields
        updateHiddenFields(moduleData);

        // If we don't have a coordinateur yet, try to fetch it
        if (coordinateurValue && !moduleData.coordinateur && moduleData.filiere_id) {
            fetchCoordinateur(moduleData.filiere_id)
                .then(coordinateur => {
                    if (coordinateur) {
                        moduleData.coordinateur = coordinateur;
                        coordinateurValue.textContent = coordinateur;
                    }
                })
                .catch(error => {
                    console.error('Error fetching coordinateur:', error);
                });
        }
    } catch (error) {
        console.error('Error updating module header:', error);
    }
}

/**
 * Update hidden fields with module data
 *
 * @param {Object} moduleData The module data
 */
function updateHiddenFields(moduleData) {
    try {
        // Store values in hidden fields for form submission
        const filiereInput = document.getElementById('filiere');
        const niveauInput = document.getElementById('niveau');
        const semestreInput = document.getElementById('semestre');

        if (filiereInput && moduleData.filiere_id) {
            filiereInput.value = moduleData.filiere_id;
            console.log('Updated filiere hidden field:', moduleData.filiere_id);
        }

        if (niveauInput && moduleData.niveau_id) {
            niveauInput.value = moduleData.niveau_id;
            console.log('Updated niveau hidden field:', moduleData.niveau_id);
        }

        if (semestreInput && moduleData.semestre_id) {
            semestreInput.value = moduleData.semestre_id;
            console.log('Updated semestre hidden field:', moduleData.semestre_id);
        }
    } catch (error) {
        console.error('Error updating hidden fields:', error);
    }
}

/**
 * Fetch coordinateur for a filiere
 *
 * @param {string|number} filiereId The filiere ID
 * @returns {Promise<string>} The coordinateur name
 */
async function fetchCoordinateur(filiereId) {
    try {
        const response = await fetch(`../../controller/filiereController.php?action=getCoordinateur&filiereId=${filiereId}`);
        const data = await response.json();

        if (data.success && data.coordinateur) {
            return data.coordinateur;
        }

        return null;
    } catch (error) {
        console.error('Error fetching coordinateur:', error);
        return null;
    }
}

/**
 * Reset a filter dropdown
 *
 * @param {string} filterId The ID of the filter to reset
 */
function resetFilter(filterId) {
    const filter = document.getElementById(filterId);
    if (filter) {
        filter.innerHTML = `<option value="" disabled selected>Select ${filterId}</option>`;
    }
}

/**
 * Display students based on the selected filters
 */
async function displayStudents(event) {
    // Prevent default behavior if it's an event
    if (event) {
        event.preventDefault();
    }

    try {
        // Disable the button during processing
        const displayButton = document.getElementById('display-button');
        if (displayButton) {
            displayButton.disabled = true;
            displayButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
        }

        // Show loading status
        showStatusMessage('Preparing to display students...', 'info', 0);

        // Get filter values with null checks
        const sessionElement = document.getElementById('session');
        const moduleElement = document.getElementById('module');

        // If any of the required elements don't exist, show an error and return
        if (!sessionElement || !moduleElement) {
            // Hide loading status
            document.getElementById('status-message-container').style.display = 'none';
            showError('Required form elements not found. Please refresh the page and try again.');

            // Re-enable the button if it exists
            if (displayButton) {
                displayButton.disabled = false;
                displayButton.innerHTML = '<i class="bi bi-search me-2"></i>Display Students';
            }

            return;
        }

        const moduleId = moduleElement.value;
        const session = sessionElement.value;

        // Validate module selection
        if (!moduleId || !window.moduleData || !window.moduleData[moduleId]) {
            // Hide loading status
            document.getElementById('status-message-container').style.display = 'none';
            showWarning('Please select a module before displaying students.');

            // Re-enable the button
            if (displayButton) {
                displayButton.disabled = false;
                displayButton.innerHTML = '<i class="bi bi-search me-2"></i>Display Students';
            }

            return;
        }

        // Get the module data
        const moduleData = window.moduleData[moduleId];

        // Get the required values from the module data
        const filiereId = moduleData.filiere_id;
        const niveauId = moduleData.niveau_id;
        const semestre = moduleData.semestre_id;

        // Validate session
        if (!session) {
            // Hide loading status
            document.getElementById('status-message-container').style.display = 'none';
            showWarning('Please select a session before displaying students.');

            // Re-enable the button
            if (displayButton) {
                displayButton.disabled = false;
                displayButton.innerHTML = '<i class="bi bi-search me-2"></i>Display Students';
            }

            return;
        }

        // Get the text values for the filters
        const moduleName = moduleElement.options[moduleElement.selectedIndex].text;
        const filiereName = moduleData.filiere_nom || 'Unknown Field';
        const niveauName = moduleData.niveau_nom || 'Unknown Level';
        const semestreName = moduleData.semestre_nom || 'Unknown Semester';

        // Update status message
        showStatusMessage(`Loading students for ${moduleName}...`, 'info', 0);

        // Redirect to student_grades.php with all filter parameters
        // Use encodeURIComponent for all parameters
        const url = `student_grades.php?filiere=${encodeURIComponent(filiereId)}&niveau=${encodeURIComponent(niveauId)}&semestre=${encodeURIComponent(semestre)}&session=${encodeURIComponent(session)}&module=${encodeURIComponent(moduleId)}&filiere_name=${encodeURIComponent(filiereName)}&niveau_name=${encodeURIComponent(niveauName)}&module_name=${encodeURIComponent(moduleName)}&semestre_name=${encodeURIComponent(semestreName)}`;

        console.log('Redirecting to:', url);

        // Use window.location.href for standard redirection
        window.location.href = url;
    } catch (error) {
        console.error('Error displaying students:', error);

        // Hide loading status
        document.getElementById('status-message-container').style.display = 'none';
        showError('An error occurred while loading students. Please try again.');

        // Re-enable the button in case of error
        const displayButton = document.getElementById('display-button');
        if (displayButton) {
            displayButton.disabled = false;
            displayButton.innerHTML = '<i class="bi bi-search me-2"></i>Display Students';
        }
    }
}

/**
 * Display applied filters
 *
 * @param {Object} filters The applied filters
 */
function displayAppliedFilters(filters) {
    const appliedFiltersContainer = document.getElementById('applied-filters');
    appliedFiltersContainer.innerHTML = '';

    for (const [key, value] of Object.entries(filters)) {
        if (value) {
            const filterLabel = key.charAt(0).toUpperCase() + key.slice(1);
            appliedFiltersContainer.innerHTML += `
                <div class="filter-badge">
                    <span class="filter-badge-label">${filterLabel}:</span>
                    <span>${value}</span>
                </div>
            `;
        }
    }

    document.getElementById('applied-filters-card').style.display = 'block';
}

/**
 * Render the students table
 *
 * @param {Array} students The students data
 */
function renderStudentsTable(students) {
    const studentsListContainer = document.getElementById('students-list');
    studentsListContainer.innerHTML = '';

    if (students.length === 0) {
        document.getElementById('grades-table-card').style.display = 'none';
        document.getElementById('no-students-message').style.display = 'block';
        return;
    }

    students.forEach(student => {
        studentsListContainer.innerHTML += `
            <tr>
                <td>${student.CNE}</td>
                <td>${student.nom}</td>
                <td>${student.prenom}</td>
                <td>
                    <input type="number" class="grade-input"
                           data-student-id="${student.id_etudiant}"
                           value="${student.valeur || ''}"
                           min="0" max="20" step="0.25">
                </td>
            </tr>
        `;
    });

    document.getElementById('grades-table-card').style.display = 'block';
    document.getElementById('no-students-message').style.display = 'none';
}

/**
 * Save the updated grades
 */
async function saveGrades() {
    try {
        // Get all grade inputs
        const gradeInputs = document.querySelectorAll('.grade-input');

        // Get filter values
        const moduleId = document.getElementById('module').value;
        const niveauId = document.getElementById('niveau').value;
        const semestre = document.getElementById('semestre').value;
        const session = document.getElementById('session').value;

        // Prepare grades data
        const gradesData = [];

        gradeInputs.forEach(input => {
            const studentId = input.getAttribute('data-student-id');
            const value = input.value;

            if (studentId && value) {
                gradesData.push({
                    id_etudiant: studentId,
                    id_module: moduleId,
                    id_niveau: niveauId,
                    semestre: semestre,
                    session: session,
                    valeur: parseFloat(value)
                });
            }
        });

        if (gradesData.length === 0) {
            showError('No grades to save.');
            return;
        }

        // Send grades data to the server
        const response = await fetch('../../route/noteRoute.php?action=saveGrades', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ grades: gradesData })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('Grades saved successfully!');
        } else {
            console.error('Failed to save grades:', data);
            showError('Failed to save grades. Please try again.');
        }
    } catch (error) {
        console.error('Error saving grades:', error);
        showError('An error occurred while saving grades. Please try again.');
    }
}

/**
 * Show a toast notification
 *
 * @param {string} message The message to display
 * @param {string} type The type of notification (success, error, warning, info)
 * @param {number} duration The duration in milliseconds (default: 5000)
 */
function showToast(message, type = 'info', duration = 5000) {
    // Create a unique ID for the toast
    const toastId = 'toast-' + Date.now();

    // Determine the icon based on the type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="bi bi-check-circle-fill toast-icon success"></i>';
            break;
        case 'error':
            icon = '<i class="bi bi-exclamation-circle-fill toast-icon error"></i>';
            break;
        case 'warning':
            icon = '<i class="bi bi-exclamation-triangle-fill toast-icon warning"></i>';
            break;
        case 'info':
        default:
            icon = '<i class="bi bi-info-circle-fill toast-icon info"></i>';
            break;
    }

    // Create the toast HTML
    const toastHtml = `
        <div id="${toastId}" class="toast ${type}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                ${icon}
                <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                <small>Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // Add the toast to the container
    const toastContainer = document.getElementById('toast-container');
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Initialize the toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: duration
    });

    // Show the toast
    toast.show();

    // Remove the toast from the DOM after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

/**
 * Show a status message in the center of the screen
 *
 * @param {string} message The message to display
 * @param {string} type The type of message (info, success, warning, danger)
 * @param {number} duration The duration in milliseconds (default: 3000, 0 for no auto-hide)
 */
function showStatusMessage(message, type = 'info', duration = 3000) {
    const container = document.getElementById('status-message-container');
    const messageElement = document.getElementById('status-message');

    // Set the message and type
    messageElement.textContent = message;
    messageElement.className = `alert alert-${type} text-center`;

    // Show the container
    container.style.display = 'block';

    // Auto-hide after duration if not 0
    if (duration > 0) {
        setTimeout(() => {
            container.style.display = 'none';
        }, duration);
    }
}

/**
 * Show an error message
 *
 * @param {string} message The error message
 */
function showError(message) {
    console.error(message);
    showToast(message, 'error');
}

/**
 * Show a success message
 *
 * @param {string} message The success message
 */
function showSuccess(message) {
    console.log(message);
    showToast(message, 'success');
}

/**
 * Show a warning message
 *
 * @param {string} message The warning message
 */
function showWarning(message) {
    console.warn(message);
    showToast(message, 'warning');
}

/**
 * Show an info message
 *
 * @param {string} message The info message
 */
function showInfo(message) {
    console.log(message);
    showToast(message, 'info');
}