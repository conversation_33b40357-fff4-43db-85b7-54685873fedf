<?php


header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, DELETE, PUT");
header("Access-Control-Allow-Headers: Content-Type");

require_once '../controller/notificationsController.php';


if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$action = $_GET['action'] ?? null;

if (!$action) {
    // Pour les requêtes GET, on peut renvoyer toutes les notifications par défaut
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $action = 'getAll';
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Action parameter is required']);
        exit();
    }
}

// Récupérer les données en fonction de la méthode HTTP et du type de contenu
$data = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Si c'est un formulaire multipart (avec upload de fichier)
    if (isset($_SERVER['CONTENT_TYPE']) && strpos($_SERVER['CONTENT_TYPE'], 'multipart/form-data') !== false) {
        $data = $_POST;
        // Les fichiers sont déjà dans $_FILES
    } else {
        // Si c'est du JSON
        $data = json_decode(file_get_contents("php://input"), true);
    }
} else {
    // Pour les autres méthodes (GET, DELETE, etc.)
    $data = json_decode(file_get_contents("php://input"), true);
}

handleNotification($action, $data);
?>