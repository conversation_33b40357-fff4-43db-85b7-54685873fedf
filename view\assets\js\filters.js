// Function to load all fields
async function loadFilieres() {
    try {
        const response = await fetch('../../route/filiereRoute.php');
        const data = await response.json();
        if (data.data) {
            const filiereSelect = document.getElementById('filiere');
            filiereSelect.innerHTML = '<option value="" disabled selected>Select field</option>';
            filiereSelect.innerHTML += '<option value="none">No field</option>';
            data.data.forEach(filiere => {
                filiereSelect.innerHTML += `<option value="${filiere.id_filiere}">${filiere.nom_filiere}</option>`;
            });
        }
    } catch (error) {
        console.error('Error loading fields:', error);
    }
}

// Function to load levels based on selected field
async function loadNiveaux(id_filiere) {
    try {
        let url = '../../route/niveauRoute.php';
        if (id_filiere !== 'none' && id_filiere !== '') {
            url = `../../route/niveauRoute.php?id_filiere=${id_filiere}`;
        }

        console.log(`Loading levels with URL: ${url}`);

        const response = await fetch(url);
        const data = await response.json();
        console.log('Received levels data:', data);

        const niveauSelect = document.getElementById('niveau');
        niveauSelect.innerHTML = '<option value="" disabled selected>Select level</option>';

        if (data.data) {
            // Filter levels to show only those with id_filiere NULL when no field is selected
            const niveaux = id_filiere === 'none' || id_filiere === ''
                ? data.data.filter(niveau => niveau.id_filiere === null)
                : data.data;

            console.log('Filtered levels:', niveaux);
            console.log('Levels with null field:', data.data.filter(niveau => niveau.id_filiere === null));

            niveaux.forEach(niveau => {
                console.log(`Adding level: ${niveau.niveau}, id: ${niveau.id_niveau}, field_id: ${niveau.id_filiere}`);
                niveauSelect.innerHTML += `<option value="${niveau.id_niveau}" data-filiere="${niveau.id_filiere}">${niveau.niveau}</option>`;
            });
        }

        // Reset the group selector
        const groupeSelect = document.getElementById('groupe');
        groupeSelect.innerHTML = '<option value="" disabled selected>Select group</option>';
    } catch (error) {
        console.error('Error loading levels:', error);
    }
}

// Function to load groups based on selected field and level
async function loadGroupes(id_filiere, id_niveau) {
    try {
        if (!id_niveau) {
            const groupeSelect = document.getElementById('groupe');
            groupeSelect.innerHTML = '<option value="" disabled selected>Select group</option>';
            return;
        }

        console.log(`Loading groups for level: ${id_niveau}, field: ${id_filiere}`);

        let url;
        if (id_filiere === 'none' || !id_filiere) {
            // If no field is selected or if it's a level without field
            url = `../../route/groupeRoute.php?id_niveau=${id_niveau}`;
            console.log(`Using URL for level only: ${url}`);
        } else {
            url = `../../route/groupeRoute.php?id_filiere=${id_filiere}&id_niveau=${id_niveau}`;
            console.log(`Using URL for field and level: ${url}`);
        }

        const response = await fetch(url);
        const data = await response.json();
        console.log('Received data:', data);

        const groupeSelect = document.getElementById('groupe');
        groupeSelect.innerHTML = '<option value="" disabled selected>Select group</option>';
        groupeSelect.innerHTML += '<option value="all">All groups</option>';

        if (data.data && Array.isArray(data.data)) {
            console.log(`Found ${data.data.length} groups`);

            // Check if the data has the expected structure
            if (data.data.length > 0) {
                const firstGroup = data.data[0];
                if (firstGroup.id_groupe && firstGroup.nom_groupe) {
                    // Expected structure
                    data.data.forEach(groupe => {
                        groupeSelect.innerHTML += `<option value="${groupe.id_groupe}">${groupe.nom_groupe}</option>`;
                    });
                } else if (firstGroup.id_groupe) {
                    // Only id_groupe is available
                    data.data.forEach(groupe => {
                        groupeSelect.innerHTML += `<option value="${groupe.id_groupe}">Group ${groupe.id_groupe}</option>`;
                    });
                } else {
                    // Fallback for unexpected structure
                    data.data.forEach((groupe, index) => {
                        const groupeId = groupe.id || index + 1;
                        const groupeName = groupe.name || groupe.nom || `Group ${groupeId}`;
                        groupeSelect.innerHTML += `<option value="${groupeId}">${groupeName}</option>`;
                    });
                }
            } else {
                console.log('No groups found for this level');
                // Add a default group option when no groups are found
                groupeSelect.innerHTML += `<option value="default">Default group</option>`;
            }
        } else {
            console.log('No groups found or data.data is not an array');
            // Add a default group option when data structure is unexpected
            groupeSelect.innerHTML += `<option value="default">Default group</option>`;
        }
    } catch (error) {
        console.error('Error loading groups:', error);

        // In case of error, add a default group option
        const groupeSelect = document.getElementById('groupe');
        if (groupeSelect) {
            groupeSelect.innerHTML = '<option value="" disabled selected>Select group</option>';
            groupeSelect.innerHTML += '<option value="all">All groups</option>';
            groupeSelect.innerHTML += `<option value="default">Default group</option>`;
        }
    }
}

// Function to load available semesters from the database
async function loadSemestres() {
    try {
        const response = await fetch('../../route/semestreRoute.php');
        const data = await response.json();

        const semestreSelect = document.getElementById('semestre');
        semestreSelect.innerHTML = '<option value="" disabled selected>Select semester</option>';

        if (data.data) {
            data.data.forEach(semestre => {
                semestreSelect.innerHTML += `<option value="${semestre.semestre}">${semestre.semestre}</option>`;
            });
        }

        // If no semesters are found, add default options
        if (!data.data || data.data.length === 0) {
            semestreSelect.innerHTML += '<option value="S1">S1</option>';
            semestreSelect.innerHTML += '<option value="S2">S2</option>';
        }
    } catch (error) {
        console.error('Error loading semesters:', error);

        // In case of error, add default options
        const semestreSelect = document.getElementById('semestre');
        semestreSelect.innerHTML = '<option value="" disabled selected>Select semester</option>';
        semestreSelect.innerHTML += '<option value="S1">S1</option>';
        semestreSelect.innerHTML += '<option value="S2">S2</option>';
    }
}

// Function to check the structure of the group table
async function checkGroupeTableStructure() {
    try {
        const response = await fetch('../../route/groupeRoute.php?structure=true');
        const data = await response.json();
        console.log('Group table structure:', data);
        return data;
    } catch (error) {
        console.error('Error checking group table structure:', error);
        return null;
    }
}

// Initialize events
document.addEventListener('DOMContentLoaded', () => {
    // Check the structure of the group table
    checkGroupeTableStructure();

    // Load fields when the page loads
    loadFilieres();

    // Load semesters when the page loads
    loadSemestres();

    // Handle field change
    document.getElementById('filiere').addEventListener('change', (e) => {
        const selectedFiliere = e.target.value;
        loadNiveaux(selectedFiliere);
    });

    // Handle level change
    document.getElementById('niveau').addEventListener('change', (e) => {
        const selectedNiveau = e.target.value;
        const selectedOption = e.target.options[e.target.selectedIndex];
        const filiereFromNiveau = selectedOption.getAttribute('data-filiere');
        const selectedFiliere = document.getElementById('filiere').value;

        console.log(`Selected level: ${selectedNiveau}, field from level: ${filiereFromNiveau}, selected field: ${selectedFiliere}`);

        // If the level has a null field, use null as id_filiere to load groups
        if (filiereFromNiveau === 'null' || filiereFromNiveau === null) {
            console.log('This level has null field, loading groups with null field');
            loadGroupes(null, selectedNiveau);
        } else {
            loadGroupes(selectedFiliere, selectedNiveau);
        }
    });
});