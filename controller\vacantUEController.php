<?php
/**
 * Controller for managing vacant teaching units
 * This controller handles the logic for department heads to view and validate vacant teaching units
 */

// Include the model
require_once __DIR__ . '/../model/vacantUEModel.php';
require_once __DIR__ . '/../model/visitsModel.php';

/**
 * Get department information by ID
 *
 * @param int $departmentId The department ID
 * @return array Department information or error
 */
function getDepartmentInfo($departmentId) {
    if (!$departmentId) {
        return null;
    }

    return getDepartmentById($departmentId);
}

/**
 * Get unassigned teaching units for a department
 *
 * @param int $departmentId The department ID
 * @param string $academicYear The academic year (optional)
 * @return array Unassigned teaching units or error
 */
function getUnassignedUnitsForDepartment($departmentId, $academicYear = null) {
    if (!$departmentId) {
        return ["error" => "Department ID is required"];
    }

    return getUnassignedTeachingUnits($departmentId, $academicYear);
}

/**
 * Mark teaching unit vacancy status
 *
 * @param array $data Request data containing ue_id, department_id, is_vacant, marked_by, comments
 * @return array Success or error message
 */
function updateVacancyStatus($data) {
    // Validate required fields
    if (!isset($data['ue_id']) || !isset($data['department_id']) || !isset($data['is_vacant']) || !isset($data['marked_by'])) {
        return ["error" => "Missing required fields"];
    }

    $ueId = $data['ue_id'];
    $departmentId = $data['department_id'];
    $isVacant = (bool)$data['is_vacant'];
    $markedBy = $data['marked_by'];
    $comments = isset($data['comments']) ? $data['comments'] : null;
    $academicYear = isset($data['academic_year']) ? $data['academic_year'] : null;

    return markTeachingUnitVacancy($ueId, $departmentId, $isVacant, $markedBy, $comments, $academicYear);
}

/**
 * Record a visit to the vacant teaching units page
 *
 * @param string $userRole The role of the user visiting
 */
function recordVacantUnitsVisit($userRole) {
    if (function_exists('recordVisit')) {
        recordVisit('vacant_teaching_units', $userRole);
    }
}

/**
 * Process AJAX request for getting unassigned teaching units
 *
 * @param int $departmentId The department ID
 */
function processUnassignedUnitsRequest($departmentId) {
    header('Content-Type: application/json');

    $academicYear = isset($_GET['academic_year']) ? $_GET['academic_year'] : null;
    $result = getUnassignedUnitsForDepartment($departmentId, $academicYear);

    if (isset($result['error'])) {
        http_response_code(500);
        echo json_encode(['error' => $result['error']]);
    } else {
        echo json_encode(['data' => $result]);
    }
    exit;
}

/**
 * Process AJAX request for updating vacancy status
 */
function processVacancyStatusUpdate() {
    header('Content-Type: application/json');

    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON data']);
        exit;
    }

    $result = updateVacancyStatus($data);

    if (isset($result['error'])) {
        http_response_code(500);
        echo json_encode(['error' => $result['error']]);
    } else {
        echo json_encode($result);
    }
    exit;
}

/**
 * Get current academic year
 *
 * @return string Current academic year
 */
function getCurrentYear() {
    return getCurrentAcademicYear();
}

// Handle AJAX requests
if (isset($_GET['action'])) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check authentication
    if (!isset($_SESSION['user']['role']) || $_SESSION['user']['role'] !== 'chef de departement') {
        http_response_code(403);
        echo json_encode(['error' => 'Unauthorized access']);
        exit;
    }

    switch ($_GET['action']) {
        case 'getUnassignedUnits':
            if (!isset($_GET['department_id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Department ID is required']);
                exit;
            }
            processUnassignedUnitsRequest($_GET['department_id']);
            break;

        case 'updateVacancyStatus':
            processVacancyStatusUpdate();
            break;

        case 'getCurrentAcademicYear':
            header('Content-Type: application/json');
            echo json_encode(['academic_year' => getCurrentYear()]);
            exit;
            break;

        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            exit;
    }
}

// If we reach here, it's a regular page load
// Get department information if department ID is available
$departement = null;
$departementId = null;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION['user']['department_id'])) {
    $departementId = $_SESSION['user']['department_id'];
    $departement = getDepartmentInfo($departementId);
}

// Record the visit
recordVacantUnitsVisit('chef de departement');

?>
