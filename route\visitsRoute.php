<?php
require_once __DIR__ . '/../controller/visitsController.php';
require_once __DIR__ . '/../utils/response.php';

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Ajouter des logs pour le débogage
error_log("visitsRoute.php - Méthode: $method, Action: $action");
error_log("visitsRoute.php - URL: " . $_SERVER['REQUEST_URI']);

// Récupérer les données du corps de la requête pour POST
if ($method === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
}

// Router pour les visites
switch ($method) {
    case 'GET':
        if ($action === 'stats') {
            // Récupérer les statistiques de visite
            handleGetVisitStats();
        } elseif ($action === 'global') {
            // Récupérer les statistiques globales
            handleGetGlobalStats();
        } else {
            jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;

    case 'POST':
        if (!isset($data)) {
            jsonResponse(['success' => false, 'message' => 'Données invalides'], 400);
            break;
        }

        if ($action === 'record' || empty($action)) {
            // Enregistrer une visite
            handleRecordVisit($data);
        } else {
            jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;

    default:
        jsonResponse(['success' => false, 'message' => 'Méthode non supportée'], 405);
        break;
}
?>
