<?php
require_once "../model/enseignantModel.php";
require_once "../utils/response.php";

/**
 * Get all teachers for the personnel page
 */
function getAllPersonnel() {
    // Get all teachers
    $enseignants = getAllEnseignants();

    // Format the data for display
    foreach ($enseignants as &$enseignant) {
        if (isset($enseignant['role'])) {
            $enseignant['formatted_role'] = formatRoleName($enseignant['role']);
        } else {
            $enseignant['formatted_role'] = 'Enseignant';
        }

        // Set default profile picture if none exists
        if (empty($enseignant['profile_picture'])) {
            $initials = strtoupper(substr($enseignant['prenom'], 0, 1) . substr($enseignant['nom'], 0, 1));
            $enseignant['profile_picture'] = "https://ui-avatars.com/api/?name=" . urlencode($initials) . "&background=a1c4fd&color=fff&size=256";
        }
    }

    return $enseignants;
}

/**
 * Search for teachers by name
 *
 * @param string $searchTerm The search term
 * @return array Array of matching teachers
 */
function searchPersonnel($searchTerm) {
    // Search for teachers
    $enseignants = searchEnseignants($searchTerm);

    // Check if there was an error
    if (isset($enseignants['error'])) {
        return $enseignants;
    }

    // Format the data for display
    foreach ($enseignants as &$enseignant) {
        if (isset($enseignant['role'])) {
            $enseignant['formatted_role'] = formatRoleName($enseignant['role']);
        } else {
            $enseignant['formatted_role'] = 'Enseignant';
        }

        // Set default profile picture if none exists
        if (empty($enseignant['profile_picture'])) {
            $initials = strtoupper(substr($enseignant['prenom'], 0, 1) . substr($enseignant['nom'], 0, 1));
            $enseignant['profile_picture'] = "https://ui-avatars.com/api/?name=" . urlencode($initials) . "&background=a1c4fd&color=fff&size=256";
        }
    }

    return $enseignants;
}

/**
 * API function to get all personnel
 */
function getAllPersonnelAPI() {
    $personnel = getAllPersonnel();
    jsonResponse(['success' => true, 'data' => $personnel]);
}

/**
 * API function to search personnel
 *
 * @param string $searchTerm The search term
 */
function searchPersonnelAPI($searchTerm) {
    $personnel = searchPersonnel($searchTerm);

    if (isset($personnel['error'])) {
        jsonResponse(['success' => false, 'error' => $personnel['error']], 500);
    }

    jsonResponse(['success' => true, 'data' => $personnel]);
}