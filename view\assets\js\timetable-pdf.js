/**
 * Timetable PDF Generation
 * This file contains functions for generating PDF versions of the timetable
 */

// Wait for the document to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Get the download PDF button
    const downloadPdfBtn = document.getElementById('download-pdf-btn');

    // Add click event listener to the download PDF button
    if (downloadPdfBtn) {
        downloadPdfBtn.addEventListener('click', function() {
            // Check if we're on the exam schedule page
            const isExamPage = window.location.href.includes('exam-schedule');
            if (isExamPage) {
                generatePDF('Exam Schedule');
            } else {
                generatePDF('Timetable');
            }
        });
    }
});

/**
 * Generate a PDF of the current timetable
 * @param {string} customTitle - Optional custom title for the PDF
 */
function generatePDF(customTitle) {
    // Create and show loading overlay
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'pdf-loading-overlay';
    loadingOverlay.innerHTML = `
        <div class="pdf-loading-spinner"></div>
        <div class="pdf-loading-text">Generating PDF, please wait...</div>
    `;
    document.body.appendChild(loadingOverlay);

    try {
        // Create a temporary container for the PDF content
        const pdfContainer = document.createElement('div');
        pdfContainer.className = 'pdf-container';
        pdfContainer.style.position = 'absolute';
        pdfContainer.style.left = '-9999px';
        pdfContainer.style.top = '-9999px';
        pdfContainer.style.width = '297mm'; // A4 landscape width
        pdfContainer.style.minHeight = '210mm'; // A4 landscape height
        pdfContainer.style.height = 'auto'; // Allow it to grow as needed
        pdfContainer.style.backgroundColor = 'white';
        pdfContainer.style.zIndex = '-1000';
        pdfContainer.style.overflow = 'visible'; // Ensure content isn't clipped
        document.body.appendChild(pdfContainer);

        // Use custom title if provided, otherwise use a default title
        let title = customTitle || 'Timetable';

        // Get the applied filters
        const appliedFilters = document.getElementById('applied-filters');
        const filtersText = appliedFilters ? appliedFilters.textContent.trim() : '';

        // Create header for PDF
        const header = document.createElement('div');
        header.style.padding = '20px';
        header.style.borderBottom = '1px solid #eaeaea';
        header.innerHTML = `
            <h1 style="font-size: 24px; margin: 0 0 10px 0; color: #333;">${title}</h1>
            ${filtersText ? `<p style="font-size: 14px; margin: 0; color: #666;">Filters: ${filtersText}</p>` : ''}
            <p style="font-size: 14px; margin: 10px 0 0 0; color: #666;">Generated on: ${new Date().toLocaleDateString()}</p>
        `;
        pdfContainer.appendChild(header);

        // Clone the timetable
        const originalTable = document.querySelector('.timetable');
        const tableClone = originalTable.cloneNode(true);

        // Apply PDF-specific styles to the cloned table
        tableClone.style.width = '100%';
        tableClone.style.borderCollapse = 'collapse';
        tableClone.style.marginTop = '20px';
        tableClone.style.marginBottom = '20px';
        tableClone.style.fontSize = '12px';
        tableClone.style.tableLayout = 'fixed';

        // Ensure all rows are visible and properly sized
        const rows = tableClone.querySelectorAll('tr');
        rows.forEach(row => {
            row.style.display = 'table-row';
            row.style.pageBreakInside = 'avoid';
        });

        // Process all cells in the cloned table
        const cells = tableClone.querySelectorAll('th, td');
        cells.forEach(cell => {
            cell.style.border = '1px solid #dee2e6';
            cell.style.padding = '8px';
            cell.style.textAlign = 'center';
            cell.style.verticalAlign = 'top';
            cell.style.minHeight = '80px'; // Ensure minimum height for cells
            cell.style.height = 'auto'; // Allow cells to grow as needed

            // Style header cells
            if (cell.tagName === 'TH') {
                cell.style.backgroundColor = '#f8f9fa';
                cell.style.fontWeight = 'bold';
                cell.style.color = '#333';
            }

            // Style time slot cells
            if (cell.classList.contains('time-slot')) {
                cell.style.backgroundColor = '#f8f9fa';
                cell.style.fontWeight = 'bold';
                cell.style.color = '#333';
            }

            // Ensure day slots (first column) are visible
            if (cell.classList.contains('day-slot')) {
                cell.style.backgroundColor = '#f8f9fa';
                cell.style.fontWeight = 'bold';
                cell.style.color = '#333';
                cell.style.width = '120px'; // Fixed width for day column
            }
        });

        // Process all class cards in the cloned table
        const classCards = tableClone.querySelectorAll('.class-card');
        classCards.forEach(card => {
            // Reset positioning and box-shadow for PDF
            card.style.boxShadow = 'none';
            card.style.margin = '0';
            card.style.height = 'auto';
            card.style.minHeight = 'auto';
            card.style.position = 'relative';
            card.style.transform = 'none';
            card.style.transition = 'none';
            card.style.padding = '8px';
            card.style.borderRadius = '4px';

            // Style the class title
            const classTitle = card.querySelector('.class-title');
            if (classTitle) {
                classTitle.style.fontSize = '12px';
                classTitle.style.fontWeight = 'bold';
                classTitle.style.marginBottom = '6px';
                classTitle.style.color = '#333';
            }

            // Style the class info
            const classInfos = card.querySelectorAll('.class-info');
            classInfos.forEach(info => {
                info.style.fontSize = '10px';
                info.style.margin = '3px 0';
                info.style.color = '#555';
            });

            // Make sure the subject tag is visible
            const subjectTag = card.querySelector('.class-subject');
            if (subjectTag) {
                subjectTag.style.position = 'static';
                subjectTag.style.display = 'inline-block';
                subjectTag.style.marginTop = '8px';
                subjectTag.style.boxShadow = 'none';
                subjectTag.style.fontSize = '9px';
                subjectTag.style.padding = '2px 6px';
                subjectTag.style.borderRadius = '3px';
                subjectTag.style.fontWeight = 'bold';
                subjectTag.style.color = 'white';
            }

            // Preserve the color coding based on session type
            if (card.classList.contains('lecture')) {
                card.style.borderTop = '4px solid #4361ee';
                card.style.backgroundColor = '#f0f7ff';
                if (subjectTag) subjectTag.style.backgroundColor = '#4361ee';
            } else if (card.classList.contains('tp')) {
                card.style.borderTop = '4px solid #2b9348';
                card.style.backgroundColor = '#e6ffe6';
                if (subjectTag) subjectTag.style.backgroundColor = '#2b9348';
            } else if (card.classList.contains('td')) {
                card.style.borderTop = '4px solid #ff9f1c';
                card.style.backgroundColor = '#fff7e6';
                if (subjectTag) subjectTag.style.backgroundColor = '#ff9f1c';
            } else if (card.classList.contains('exam')) {
                card.style.borderTop = '4px solid #e63946';
                card.style.backgroundColor = '#ffe6e6';
                if (subjectTag) subjectTag.style.backgroundColor = '#e63946';
            } else {
                card.style.borderTop = '4px solid #6c757d';
                card.style.backgroundColor = '#f8f9fa';
                if (subjectTag) subjectTag.style.backgroundColor = '#6c757d';
            }
        });

        // Add the styled table to the PDF container
        pdfContainer.appendChild(tableClone);

        // Add footer
        const footer = document.createElement('div');
        footer.style.padding = '10px 20px';
        footer.style.borderTop = '1px solid #eaeaea';
        footer.style.fontSize = '10px';
        footer.style.color = '#999';
        footer.style.textAlign = 'center';
        footer.innerHTML = 'Generated by UniAdmin Timetable System';
        pdfContainer.appendChild(footer);

        // Make sure the table rows are all visible for measurement
        const tableRows = tableClone.querySelectorAll('tr');
        tableRows.forEach(row => {
            row.style.display = 'table-row';
            row.style.visibility = 'visible';
        });

        // Ensure the container has enough height to fit all content
        pdfContainer.style.height = 'auto';

        // Use html2canvas to capture the prepared container
        html2canvas(pdfContainer, {
            scale: 2, // Higher scale for better quality
            useCORS: true,
            logging: false,
            backgroundColor: '#ffffff',
            windowWidth: pdfContainer.offsetWidth,
            windowHeight: pdfContainer.offsetHeight,
            // Ensure the entire content is captured
            height: pdfContainer.scrollHeight,
            // Increase the canvas height to ensure all content is captured
            onclone: function(_, clonedElement) {
                // Make sure all rows are visible in the clone
                const clonedRows = clonedElement.querySelectorAll('tr');
                clonedRows.forEach(row => {
                    row.style.display = 'table-row';
                    row.style.visibility = 'visible';
                });
            }
        }).then(canvas => {
            // Initialize jsPDF
            const { jsPDF } = window.jspdf;

            // Create a new PDF document (landscape orientation)
            const pdf = new jsPDF({
                orientation: 'landscape',
                unit: 'mm',
                format: 'a4'
            });

            // Convert the canvas to an image
            const imgData = canvas.toDataURL('image/png');

            // Calculate the width and height to fit the page
            const imgProps = pdf.getImageProperties(imgData);
            const pdfWidth = pdf.internal.pageSize.getWidth();
            // Calculate height while maintaining aspect ratio
            const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

            // Check if the content is too tall for a single page
            const pageHeight = pdf.internal.pageSize.getHeight();

            if (pdfHeight > pageHeight) {
                // Content is too tall, scale it to fit the page height with a small margin
                const scaleFactor = (pageHeight - 10) / pdfHeight;
                const scaledWidth = pdfWidth * scaleFactor;

                // Center the image horizontally
                const xOffset = (pdfWidth - scaledWidth) / 2;

                // Add the image to the PDF with scaling to fit the page
                pdf.addImage(imgData, 'PNG', xOffset, 5, scaledWidth, pageHeight - 10);
            } else {
                // Content fits, add it normally with small margins
                pdf.addImage(imgData, 'PNG', 0, 5, pdfWidth, pdfHeight);
            }

            // Generate a filename with date
            const now = new Date();
            const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
            // Use appropriate filename based on the title
            const filePrefix = title.toLowerCase().includes('exam') ? 'exam_schedule' : 'timetable';
            const filename = `${filePrefix}_${dateStr}.pdf`;

            // Save the PDF
            pdf.save(filename);

            // Clean up - remove the temporary container and loading overlay
            document.body.removeChild(pdfContainer);
            document.body.removeChild(loadingOverlay);

            // Show success message
            showSuccessMessage('PDF generated successfully!');
        }).catch(error => {
            console.error('Error generating PDF:', error);
            showErrorMessage('Error generating PDF: ' + error.message);

            // Clean up on error
            if (document.body.contains(pdfContainer)) {
                document.body.removeChild(pdfContainer);
            }
            if (document.body.contains(loadingOverlay)) {
                document.body.removeChild(loadingOverlay);
            }
        });
    } catch (error) {
        console.error('Error preparing PDF:', error);
        showErrorMessage('Error preparing PDF: ' + error.message);

        // Remove loading overlay on error
        if (document.body.contains(loadingOverlay)) {
            document.body.removeChild(loadingOverlay);
        }
    }
}
