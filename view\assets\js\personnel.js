document.addEventListener('DOMContentLoaded', function() {
    // Load all personnel on page load
    loadPersonnel();

    const searchInput = document.getElementById('searchInput');
    const cancelBtn = document.getElementById('cancelBtn');

    // Show/hide cancel button based on input content
    searchInput.addEventListener('input', function() {
        if (this.value.trim() !== '') {
            cancelBtn.classList.remove('d-none');
        } else {
            cancelBtn.classList.add('d-none');
        }
    });

    // Search button click event
    document.getElementById('searchBtn').addEventListener('click', function() {
        const searchTerm = searchInput.value.trim();
        if (searchTerm) {
            searchPersonnel(searchTerm);
        } else {
            loadPersonnel();
        }
    });

    // Cancel button click event
    cancelBtn.addEventListener('click', function() {
        searchInput.value = '';
        this.classList.add('d-none');
        loadPersonnel();
    });

    // Enter key in search input
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const searchTerm = this.value.trim();
            if (searchTerm) {
                searchPersonnel(searchTerm);
            } else {
                loadPersonnel();
            }
        }
    });
});

// Function to load all personnel
function loadPersonnel() {
    fetch('../../route/personnelRoute.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                displayPersonnel(data.data);
            } else {
                console.error('Error loading personnel:', data.error);
                displayError('Une erreur est survenue lors du chargement des données.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            displayError('Une erreur est survenue lors du chargement des données.');
        });
}

// Function to search personnel
function searchPersonnel(searchTerm) {
    fetch(`../../route/personnelRoute.php?search=${encodeURIComponent(searchTerm)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                displayPersonnel(data.data);
            } else {
                console.error('Error searching personnel:', data.error);
                displayError('Une erreur est survenue lors de la recherche.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            displayError('Une erreur est survenue lors de la recherche.');
        });
}

// Function to display personnel in the table
function displayPersonnel(personnel) {
    const tableBody = document.getElementById('personnelTableBody');
    const countElement = document.getElementById('countValue');

    // Update count
    countElement.textContent = personnel.length;

    // Clear table
    tableBody.innerHTML = '';

    if (personnel.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="5" class="text-center">Aucun résultat trouvé</td>`;
        tableBody.appendChild(row);
        return;
    }

    // Add each person to the table
    personnel.forEach(person => {
        const row = document.createElement('tr');

        // Determine role badge class based on role
        let roleBadgeClass = 'role-enseignant';
        if (person.role === 'chef de departement') {
            roleBadgeClass = 'role-chef';
        } else if (person.role === 'coordinateur') {
            roleBadgeClass = 'role-coordinateur';
        } else if (person.role === 'vacataire') {
            roleBadgeClass = 'role-vacataire';
        } else if (person.role === 'chef de filiere') {
            roleBadgeClass = 'role-chef-filiere';
        }

        row.innerHTML = `
            <td>${person.nom || ''}</td>
            <td>${person.prenom || ''}</td>
            <td>${person.email || ''}</td>
            <td><img src="${person.profile_picture}" alt="${person.nom} ${person.prenom}" class="profile-pic"></td>
            <td><span class="role-badge ${roleBadgeClass}">${person.formatted_role || 'Enseignant'}</span></td>
        `;

        tableBody.appendChild(row);
    });
}

// Function to display error message
function displayError(message) {
    const tableBody = document.getElementById('personnelTableBody');
    const countElement = document.getElementById('countValue');

    // Update count
    countElement.textContent = '0';

    // Display error message
    tableBody.innerHTML = `<tr><td colspan="5" class="text-center text-danger">${message}</td></tr>`;
}