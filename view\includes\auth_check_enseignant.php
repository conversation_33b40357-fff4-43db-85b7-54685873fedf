<?php
/**
 * Fichier de vérification d'authentification pour les enseignants
 *
 * Ce fichier vérifie si l'utilisateur est authentifié et a le rôle d'enseignant.
 * Il doit être inclus au début de chaque page réservée aux enseignants.
 */

// Inclure le fichier de constantes
require_once __DIR__ . '/../../config/constants.php';

// Inclure les fonctions d'authentification
require_once __DIR__ . '/../../utils/auth.php';

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérifier si l'utilisateur est connecté
if (!isLoggedIn()) {
    // Sauvegarder l'URL actuelle pour redirection après connexion
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];

    // Rediriger vers la page de connexion
    header('Location: ' . BASE_URL . '/index.php');
    exit;
}

// Vérifier l'expiration de la session (30 minutes)
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
    // Détruire la session
    session_destroy();

    // Rediriger vers la page de connexion
    header('Location: ' . BASE_URL . '/index.php?expired=1');
    exit;
}

// Vérifier si l'IP a changé
if (isset($_SESSION['ip']) && $_SESSION['ip'] !== $_SERVER['REMOTE_ADDR']) {
    // Détruire la session
    session_destroy();

    // Rediriger vers la page de connexion
    header('Location: ' . BASE_URL . '/index.php?security=1');
    exit;
}

// Mettre à jour le timestamp de dernière activité
$_SESSION['last_activity'] = time();

// Vérifier si l'utilisateur est un enseignant ou un administrateur
if (!isTeacher() && !isAdmin()) {
    // Rediriger vers la page d'erreur
    header('Location: ' . BASE_URL . '/view/unauthorized.php');
    exit;
}

// Si l'utilisateur est un enseignant, vérifier que ses informations sont disponibles
if (isTeacher()) {
    // Inclure le modèle d'authentification pour accéder à la fonction getTeacher
    require_once __DIR__ . '/../../model/authModel.php';

    // Vérifier si les informations de l'enseignant sont déjà dans la session
    if (!isset($_SESSION['user']['teacher_id']) || !isset($_SESSION['user']['department_id']) || !isset($_SESSION['user']['specialty_id'])) {
        // Récupérer les informations de l'enseignant
        $teacherInfo = getTeacher($_SESSION['user']['username']);

        // Si aucune information n'est trouvée, rediriger vers une page d'erreur
        if (!$teacherInfo) {
            // Enregistrer un message d'erreur dans la session
            $_SESSION['error_message'] = "Vos informations d'enseignant n'ont pas été trouvées. Veuillez contacter l'administrateur.";

            // Rediriger vers une page d'erreur
            header('Location: ' . BASE_URL . '/view/error.php');
            exit;
        }

        // Stocker les informations de l'enseignant dans la session
        $_SESSION['user']['teacher_id'] = $teacherInfo['id_enseignant'];
        $_SESSION['user']['specialty_id'] = $teacherInfo['id_specialite'];
        $_SESSION['user']['department_id'] = $teacherInfo['id_departement'];
        $_SESSION['user']['specialty_name'] = $teacherInfo['specialty_name'];
        $_SESSION['user']['department_name'] = $teacherInfo['department_name'];
    }
}
?>
