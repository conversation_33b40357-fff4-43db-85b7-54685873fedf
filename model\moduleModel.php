<?php
require_once __DIR__ . "/../config/db.php";

/**
 * Get all modules
 *
 * @return array Array of modules
 */
function getAllModules() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllModules");
        return ["error" => "Database connection error"];
    }

    $sql = "SELECT m.*, f.nom_filiere, n.nom as niveau
            FROM module m
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            ORDER BY m.nom";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllModules: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching modules: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $modules[] = $row;
    }

    mysqli_close($conn);
    return $modules;
}

/**
 * Get modules for a specific teacher
 *
 * @param int $teacherId The teacher ID
 * @param int|null $filiereId The field ID (optional)
 * @param int|null $niveauId The level ID (optional)
 * @param int|null $semestreId The semester ID (optional)
 * @return array Array of modules
 */
function getTeacherModules($teacherId, $filiereId = null, $niveauId = null, $semestreId = null) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherModules");
        return ["error" => "Database connection error"];
    }

    // Sanitize inputs
    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $filiereId = $filiereId ? mysqli_real_escape_string($conn, $filiereId) : null;
    $niveauId = $niveauId ? mysqli_real_escape_string($conn, $niveauId) : null;
    $semestreId = $semestreId ? mysqli_real_escape_string($conn, $semestreId) : null;

    // Check if the affectation table exists
    $checkAffectationTable = mysqli_query($conn, "SHOW TABLES LIKE 'affectation'");

    if (mysqli_num_rows($checkAffectationTable) > 0) {
        // If the affectation table exists, use it to get the modules
        // Verify column names in the tables
        $checkModuleColumns = mysqli_query($conn, "SHOW COLUMNS FROM module");
        $moduleColumns = [];
        while ($col = mysqli_fetch_assoc($checkModuleColumns)) {
            $moduleColumns[] = $col['Field'];
        }

        $checkUEColumns = mysqli_query($conn, "SHOW COLUMNS FROM uniteenseignement");
        $ueColumns = [];
        while ($col = mysqli_fetch_assoc($checkUEColumns)) {
            $ueColumns[] = $col['Field'];
        }

        // Determine the correct field names based on the database structure
        $moduleIdField = in_array('id', $moduleColumns) ? 'id' : 'id_module';
        $ueModuleIdField = in_array('module_id', $ueColumns) ? 'module_id' : 'id_module';
        $ueIdField = in_array('id', $ueColumns) ? 'id' : 'id';
        $ueTypeField = in_array('type', $ueColumns) ? 'type' : 'type_ue';

        // Determine the semester field name
        $semestreField = in_array('id_semestre', $moduleColumns) ? 'id_semestre' :
                        (in_array('semestre_id', $moduleColumns) ? 'semestre_id' :
                        (in_array('semestre', $moduleColumns) ? 'semestre' : null));

        error_log("Module ID field: $moduleIdField, UE Module ID field: $ueModuleIdField, UE ID field: $ueIdField, UE Type field: $ueTypeField, Semestre field: $semestreField");

        // Build the query to get modules directly from the affectation table
        // Filter for teaching units of type 'Cours' only
        $sql = "SELECT DISTINCT m.*, f.nom_filiere, n.nom as niveau, s.nom as semestre_nom
                FROM module m
                JOIN uniteenseignement ue ON m.$moduleIdField = ue.$ueModuleIdField
                JOIN affectation a ON ue.$ueIdField = a.unite_enseignement_id
                LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                LEFT JOIN niveaux n ON m.id_niveau = n.id
                LEFT JOIN semestre s ON m.$semestreField = s.id
                WHERE a.professeur_id = '$teacherId'
                AND ue.$ueTypeField = 'Cours'";

        // Add filters if provided
        if ($filiereId) {
            $sql .= " AND m.filiere_id = '$filiereId'";
        }

        if ($niveauId) {
            $sql .= " AND m.id_niveau = '$niveauId'";
        }

        if ($semestreId && $semestreField) {
            $sql .= " AND m.$semestreField = '$semestreId'";
        }

        $sql .= " ORDER BY m.nom";

        error_log("Using affectation table to get modules for teacher ID: $teacherId with SQL: $sql");
    } else {
        // If the affectation table doesn't exist, get modules based on seances
        // Verify column names in the tables
        $checkModuleColumns = mysqli_query($conn, "SHOW COLUMNS FROM module");
        $moduleColumns = [];
        while ($col = mysqli_fetch_assoc($checkModuleColumns)) {
            $moduleColumns[] = $col['Field'];
        }

        $checkSeanceColumns = mysqli_query($conn, "SHOW COLUMNS FROM seance");
        $seanceColumns = [];
        while ($col = mysqli_fetch_assoc($checkSeanceColumns)) {
            $seanceColumns[] = $col['Field'];
        }

        // Determine the correct field names
        $moduleIdField = in_array('id', $moduleColumns) ? 'id' : 'id_module';
        $seanceModuleIdField = in_array('id_module', $seanceColumns) ? 'id_module' : 'module_id';
        $seanceTeacherIdField = in_array('id_enseignant', $seanceColumns) ? 'id_enseignant' : 'enseignant_id';
        $seanceTypeField = in_array('type', $seanceColumns) ? 'type' : 'type_seance';

        // Determine the semester field name
        $semestreField = in_array('id_semestre', $moduleColumns) ? 'id_semestre' :
                        (in_array('semestre_id', $moduleColumns) ? 'semestre_id' :
                        (in_array('semestre', $moduleColumns) ? 'semestre' : null));

        error_log("Module ID field: $moduleIdField, Seance Module ID field: $seanceModuleIdField, Seance Teacher ID field: $seanceTeacherIdField, Seance Type field: $seanceTypeField, Semestre field: $semestreField");

        $sql = "SELECT DISTINCT m.*, f.nom_filiere, n.nom as niveau, s.nom as semestre_nom
                FROM module m
                JOIN seance sc ON m.$moduleIdField = sc.$seanceModuleIdField
                LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                LEFT JOIN niveaux n ON m.id_niveau = n.id
                LEFT JOIN semestre s ON m.$semestreField = s.id
                WHERE sc.$seanceTeacherIdField = '$teacherId'
                AND sc.$seanceTypeField = 'Cours'";

        // Add filters if provided
        if ($filiereId) {
            $sql .= " AND m.filiere_id = '$filiereId'";
        }

        if ($niveauId) {
            $sql .= " AND m.id_niveau = '$niveauId'";
        }

        if ($semestreId && $semestreField) {
            $sql .= " AND m.$semestreField = '$semestreId'";
        }

        $sql .= " ORDER BY m.nom";

        error_log("Using seance table to get modules for teacher ID: $teacherId with SQL: $sql");
    }

    // Log the SQL query for debugging
    error_log("Executing SQL query: " . $sql);

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherModules: " . $error);
        error_log("SQL query that failed: " . $sql);
        mysqli_close($conn);
        return ["error" => "Error fetching modules: " . $error];
    }

    $modules = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Add additional information about the module's relationships
        if (isset($row[$semestreField])) {
            $row['semestre_id'] = $row[$semestreField];
        }
        $modules[] = $row;
    }

    // If no modules found, try a fallback query
    if (empty($modules) && $filiereId) {
        error_log("No modules found with the primary query. Trying fallback query.");

        // Fallback query to get all modules for this field
        // Still try to filter for teaching units of type 'Cours' if possible
        $fallbackSql = "SELECT DISTINCT m.*, f.nom_filiere, n.nom as niveau, s.nom as semestre_nom
                        FROM module m
                        JOIN uniteenseignement ue ON m.$moduleIdField = ue.$ueModuleIdField
                        LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                        LEFT JOIN niveaux n ON m.id_niveau = n.id
                        LEFT JOIN semestre s ON m.$semestreField = s.id
                        WHERE m.filiere_id = '$filiereId'
                        AND ue.$ueTypeField = 'Cours'";

        // Add level filter if specified
        if ($niveauId) {
            $fallbackSql .= " AND m.id_niveau = '$niveauId'";
        }

        // Add semester filter if specified
        if ($semestreId && $semestreField) {
            $fallbackSql .= " AND m.$semestreField = '$semestreId'";
        }

        $fallbackSql .= " ORDER BY m.nom";

        error_log("Executing fallback SQL query: " . $fallbackSql);

        $fallbackResult = mysqli_query($conn, $fallbackSql);

        if ($fallbackResult) {
            while ($row = mysqli_fetch_assoc($fallbackResult)) {
                // Add additional information about the module's relationships
                if (isset($row[$semestreField])) {
                    $row['semestre_id'] = $row[$semestreField];
                }
                $modules[] = $row;
            }

            error_log("Fallback query returned " . count($modules) . " modules.");
        } else {
            error_log("Fallback query failed: " . mysqli_error($conn));
        }
    }

    mysqli_close($conn);
    return $modules;
}

/**
 * Get a module by ID
 *
 * @param int $id Module ID
 * @return array|null Module data or null if not found
 */
function getModuleById($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getModuleById");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);

    // Check the structure of the module table
    $checkModuleColumns = mysqli_query($conn, "SHOW COLUMNS FROM module");
    $moduleColumns = [];
    while ($col = mysqli_fetch_assoc($checkModuleColumns)) {
        $moduleColumns[] = $col['Field'];
    }

    // Check if the enseignant table exists
    $checkEnseignantTable = mysqli_query($conn, "SHOW TABLES LIKE 'enseignant'");
    $enseignantTableExists = mysqli_num_rows($checkEnseignantTable) > 0;

    // Check if the module table has the teacher and coordinator columns
    $hasTeacherColumn = in_array('id_enseignant', $moduleColumns) || in_array('enseignant_id', $moduleColumns);
    $hasCoordinatorColumn = in_array('id_coordinateur', $moduleColumns) || in_array('coordinateur_id', $moduleColumns);

    // Determine the column names to use
    $teacherColumnName = in_array('id_enseignant', $moduleColumns) ? 'id_enseignant' :
                        (in_array('enseignant_id', $moduleColumns) ? 'enseignant_id' : null);
    $coordinatorColumnName = in_array('id_coordinateur', $moduleColumns) ? 'id_coordinateur' :
                            (in_array('coordinateur_id', $moduleColumns) ? 'coordinateur_id' : null);

    // Determine the semester field name
    $semestreField = in_array('id_semestre', $moduleColumns) ? 'id_semestre' :
                    (in_array('semestre_id', $moduleColumns) ? 'semestre_id' :
                    (in_array('semestre', $moduleColumns) ? 'semestre' : null));

    // Log the column information for debugging
    error_log("Module columns: " . implode(", ", $moduleColumns));
    error_log("Teacher column: " . ($teacherColumnName ?? 'none'));
    error_log("Coordinator column: " . ($coordinatorColumnName ?? 'none'));
    error_log("Semestre field: " . ($semestreField ?? 'none'));

    if ($enseignantTableExists && $hasTeacherColumn && $hasCoordinatorColumn) {
        // If the enseignant table exists and module has the necessary columns, join with it
        $sql = "SELECT m.*, f.nom_filiere, n.nom as niveau, s.nom as semestre_nom,
                CONCAT(e.prenom, ' ', e.nom) as enseignant,
                CONCAT(c.prenom, ' ', c.nom) as coordinateur
                FROM module m
                LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                LEFT JOIN niveaux n ON m.id_niveau = n.id
                LEFT JOIN semestre s ON m.$semestreField = s.id
                LEFT JOIN enseignant e ON m.$teacherColumnName = e.id_enseignant
                LEFT JOIN enseignant c ON m.$coordinatorColumnName = c.id_enseignant
                WHERE m.id = '$id'";
    } else {
        // If the enseignant table doesn't exist or module doesn't have the necessary columns, just get the basic module info
        $sql = "SELECT m.*, f.nom_filiere, n.nom as niveau, s.nom as semestre_nom
                FROM module m
                LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                LEFT JOIN niveaux n ON m.id_niveau = n.id
                LEFT JOIN semestre s ON m.$semestreField = s.id
                WHERE m.id = '$id'";
    }

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getModuleById: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching module: " . $error];
    }

    $module = mysqli_fetch_assoc($result);

    // Add additional information about the module's relationships
    if ($module && isset($module[$semestreField])) {
        $module['semestre_id'] = $module[$semestreField];
    }

    mysqli_close($conn);
    return $module;
}

/**
 * Create a new module
 *
 * @param array $data Module data
 * @return bool|array True on success, error array on failure
 */
function createModule($data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in createModule");
        return ["error" => "Database connection error"];
    }

    // Prepare the query
    $query = "INSERT INTO module (nom, volume_total, filiere_id, id_niveau, semestre)
              VALUES (?, ?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        $error = mysqli_error($conn);
        error_log("Error preparing statement in createModule: " . $error);
        mysqli_close($conn);
        return ["error" => "Error preparing statement: " . $error];
    }

    // Bind parameters
    mysqli_stmt_bind_param(
        $stmt,
        "siiss",
        $data['nom'],
        $data['volume_total'],
        $data['filiere_id'],
        $data['id_niveau'],
        $data['semestre']
    );

    // Execute the statement
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        $error = mysqli_stmt_error($stmt);
        error_log("Error executing statement in createModule: " . $error);
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        return ["error" => "Error creating module: " . $error];
    }

    // Get the ID of the newly created module
    $id = mysqli_insert_id($conn);

    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    return ["id" => $id];
}

/**
 * Update a module
 *
 * @param int $id Module ID
 * @param array $data Module data
 * @return bool|array True on success, error array on failure
 */
function updateModule($id, $data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateModule");
        return ["error" => "Database connection error"];
    }

    // Prepare the query
    $query = "UPDATE module
              SET nom = ?, volume_total = ?, filiere_id = ?, id_niveau = ?, semestre = ?
              WHERE id = ?";

    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        $error = mysqli_error($conn);
        error_log("Error preparing statement in updateModule: " . $error);
        mysqli_close($conn);
        return ["error" => "Error preparing statement: " . $error];
    }

    // Bind parameters
    mysqli_stmt_bind_param(
        $stmt,
        "siissi",
        $data['nom'],
        $data['volume_total'],
        $data['filiere_id'],
        $data['id_niveau'],
        $data['semestre'],
        $id
    );

    // Execute the statement
    $result = mysqli_stmt_execute($stmt);

    if (!$result) {
        $error = mysqli_stmt_error($stmt);
        error_log("Error executing statement in updateModule: " . $error);
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        return ["error" => "Error updating module: " . $error];
    }

    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    return true;
}

/**
 * Delete a module
 *
 * @param int $id Module ID
 * @return bool|array True on success, error array on failure
 */
function deleteModule($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in deleteModule");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);

    // Delete the module
    $query = "DELETE FROM module WHERE id = '$id'";
    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in deleteModule: " . $error);
        mysqli_close($conn);
        return ["error" => "Error deleting module: " . $error];
    }

    mysqli_close($conn);
    return true;
}

/**
 * Get all modules for a teacher without filtering
 * Only returns modules where the teacher is assigned to teach the lecture component ('cours')
 *
 * @param int $teacherId The teacher ID
 * @return array Array of modules
 */
function getAllTeacherModules($teacherId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllTeacherModules");
        return ["error" => "Database connection error"];
    }

    // Sanitize inputs
    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    // Check if the affectation table exists
    $checkAffectationTable = mysqli_query($conn, "SHOW TABLES LIKE 'affectation'");

    if (mysqli_num_rows($checkAffectationTable) > 0) {
        // Verify column names in the tables
        $checkModuleColumns = mysqli_query($conn, "SHOW COLUMNS FROM module");
        $moduleColumns = [];
        while ($col = mysqli_fetch_assoc($checkModuleColumns)) {
            $moduleColumns[] = $col['Field'];
        }

        $checkUEColumns = mysqli_query($conn, "SHOW COLUMNS FROM uniteenseignement");
        $ueColumns = [];
        while ($col = mysqli_fetch_assoc($checkUEColumns)) {
            $ueColumns[] = $col['Field'];
        }

        // Determine the correct field names based on the database structure
        $moduleIdField = in_array('id', $moduleColumns) ? 'id' : 'id_module';
        $ueModuleIdField = in_array('module_id', $ueColumns) ? 'module_id' : 'id_module';
        $ueIdField = in_array('id', $ueColumns) ? 'id' : 'id';
        $ueTypeField = in_array('type', $ueColumns) ? 'type' : 'type_ue';

        // Determine the semester field name
        $semestreField = in_array('id_semestre', $moduleColumns) ? 'id_semestre' :
                        (in_array('semestre_id', $moduleColumns) ? 'semestre_id' :
                        (in_array('semestre', $moduleColumns) ? 'semestre' : null));

        error_log("Module ID field: $moduleIdField, UE Module ID field: $ueModuleIdField, UE ID field: $ueIdField, UE Type field: $ueTypeField, Semestre field: $semestreField");

        // Build the query to get all modules for the teacher where they teach the 'Cours' component
        $sql = "SELECT DISTINCT m.*, f.nom_filiere, n.nom as niveau, s.nom as semestre_nom
                FROM module m
                JOIN uniteenseignement ue ON m.$moduleIdField = ue.$ueModuleIdField
                JOIN affectation a ON ue.$ueIdField = a.unite_enseignement_id
                LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                LEFT JOIN niveaux n ON m.id_niveau = n.id
                LEFT JOIN semestre s ON m.$semestreField = s.id
                WHERE a.professeur_id = '$teacherId'
                AND ue.$ueTypeField = 'Cours'
                ORDER BY f.nom_filiere, n.nom, s.nom, m.nom";

        error_log("Using affectation table to get all modules for teacher ID: $teacherId with SQL: $sql");

        $result = mysqli_query($conn, $sql);

        if (!$result) {
            $error = mysqli_error($conn);
            error_log("Error executing query: " . $error);
            mysqli_close($conn);
            return ["error" => "Database query error: " . $error];
        }

        $modules = [];
        while ($row = mysqli_fetch_assoc($result)) {
            // Add additional information about the module's relationships
            if (isset($row[$semestreField])) {
                $row['semestre_id'] = $row[$semestreField];
            }
            $modules[] = $row;
        }

        mysqli_close($conn);
        return $modules;
    } else {
        // If the affectation table doesn't exist, try using the seance table
        $checkSeanceTable = mysqli_query($conn, "SHOW TABLES LIKE 'seance'");

        if (mysqli_num_rows($checkSeanceTable) > 0) {
            // Verify column names in the tables
            $checkModuleColumns = mysqli_query($conn, "SHOW COLUMNS FROM module");
            $moduleColumns = [];
            while ($col = mysqli_fetch_assoc($checkModuleColumns)) {
                $moduleColumns[] = $col['Field'];
            }

            $checkSeanceColumns = mysqli_query($conn, "SHOW COLUMNS FROM seance");
            $seanceColumns = [];
            while ($col = mysqli_fetch_assoc($checkSeanceColumns)) {
                $seanceColumns[] = $col['Field'];
            }

            // Determine the correct field names
            $moduleIdField = in_array('id', $moduleColumns) ? 'id' : 'id_module';
            $seanceModuleIdField = in_array('id_module', $seanceColumns) ? 'id_module' : 'module_id';
            $seanceTeacherIdField = in_array('id_enseignant', $seanceColumns) ? 'id_enseignant' : 'enseignant_id';
            $seanceTypeField = in_array('type', $seanceColumns) ? 'type' : 'type_seance';

            // Determine the semester field name
            $semestreField = in_array('id_semestre', $moduleColumns) ? 'id_semestre' :
                            (in_array('semestre_id', $moduleColumns) ? 'semestre_id' :
                            (in_array('semestre', $moduleColumns) ? 'semestre' : null));

            $sql = "SELECT DISTINCT m.*, f.nom_filiere, n.nom as niveau, s.nom as semestre_nom
                    FROM module m
                    JOIN seance sc ON m.$moduleIdField = sc.$seanceModuleIdField
                    LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                    LEFT JOIN niveaux n ON m.id_niveau = n.id
                    LEFT JOIN semestre s ON m.$semestreField = s.id
                    WHERE sc.$seanceTeacherIdField = '$teacherId'
                    AND sc.$seanceTypeField = 'Cours'
                    ORDER BY f.nom_filiere, n.nom, s.nom, m.nom";

            error_log("Using seance table to get all modules for teacher ID: $teacherId with SQL: $sql");

            $result = mysqli_query($conn, $sql);

            if (!$result) {
                $error = mysqli_error($conn);
                error_log("Error executing query: " . $error);
                mysqli_close($conn);
                return ["error" => "Database query error: " . $error];
            }

            $modules = [];
            while ($row = mysqli_fetch_assoc($result)) {
                // Add additional information about the module's relationships
                if (isset($row[$semestreField])) {
                    $row['semestre_id'] = $row[$semestreField];
                }
                $modules[] = $row;
            }

            mysqli_close($conn);
            return $modules;
        } else {
            error_log("Neither affectation nor seance table found, cannot get modules for teacher");
            mysqli_close($conn);
            return ["error" => "Required tables not found"];
        }
    }
}
?>