<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);


header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, DELETE, PUT");
header("Access-Control-Allow-Headers: Content-Type");

require_once __DIR__ . '/../controller/demandesController.php';

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$action = $_GET['action'] ?? null;

if (!$action) {
    http_response_code(400);
    echo json_encode(['error' => 'Action parameter is required']);
    exit();
}

$data = json_decode(file_get_contents("php://input"), true);

handleDemand($action, $data);
?>