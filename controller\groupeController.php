<?php
require_once "../model/groupeModel.php";
require_once "../utils/response.php";

// Get groupe table structure
function getGroupeTableStructureAPI() {
    $structure = getGroupeTableStructure();

    if (isset($structure['error'])) {
        jsonResponse(['error' => $structure['error']], 404);
    }

    jsonResponse(['data' => $structure], 200);
}

// Get all groupes
function getAllGroupesAPI() {
    $groupes = getAllGroupes();

    if (isset($groupes['error'])) {
        jsonResponse(['error' => $groupes['error']], 404);
    }

    jsonResponse(['data' => $groupes], 200);
}

// Get groupes by filiere and niveau
function getGroupesByFiliereNiveauAPI($id_filiere, $id_niveau) {
    $groupes = getGroupesByFiliereNiveau($id_filiere, $id_niveau);

    if (isset($groupes['error'])) {
        jsonResponse(['error' => $groupes['error']], 404);
    }

    jsonResponse(['data' => $groupes], 200);
}

// Get groupes by niveau only
function getGroupesByNiveauAPI($id_niveau) {
    error_log("Getting groups for niveau: " . $id_niveau);
    $groupes = getGroupesByNiveau($id_niveau);

    if (isset($groupes['error'])) {
        error_log("Error getting groups: " . $groupes['error']);
        jsonResponse(['error' => $groupes['error']], 404);
    }

    error_log("Found " . count($groupes) . " groups for niveau: " . $id_niveau);
    jsonResponse(['data' => $groupes], 200);
}