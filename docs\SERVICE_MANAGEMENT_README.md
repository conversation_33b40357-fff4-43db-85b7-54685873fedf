# Service Management System

## Overview

The Service Management System provides administrators with comprehensive control over time-sensitive academic services. It allows for activation/deactivation of services with automatic expiration, user access control, and detailed logging.

## Features

### 🎯 Core Functionality
- **Time-based Service Control**: Activate services for specific durations (hours, days, or custom end dates)
- **Automatic Expiration**: Services automatically deactivate when their time limit expires
- **Access Control Middleware**: Prevents access to inactive services with user-friendly messages
- **Real-time Status Display**: Shows service status and remaining time to users
- **Comprehensive Logging**: All service actions are logged for audit and debugging

### 🛠 Admin Features
- **Intuitive Admin Interface**: Easy-to-use control panel for managing services
- **Service Configuration**: Set duration, schedule activation, and manage service settings
- **Status Monitoring**: Real-time view of all services with remaining time indicators
- **Activity Logs**: Detailed logs of all service state changes
- **Bulk Operations**: Check and deactivate expired services in bulk

### 🔧 Technical Features
- **RESTful API**: Complete API for service management operations
- **Database Integration**: Robust database schema with proper indexing
- **<PERSON>ron Job Support**: Automatic background processing for expired services
- **Responsive Design**: Mobile-friendly admin interface
- **Error Handling**: Comprehensive error handling and recovery

## System Architecture

### Database Schema

#### `service_management` Table
```sql
- id (Primary Key)
- service_name (Unique service identifier)
- service_key (API key for service)
- display_name (Human-readable name)
- description (Service description)
- is_active (Current status)
- start_time (Activation timestamp)
- end_time (Expiration timestamp)
- duration_hours (Duration in hours)
- auto_deactivate (Auto-deactivation flag)
- created_by (Creator identifier)
- activation_count (Number of activations)
- settings (JSON configuration)
- Timestamps and indexes
```

#### `service_logs` Table
```sql
- id (Primary Key)
- service_key (Service identifier)
- action (Action performed)
- performed_by (User who performed action)
- details (JSON action details)
- created_at (Timestamp)
```

### File Structure

```
├── model/
│   └── serviceManagementModel.php      # Database operations
├── controller/
│   └── serviceManagementController.php # API controllers
├── route/
│   └── serviceManagementRoute.php      # API routing
├── middleware/
│   └── serviceAccessMiddleware.php     # Access control
├── view/admin/
│   └── service-management.php          # Admin interface
├── cron/
│   └── check_expired_services.php      # Cron job script
└── docs/
    └── SERVICE_MANAGEMENT_README.md    # This file
```

## Default Services

The system comes pre-configured with these services:

1. **UE Preferences** (`ue_preferences`)
   - Allows teachers to submit teaching unit preferences
   - Integrates with existing UE preference workflow

2. **Grade Submission** (`grade_submission`)
   - Controls grade submission periods
   - Prevents late submissions when inactive

3. **Course Evaluation** (`course_evaluation`)
   - Manages student course evaluation periods
   - Ensures evaluation windows are properly timed

4. **Schedule Modification** (`schedule_modification`)
   - Controls when schedule changes are allowed
   - Prevents unauthorized schedule modifications

## Usage Guide

### For Administrators

#### Accessing the Service Management Interface
1. Log in as an administrator
2. Navigate to **Settings** → **Service Management**
3. View all services with their current status

#### Activating a Service
1. Find the service you want to activate
2. Click the **Activate** button
3. Choose activation method:
   - **Duration**: Set number of hours (e.g., 24 for one day)
   - **End Time**: Set specific end date and time
4. Click **Activate Service**

#### Monitoring Services
- **Active Services**: Show green status with remaining time
- **Inactive Services**: Show red status with last deactivation time
- **Expiring Soon**: Highlighted with warning colors

#### Viewing Logs
1. Click the journal icon next to any service
2. View detailed activity logs
3. See who performed actions and when

### For Developers

#### Adding New Services
1. Add service configuration to `ensureServiceManagementTable()` in `serviceManagementModel.php`
2. Create middleware function in `serviceAccessMiddleware.php`
3. Add service checks to relevant pages

#### API Usage

**Get All Services**
```javascript
GET /route/serviceManagementRoute.php
```

**Check Service Status**
```javascript
GET /route/serviceManagementRoute.php?action=status&service_key=ue_preferences
```

**Activate Service**
```javascript
POST /route/serviceManagementRoute.php?action=activate
Content-Type: application/json

{
    "service_key": "ue_preferences",
    "duration_hours": 24,
    "activated_by": "admin"
}
```

**Deactivate Service**
```javascript
POST /route/serviceManagementRoute.php?action=deactivate
Content-Type: application/json

{
    "service_key": "ue_preferences",
    "reason": "manual",
    "deactivated_by": "admin"
}
```

#### Middleware Integration

**Protecting Pages**
```php
require_once '../../middleware/serviceAccessMiddleware.php';

// Require UE preferences to be active
requireUEPreferencesAccess();

// Or check without redirecting
$accessStatus = checkUEPreferencesServiceAccess();
if (!$accessStatus['allowed']) {
    // Handle inactive service
}
```

**Displaying Service Status**
```php
// Show service status widget
echo renderServiceStatusWidget('ue_preferences', 'UE Preferences');

// Get service status for custom display
$status = getServiceStatusForUI('ue_preferences');
```

## Setup Instructions

### 1. Database Setup
The system automatically creates required tables on first use. No manual database setup required.

### 2. Cron Job Setup
Add this line to your crontab for automatic expiration checking:
```bash
* * * * * /usr/bin/php /path/to/project/cron/check_expired_services.php >> /var/log/service_cron.log 2>&1
```

### 3. Admin Access
Ensure admin users have access to the service management interface through the admin settings page.

### 4. Service Integration
Add service access checks to existing pages that should be controlled by the service management system.

## Configuration

### Service Settings
Each service can have custom JSON settings:
```json
{
    "notification_enabled": true,
    "reminder_hours": [24, 6, 1],
    "department_notification": true,
    "auto_lock": true
}
```

### Notification Configuration
Configure email notifications for service state changes by implementing the notification functions in the cron job script.

## Security Considerations

- **Admin Only**: Service management is restricted to admin users
- **Audit Logging**: All actions are logged with user identification
- **Input Validation**: All inputs are validated and sanitized
- **Transaction Safety**: Database operations use transactions for consistency

## Troubleshooting

### Common Issues

**Services Not Deactivating Automatically**
- Check if cron job is running
- Verify cron job has proper file permissions
- Check cron job logs for errors

**Access Denied Messages Not Showing**
- Ensure middleware is included in protected pages
- Check session management
- Verify service status in database

**Admin Interface Not Loading**
- Check admin authentication
- Verify database connection
- Check for JavaScript errors in browser console

### Debug Mode
Enable debug mode by setting error reporting in the admin interface:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Future Enhancements

- **Email Notifications**: Automatic email alerts for service state changes
- **Advanced Scheduling**: Recurring activation schedules
- **Department-specific Services**: Services that can be controlled per department
- **API Rate Limiting**: Prevent abuse of service management APIs
- **Service Dependencies**: Services that depend on other services
- **Mobile App Integration**: Mobile notifications for service status changes

## Support

For technical support or feature requests, contact the development team or create an issue in the project repository.
