<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include constants
require_once __DIR__ . '/../config/constants.php';

// Get error message from session if available
$errorMessage = isset($_SESSION['error_message']) ? $_SESSION['error_message'] : "Une erreur s'est produite.";

// Clear the error message from session
unset($_SESSION['error_message']);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erreur - UniAdmin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
        }
        
        .error-card {
            max-width: 600px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
        }
        
        .error-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #343a40;
        }
        
        .error-message {
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .back-button {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .back-button:hover {
            background-color: var(--primary-color-dark);
            border-color: var(--primary-color-dark);
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="card error-card p-4">
            <div class="card-body text-center">
                <div class="mb-4">
                    <i class="bi bi-exclamation-triangle-fill error-icon"></i>
                </div>
                
                <h1 class="error-title mb-3">Erreur</h1>
                
                <p class="error-message mb-4">
                    <?php echo htmlspecialchars($errorMessage); ?>
                </p>
                
                <div class="d-flex justify-content-center">
                    <a href="<?php echo BASE_URL; ?>/index.php" class="btn btn-primary back-button">
                        <i class="bi bi-house-door me-2"></i>Retour à l'accueil
                    </a>
                </div>
                
                <?php if (isset($_SESSION['user']) && isset($_SESSION['user']['role'])): ?>
                <div class="mt-4 text-center">
                    <p class="small text-muted">
                        Si vous pensez qu'il s'agit d'une erreur, veuillez contacter l'administrateur système.
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
