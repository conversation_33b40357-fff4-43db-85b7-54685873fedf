/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: #f9fafb;
  color: #111827;
  
  margin: 0;
}

/* Typography */
h2 {
  font-size: 1.875rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.card-header {
  padding: 1.5rem 1.5rem 0;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-content {
  padding: 1.5rem;
}

/* Triangle alert icon */
.triangle-alert {
  width: 1.25rem;
  height: 1.25rem;
  position: relative;
  color: #ef4444;
  display: inline-flex;
  animation: pulse 2s infinite ease-in-out;
}
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}
/* shake animation on new conflict */
.shake {
  animation: shake-triangle 0.4s;
}
@keyframes shake-triangle {
  0% { transform: translateX(0); }
  20% { transform: translateX(-4px); }
  40% { transform: translateX(4px); }
  60% { transform: translateX(-4px); }
  80% { transform: translateX(4px); }
  100% { transform: translateX(0); }
}

/* Filters */
.filter-icon {
  color: #3b82f6;
}

.filters-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .filters-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .filters-grid {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 500;
}

.filter-select {
  padding: 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  background-color: white;
  width: 100%;
  font-size: 0.875rem;
}

.button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.button {
  background-color: #3b82f6;
  color: white;
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}
.button:hover {
  background-color: #2563eb;
}