<?php
require_once "../controller/clubsController.php";
require_once "../utils/response.php";

// Set headers for API responses
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    // Get the club ID from the URL if provided
    $id_club = isset($_GET['id']) ? $_GET['id'] : null;
    $id_programme = isset($_GET['id_programme']) ? $_GET['id_programme'] : null;

    switch ($method) {
        case 'GET':
            if (isset($_GET['students'])) {
                // Get all students for dropdown selection
                getAllStudentsForDropdownAPI();
            } else if (isset($_GET['programme']) && $id_club) {
                // Get all programme items for a specific club
                getClubProgrammeAPI($id_club);
            } else if ($id_club) {
                // Get a specific club
                getClubByIdAPI($id_club);
            } else if (isset($_GET['paginated'])) {
                // Get paginated clubs
                getAllClubsPaginatedAPI();
            } else {
                // Get all clubs (no pagination)
                getAllClubsAPI();
            }
            break;

        case 'POST':
            if (isset($_GET['programme'])) {
                // Add a new programme item to a club
                addClubProgrammeAPI();
            } else {
                // Create a new club
                createClubAPI();
            }
            break;

        case 'PUT':
            if (!$id_club) {
                jsonResponse(['error' => 'Club ID is required'], 400);
            }
            // Update a club
            updateClubAPI($id_club);
            break;

        case 'DELETE':
            if (isset($_GET['programme']) && $id_programme) {
                // Delete a club programme item
                deleteClubProgrammeAPI($id_programme);
            } else if ($id_club) {
                // Delete a club
                deleteClubAPI($id_club);
            } else {
                jsonResponse(['error' => 'ID is required'], 400);
            }
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
            break;
    }
} catch (Exception $e) {
    jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
}