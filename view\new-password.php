<?php
// Inclure les constantes
require_once __DIR__ . "/../config/constants.php";
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouveau mot de passe - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <style>
        :root {
            --royal-blue: #1a73e8;
            --azure: #3a8ff7;
            --sky-blue: #64b5f6;
            --light-blue: #e8f0fe;
            --navy-blue: #0d47a1;
        }

        body {
            background-color: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .reset-container {
            max-width: 450px;
            width: 100%;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .card-header {
            background-color: var(--royal-blue);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 20px;
            text-align: center;
        }

        .card-body {
            padding: 30px;
        }

        .form-control {
            border-radius: 5px;
            padding: 12px 15px;
            border: 1px solid #ced4da;
            margin-bottom: 20px;
        }

        .btn-primary {
            background-color: var(--royal-blue);
            border-color: var(--royal-blue);
            border-radius: 5px;
            padding: 12px 15px;
            font-weight: 500;
            width: 100%;
        }

        .btn-primary:hover {
            background-color: var(--navy-blue);
            border-color: var(--navy-blue);
        }

        .alert {
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .logo {
            max-width: 100px;
            margin-bottom: 15px;
        }

        .steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 33%;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .step.active .step-number {
            background-color: var(--royal-blue);
            color: white;
        }

        .step-text {
            font-size: 12px;
            color: #6c757d;
            text-align: center;
        }

        .step.active .step-text {
            color: var(--royal-blue);
            font-weight: 500;
        }

        .step-line {
            flex-grow: 1;
            height: 2px;
            background-color: #e9ecef;
            margin: 15px 5px 0;
        }

        .back-to-login {
            text-align: center;
            margin-top: 20px;
        }

        .back-to-login a {
            color: var(--royal-blue);
            text-decoration: none;
        }

        .back-to-login a:hover {
            text-decoration: underline;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
        }

        .password-strength {
            height: 5px;
            margin-top: -15px;
            margin-bottom: 20px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .password-feedback {
            font-size: 12px;
            margin-top: -15px;
            margin-bottom: 20px;
        }

        #resetSuccess {
            display: none;
            text-align: center;
        }

        #resetSuccess i {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="card">
            <div class="card-header">
                <img src="<?php echo BASE_URL; ?>/view/assets/img/logo.png" alt="ENSAH Logo" class="logo">
                <h4>Nouveau mot de passe</h4>
            </div>
            <div class="card-body">
                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-text">Identification</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-text">Vérification</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step active">
                        <div class="step-number">3</div>
                        <div class="step-text">Nouveau mot de passe</div>
                    </div>
                </div>

                <div id="passwordForm">
                    <div class="alert alert-info" role="alert">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        Créez un nouveau mot de passe sécurisé pour votre compte.
                    </div>

                    <div class="alert alert-danger" id="errorAlert" style="display: none;" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <span id="errorMessage"></span>
                    </div>

                    <form id="newPasswordForm">
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                            <div class="password-container">
                                <input type="password" class="form-control" id="newPassword" name="newPassword" required>
                                <span class="password-toggle" onclick="togglePassword('newPassword')">
                                    <i class="bi bi-eye"></i>
                                </span>
                            </div>
                            <div class="password-strength" id="passwordStrength"></div>
                            <div class="password-feedback" id="passwordFeedback"></div>
                        </div>

                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                            <div class="password-container">
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                <span class="password-toggle" onclick="togglePassword('confirmPassword')">
                                    <i class="bi bi-eye"></i>
                                </span>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            Réinitialiser le mot de passe
                        </button>
                    </form>
                </div>

                <div id="resetSuccess">
                    <i class="bi bi-check-circle-fill"></i>
                    <h4>Mot de passe réinitialisé avec succès!</h4>
                    <p class="mb-4">Votre mot de passe a été mis à jour. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.</p>

                    <a href="<?php echo BASE_URL; ?>/index.php" class="btn btn-primary">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        Se connecter
                    </a>
                </div>

                <div class="back-to-login">
                    <a href="<?php echo BASE_URL; ?>/view/verify-code.php">
                        <i class="bi bi-arrow-left me-1"></i>
                        Retour à l'étape précédente
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Vérifier si le token est stocké
            const token = sessionStorage.getItem('resetToken');
            if (!token) {
                // Rediriger vers la page de demande de réinitialisation
                window.location.href = '<?php echo BASE_URL; ?>/view/reset-password.php';
                return;
            }

            const newPasswordForm = document.getElementById('newPasswordForm');
            const passwordForm = document.getElementById('passwordForm');
            const resetSuccess = document.getElementById('resetSuccess');
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            const passwordStrength = document.getElementById('passwordStrength');
            const passwordFeedback = document.getElementById('passwordFeedback');
            const newPasswordInput = document.getElementById('newPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');

            // Évaluer la force du mot de passe
            newPasswordInput.addEventListener('input', function() {
                const password = this.value;
                let strength = 0;
                let feedback = '';

                if (password.length >= 8) {
                    strength += 25;
                    feedback += '<span class="text-success">✓ Au moins 8 caractères</span><br>';
                } else {
                    feedback += '<span class="text-danger">✗ Au moins 8 caractères</span><br>';
                }

                if (/[A-Z]/.test(password)) {
                    strength += 25;
                    feedback += '<span class="text-success">✓ Au moins une majuscule</span><br>';
                } else {
                    feedback += '<span class="text-danger">✗ Au moins une majuscule</span><br>';
                }

                if (/[0-9]/.test(password)) {
                    strength += 25;
                    feedback += '<span class="text-success">✓ Au moins un chiffre</span><br>';
                } else {
                    feedback += '<span class="text-danger">✗ Au moins un chiffre</span><br>';
                }

                if (/[^A-Za-z0-9]/.test(password)) {
                    strength += 25;
                    feedback += '<span class="text-success">✓ Au moins un caractère spécial</span>';
                } else {
                    feedback += '<span class="text-danger">✗ Au moins un caractère spécial</span>';
                }

                // Définir la couleur en fonction de la force
                let color = '';
                if (strength <= 25) {
                    color = '#dc3545'; // Rouge
                } else if (strength <= 50) {
                    color = '#ffc107'; // Jaune
                } else if (strength <= 75) {
                    color = '#fd7e14'; // Orange
                } else {
                    color = '#28a745'; // Vert
                }

                passwordStrength.style.width = strength + '%';
                passwordStrength.style.backgroundColor = color;
                passwordFeedback.innerHTML = feedback;
            });

            // Gérer la soumission du formulaire
            newPasswordForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                // Validation des mots de passe
                if (newPassword.length < 6) {
                    showError('Le mot de passe doit contenir au moins 6 caractères.');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    showError('Les mots de passe ne correspondent pas.');
                    return;
                }

                // Désactiver le bouton pendant la requête
                const submitBtn = newPasswordForm.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Réinitialisation en cours...';

                // Masquer les erreurs précédentes
                errorAlert.style.display = 'none';

                // Envoyer la requête
                fetch('<?php echo BASE_URL; ?>/route/reset-password-index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'reset',
                        token: token,
                        newPassword: newPassword,
                        confirmPassword: confirmPassword
                    })
                })
                .then(response => response.json())
                .then(data => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-check-circle me-2"></i>Réinitialiser le mot de passe';

                    if (data.success) {
                        // Nettoyer sessionStorage
                        sessionStorage.removeItem('resetIdentifier');
                        sessionStorage.removeItem('resetToken');

                        // Afficher le message de succès
                        passwordForm.style.display = 'none';
                        resetSuccess.style.display = 'block';
                    } else {
                        showError(data.error);
                    }
                })
                .catch(error => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-check-circle me-2"></i>Réinitialiser le mot de passe';
                    showError('Une erreur est survenue. Veuillez réessayer plus tard.');
                    console.error('Error:', error);
                });
            });

            function showError(message) {
                errorMessage.textContent = message;
                errorAlert.style.display = 'block';
            }
        });

        // Fonction pour afficher/masquer le mot de passe
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        }
    </script>
</body>
</html>
