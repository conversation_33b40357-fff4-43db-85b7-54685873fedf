<?php
/**
 * <PERSON>chier de vérification d'authentification pour les coordinateurs
 *
 * Ce fichier vérifie si l'utilisateur est authentifié et a le rôle de coordinateur.
 * Il doit être inclus au début de chaque page réservée aux coordinateurs.
 */

// Inclure le fichier de constantes
require_once __DIR__ . '/../../config/constants.php';

// Inclure les fonctions d'authentification
require_once __DIR__ . '/../../utils/auth.php';

// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérifier si l'utilisateur est connecté
if (!isLoggedIn()) {
    // Sauvegarder l'URL actuelle pour redirection après connexion
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];

    // Rediriger vers la page de connexion
    header('Location: ' . BASE_URL . '/index.php');
    exit;
}

// Vérifier l'expiration de la session (30 minutes)
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
    // Détruire la session
    session_destroy();

    // Rediriger vers la page de connexion
    header('Location: ' . BASE_URL . '/index.php?expired=1');
    exit;
}

// Vérifier si l'IP a changé
if (isset($_SESSION['ip']) && $_SESSION['ip'] !== $_SERVER['REMOTE_ADDR']) {
    // Journaliser l'incident pour le débogage avec des informations détaillées
    error_log("IP mismatch for coordinator: Username: " . ($_SESSION['user']['username'] ?? 'unknown') .
              ", Role: " . ($_SESSION['user']['role'] ?? 'unknown') .
              ", Session IP: " . $_SESSION['ip'] .
              ", Current IP: " . $_SERVER['REMOTE_ADDR'] .
              ", User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'unknown'));

    // Vérifier si le changement d'IP est suspect (par exemple, changement de pays)
    // Dans un environnement de production, vous pourriez ajouter ici une vérification géographique
    // Pour l'instant, nous mettons simplement à jour l'IP

    // Mettre à jour l'IP dans la session
    $_SESSION['ip'] = $_SERVER['REMOTE_ADDR'];

    // Enregistrer le changement d'IP dans la session pour référence
    if (!isset($_SESSION['ip_changes'])) {
        $_SESSION['ip_changes'] = [];
    }
    $_SESSION['ip_changes'][] = [
        'old_ip' => $_SESSION['ip'],
        'new_ip' => $_SERVER['REMOTE_ADDR'],
        'timestamp' => time()
    ];

    // Limiter le nombre de changements d'IP autorisés (par exemple, max 5)
    if (count($_SESSION['ip_changes']) > 5) {
        // Si trop de changements, journaliser mais ne pas déconnecter pour les coordinateurs
        error_log("Multiple IP changes detected for coordinator: " . ($_SESSION['user']['username'] ?? 'unknown'));
    }
}

// Mettre à jour le timestamp de dernière activité
$_SESSION['last_activity'] = time();

// Fonction pour vérifier si l'utilisateur est un coordinateur
if (!function_exists('isCoordinator')) {
    function isCoordinator() {
        return isset($_SESSION['user']['role']) && $_SESSION['user']['role'] === 'coordinateur';
    }
}

// Vérifier si l'utilisateur est un coordinateur ou un administrateur
if (!isCoordinator() && !isAdmin()) {
    // Rediriger vers la page d'erreur
    header('Location: ' . BASE_URL . '/view/unauthorized.php');
    exit;
}

// Si l'utilisateur est un coordinateur, vérifier que son filiere_id est disponible
if (isCoordinator()) {
    try {
        // Journaliser l'accès
        error_log("Coordinator access check for user: " . ($_SESSION['user']['username'] ?? 'unknown'));

        // Inclure le modèle d'authentification pour accéder à la fonction getCoordinatorFiliere
        require_once __DIR__ . '/../../model/authModel.php';

        // Inclure la configuration de la base de données
        require_once __DIR__ . '/../../config/db.php';

        // Vérifier si le filiere_id est déjà dans la session
        if (!isset($_SESSION['user']['filiere_id']) || $_SESSION['user']['filiere_id'] == 0) {
            error_log("filiere_id not found or is 0 in session for coordinator: " . ($_SESSION['user']['username'] ?? 'unknown'));

            // Récupérer le filiere_id
            $username = $_SESSION['user']['username'] ?? '';
            if (empty($username)) {
                error_log("Empty username for coordinator in session");
                // Définir une valeur par défaut pour éviter les erreurs
                $_SESSION['user']['filiere_id'] = 1; // Utiliser 1 comme valeur par défaut au lieu de 0
                $_SESSION['user']['filiere_name'] = 'Non spécifié';
            } else {
                // Essayer de récupérer l'ID de filière
                $filiereId = getCoordinatorFiliere($username);

                // Si aucun filiere_id n'est trouvé, essayer de trouver une filière par défaut
                if (!$filiereId) {
                    error_log("No filiere_id found for coordinator: $username. Trying to find a default filiere.");

                    $conn = getConnection();
                    if ($conn) {
                        // Récupérer la première filière disponible
                        $defaultFiliereQuery = mysqli_query($conn, "SELECT id_filiere, nom_filiere FROM filiere LIMIT 1");
                        if ($defaultFiliereQuery && mysqli_num_rows($defaultFiliereQuery) > 0) {
                            $defaultFiliere = mysqli_fetch_assoc($defaultFiliereQuery);
                            $filiereId = $defaultFiliere['id_filiere'];
                            $filiereName = $defaultFiliere['nom_filiere'];

                            error_log("Using default filiere ID: $filiereId, Name: $filiereName for coordinator: $username");

                            // Stocker les informations dans la session
                            $_SESSION['user']['filiere_id'] = $filiereId;
                            $_SESSION['user']['filiere_name'] = $filiereName;

                            // Afficher un message d'avertissement à l'utilisateur
                            $_SESSION['warning_message'] = "Vous avez été associé temporairement à la filière '$filiereName'. Veuillez contacter l'administrateur pour une association permanente.";

                            // Optionnel: Associer automatiquement le coordinateur à cette filière
                            // Cette partie est commentée car elle modifie la base de données
                            /*
                            // Récupérer l'ID de l'enseignant
                            $enseignantQuery = mysqli_query($conn, "SELECT id_enseignant FROM enseignant WHERE CNI = '" . mysqli_real_escape_string($conn, $username) . "'");
                            if ($enseignantQuery && mysqli_num_rows($enseignantQuery) > 0) {
                                $enseignantRow = mysqli_fetch_assoc($enseignantQuery);
                                $enseignantId = $enseignantRow['id_enseignant'];

                                // Trouver la colonne appropriée dans la table filiere
                                $possibleColumns = ['id_chef_filiere', 'chef_filiere', 'id_coordinateur', 'coordinateur_id'];
                                $columnToUse = null;

                                foreach ($possibleColumns as $column) {
                                    $checkColumn = mysqli_query($conn, "SHOW COLUMNS FROM filiere LIKE '$column'");
                                    if ($checkColumn && mysqli_num_rows($checkColumn) > 0) {
                                        $columnToUse = $column;
                                        break;
                                    }
                                }

                                if ($columnToUse) {
                                    $updateQuery = "UPDATE filiere SET $columnToUse = '$enseignantId' WHERE id_filiere = '$filiereId'";
                                    mysqli_query($conn, $updateQuery);
                                    error_log("Updated filiere $filiereId to associate with coordinator $username (enseignant ID: $enseignantId)");
                                }
                            }
                            */
                        } else {
                            // Si aucune filière n'est trouvée, définir des valeurs par défaut
                            $_SESSION['user']['filiere_id'] = 1;
                            $_SESSION['user']['filiere_name'] = 'Non spécifié';
                            $_SESSION['warning_message'] = "Aucune filière n'a été trouvée dans le système. Veuillez contacter l'administrateur.";
                            error_log("No filiere found in the database for coordinator: $username");
                        }

                        mysqli_close($conn);
                    } else {
                        // Si la connexion à la base de données échoue, définir des valeurs par défaut
                        $_SESSION['user']['filiere_id'] = 1;
                        $_SESSION['user']['filiere_name'] = 'Non spécifié';
                        error_log("Database connection failed when trying to find a default filiere for coordinator: $username");
                    }
                } else {
                    // Stocker le filiere_id dans la session
                    $_SESSION['user']['filiere_id'] = $filiereId;

                    // Récupérer le nom de la filière
                    $conn = getConnection();
                    if ($conn) {
                        $sql = "SELECT nom_filiere FROM filiere WHERE id_filiere = $filiereId";
                        $result = mysqli_query($conn, $sql);
                        if ($result && mysqli_num_rows($result) > 0) {
                            $row = mysqli_fetch_assoc($result);
                            $_SESSION['user']['filiere_name'] = $row['nom_filiere'];
                            error_log("Found filiere name: " . $_SESSION['user']['filiere_name'] . " for ID: $filiereId");
                        } else {
                            $_SESSION['user']['filiere_name'] = 'Non spécifié';
                            error_log("No filiere name found for ID: $filiereId");
                        }
                        mysqli_close($conn);
                    } else {
                        $_SESSION['user']['filiere_name'] = 'Non spécifié';
                        error_log("Database connection failed when trying to get filiere name for ID: $filiereId");
                    }
                }
            }
        } else {
            // Si le filiere_id est déjà dans la session mais que le nom de la filière n'est pas défini
            if (!isset($_SESSION['user']['filiere_name']) || $_SESSION['user']['filiere_name'] == 'Non spécifié') {
                $filiereId = $_SESSION['user']['filiere_id'];
                $conn = getConnection();

                if ($conn) {
                    $sql = "SELECT nom_filiere FROM filiere WHERE id_filiere = $filiereId";
                    $result = mysqli_query($conn, $sql);

                    if ($result && mysqli_num_rows($result) > 0) {
                        $row = mysqli_fetch_assoc($result);
                        $_SESSION['user']['filiere_name'] = $row['nom_filiere'];
                        error_log("Updated filiere name: " . $_SESSION['user']['filiere_name'] . " for ID: $filiereId");
                    }

                    mysqli_close($conn);
                }
            }

            error_log("filiere_id already in session for coordinator: " . ($_SESSION['user']['username'] ?? 'unknown') .
                      ", ID: " . $_SESSION['user']['filiere_id'] .
                      ", Name: " . ($_SESSION['user']['filiere_name'] ?? 'Not set'));
        }
    } catch (Exception $e) {
        error_log("Exception in coordinator auth check: " . $e->getMessage() . " for user: " . ($_SESSION['user']['username'] ?? 'unknown'));
        // Ne pas rediriger, simplement définir des valeurs par défaut
        if (!isset($_SESSION['user']['filiere_id']) || $_SESSION['user']['filiere_id'] == 0) {
            $_SESSION['user']['filiere_id'] = 1;
        }
        if (!isset($_SESSION['user']['filiere_name']) || empty($_SESSION['user']['filiere_name'])) {
            $_SESSION['user']['filiere_name'] = 'Non spécifié';
        }
    }
}
?>
