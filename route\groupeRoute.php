<?php
require_once "../controller/groupeController.php";

// Activer CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Si la méthode est OPTIONS, terminer la requête ici
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Router pour les groupes
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (isset($_GET['structure']) && $_GET['structure'] === 'true') {
        getGroupeTableStructureAPI();
    } else if (isset($_GET['id_filiere']) && isset($_GET['id_niveau'])) {
        getGroupesByFiliereNiveauAPI($_GET['id_filiere'], $_GET['id_niveau']);
    } else if (isset($_GET['id_niveau'])) {
        getGroupesByNiveauAPI($_GET['id_niveau']);
    } else {
        getAllGroupesAPI();
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Méthode non autorisée']);
}