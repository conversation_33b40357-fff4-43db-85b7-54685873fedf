document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const clubsContainer = document.getElementById('clubsContainer');
    const clubModal = new bootstrap.Modal(document.getElementById('clubModal'));
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const programmeModal = new bootstrap.Modal(document.getElementById('programmeModal'));
    const addProgrammeModal = new bootstrap.Modal(document.getElementById('addProgrammeModal'));
    const deleteProgrammeModal = new bootstrap.Modal(document.getElementById('deleteProgrammeModal'));
    const clubForm = document.getElementById('clubForm');
    const programmeForm = document.getElementById('programmeForm');
    const clubModalLabel = document.getElementById('clubModalLabel');
    const saveClubBtn = document.getElementById('saveClubBtn');
    const saveProgrammeBtn = document.getElementById('saveProgrammeBtn');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const confirmDeleteProgrammeBtn = document.getElementById('confirmDeleteProgrammeBtn');

    // Form elements - Club
    const clubId = document.getElementById('clubId');
    const clubName = document.getElementById('clubName');
    const clubDescription = document.getElementById('clubDescription');
    const clubCreationDate = document.getElementById('clubCreationDate');
    const clubLogo = document.getElementById('clubLogo');
    const clubStatus = document.getElementById('clubStatus');
    const clubPresident = document.getElementById('clubPresident');
    const clubVicePresident = document.getElementById('clubVicePresident');
    const clubSecretary = document.getElementById('clubSecretary');
    const clubTreasurer = document.getElementById('clubTreasurer');



    // Form elements - Programme
    const programmeClubId = document.getElementById('programmeClubId');
    const programmeTitle = document.getElementById('programmeTitle');
    const programmeDescription = document.getElementById('programmeDescription');
    const programmeType = document.getElementById('programmeType');
    const programmeStartDate = document.getElementById('programmeStartDate');
    const programmeEndDate = document.getElementById('programmeEndDate');
    const programmeLocation = document.getElementById('programmeLocation');
    const programmeStatus = document.getElementById('programmeStatus');

    // Programme table container
    const programmeTableContainer = document.getElementById('programmeTableContainer');
    const clubProgrammeTitle = document.getElementById('clubProgrammeTitle');

    // Set today's date as default for creation date
    const today = new Date().toISOString().split('T')[0];
    clubCreationDate.value = today;

    // Variables for pagination
    let currentPage = 1;
    const perPage = 3; // Réduit à 3 clubs par page pour rendre la pagination visible
    let totalPages = 1;

    // Load clubs on page load with pagination
    loadClubs(1);

    // Load students for dropdowns
    loadStudents();

    // Event listeners - Club
    document.getElementById('addClubBtn').addEventListener('click', function() {
        resetForm();
        clubModalLabel.textContent = 'Ajouter un Club';
        clubModal.show();
    });

    saveClubBtn.addEventListener('click', saveClub);
    confirmDeleteBtn.addEventListener('click', deleteClub);



    // Event listeners - Programme
    document.getElementById('addProgrammeBtn').addEventListener('click', function() {
        resetProgrammeForm();
        addProgrammeModal.show();
    });

    saveProgrammeBtn.addEventListener('click', saveProgramme);
    confirmDeleteProgrammeBtn.addEventListener('click', deleteProgramme);

    // Functions
    function loadClubs(page = 1) {
        currentPage = page;

        // Show loading state
        clubsContainer.innerHTML = `
            <div class="col-12 text-center py-5">
                <div class="spinner-border" role="status" style="color: #80DEEA;">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-3" style="color: #00838F; font-weight: 500;">
                    <i class="bi bi-arrow-repeat me-2"></i>Chargement des clubs...
                </p>
            </div>
        `;

        fetch(`../../route/clubsRoute.php?paginated=true&page=${page}&per_page=${perPage}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    displayClubs(data.data);
                    totalPages = data.pagination.totalPages;
                    displayPagination(data.pagination);
                } else {
                    showError('Une erreur est survenue lors du chargement des clubs.');
                    console.error('Error:', data.error);
                }
            })
            .catch(error => {
                showError('Une erreur est survenue lors du chargement des clubs.');
                console.error('Error:', error);
            });
    }

    function displayPagination(pagination) {
        const paginationContainer = document.getElementById('paginationContainer');

        if (!paginationContainer) return;

        if (pagination.totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHtml = `
            <nav aria-label="Navigation des pages">
                <ul class="pagination justify-content-center">
                    <li class="page-item ${pagination.currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${pagination.currentPage - 1}" aria-label="Précédent">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
        `;

        // Display page numbers
        for (let i = 1; i <= pagination.totalPages; i++) {
            paginationHtml += `
                <li class="page-item ${i === pagination.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }

        paginationHtml += `
                    <li class="page-item ${pagination.currentPage === pagination.totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${pagination.currentPage + 1}" aria-label="Suivant">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        `;

        paginationContainer.innerHTML = paginationHtml;

        // Add event listeners to pagination links
        document.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = parseInt(this.getAttribute('data-page'));
                loadClubs(page);
            });
        });
    }

    // Variable to store all students
    let allStudents = [];

    function loadStudents() {
        fetch('../../route/clubsRoute.php?students=true')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    allStudents = data.data;
                    setupStudentSearchFields();
                } else {
                    console.error('Error loading students:', data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
    }

    function setupStudentSearchFields() {
        // Define the search fields and their corresponding hidden inputs and result containers
        const searchFields = [
            {
                search: document.getElementById('clubPresidentSearch'),
                hidden: document.getElementById('clubPresident'),
                results: document.getElementById('clubPresidentResults'),
                selected: document.getElementById('clubPresidentSelected')
            },
            {
                search: document.getElementById('clubVicePresidentSearch'),
                hidden: document.getElementById('clubVicePresident'),
                results: document.getElementById('clubVicePresidentResults'),
                selected: document.getElementById('clubVicePresidentSelected')
            },
            {
                search: document.getElementById('clubSecretarySearch'),
                hidden: document.getElementById('clubSecretary'),
                results: document.getElementById('clubSecretaryResults'),
                selected: document.getElementById('clubSecretarySelected')
            },
            {
                search: document.getElementById('clubTreasurerSearch'),
                hidden: document.getElementById('clubTreasurer'),
                results: document.getElementById('clubTreasurerResults'),
                selected: document.getElementById('clubTreasurerSelected')
            }
        ];

        // Setup event listeners for each search field
        searchFields.forEach(field => {
            if (!field.search) return;

            // Input event for search
            field.search.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();

                if (searchTerm.length < 2) {
                    field.results.classList.remove('active');
                    field.results.innerHTML = '';
                    return;
                }

                // Filter students based on search term
                const filteredStudents = allStudents.filter(student => {
                    const fullName = `${student.nom} ${student.prenom}`.toLowerCase();
                    return fullName.includes(searchTerm);
                }).slice(0, 10); // Limit to 10 results

                // Display results
                field.results.innerHTML = '';

                if (filteredStudents.length === 0) {
                    field.results.innerHTML = '<div class="student-result-item">Aucun étudiant trouvé</div>';
                } else {
                    filteredStudents.forEach(student => {
                        const resultItem = document.createElement('div');
                        resultItem.className = 'student-result-item';
                        resultItem.textContent = `${student.nom} ${student.prenom}`;
                        resultItem.dataset.cne = student.CNE;
                        resultItem.dataset.name = `${student.nom} ${student.prenom}`;

                        resultItem.addEventListener('click', function() {
                            selectStudent(field, this.dataset.cne, this.dataset.name);
                        });

                        field.results.appendChild(resultItem);
                    });
                }

                field.results.classList.add('active');
            });

            // Focus event to show results if there's a search term
            field.search.addEventListener('focus', function() {
                if (this.value.trim().length >= 2) {
                    field.results.classList.add('active');
                }
            });

            // Click outside to close results
            document.addEventListener('click', function(e) {
                if (!field.search.contains(e.target) && !field.results.contains(e.target)) {
                    field.results.classList.remove('active');
                }
            });
        });
    }

    function selectStudent(field, cne, name) {
        // Set the hidden input value
        field.hidden.value = cne;

        // Clear the search field
        field.search.value = '';

        // Hide the results
        field.results.classList.remove('active');

        // Show the selected student
        field.selected.innerHTML = `
            <span>${name}</span>
            <span class="remove-student" title="Supprimer"><i class="bi bi-x-circle"></i></span>
        `;
        field.selected.classList.add('active');

        // Add event listener to remove button
        const removeBtn = field.selected.querySelector('.remove-student');
        removeBtn.addEventListener('click', function() {
            field.hidden.value = '';
            field.selected.innerHTML = '';
            field.selected.classList.remove('active');
        });
    }

    function populateForm(club) {
        clubId.value = club.id_club;
        clubName.value = club.nom;
        clubDescription.value = club.description;
        clubCreationDate.value = club.date_creation;
        clubLogo.value = club.logo || '';
        clubStatus.value = club.statut;

        // Clear all selected students
        document.querySelectorAll('.selected-student').forEach(el => {
            el.innerHTML = '';
            el.classList.remove('active');
        });

        // Set hidden inputs
        document.getElementById('clubPresident').value = club.president_id || '';
        document.getElementById('clubVicePresident').value = club.vice_president_id || '';
        document.getElementById('clubSecretary').value = club.secretary_id || '';
        document.getElementById('clubTreasurer').value = club.treasurer_id || '';

        // Display selected students if they exist
        if (club.president_id && club.president_nom) {
            const field = {
                hidden: document.getElementById('clubPresident'),
                selected: document.getElementById('clubPresidentSelected')
            };
            selectStudent(field, club.president_id, `${club.president_nom} ${club.president_prenom}`);
        }

        if (club.vice_president_id && club.vice_president_nom) {
            const field = {
                hidden: document.getElementById('clubVicePresident'),
                selected: document.getElementById('clubVicePresidentSelected')
            };
            selectStudent(field, club.vice_president_id, `${club.vice_president_nom} ${club.vice_president_prenom}`);
        }

        if (club.secretary_id && club.secretary_nom) {
            const field = {
                hidden: document.getElementById('clubSecretary'),
                selected: document.getElementById('clubSecretarySelected')
            };
            selectStudent(field, club.secretary_id, `${club.secretary_nom} ${club.secretary_prenom}`);
        }

        if (club.treasurer_id && club.treasurer_nom) {
            const field = {
                hidden: document.getElementById('clubTreasurer'),
                selected: document.getElementById('clubTreasurerSelected')
            };
            selectStudent(field, club.treasurer_id, `${club.treasurer_nom} ${club.treasurer_prenom}`);
        }
    }

    function displayClubs(clubs) {
        clubsContainer.innerHTML = '';

        if (clubs.length === 0) {
            clubsContainer.innerHTML = `
                <div class="col-12">
                    <div class="empty-state">
                        <i class="bi bi-people"></i>
                        <p>Aucun club n'a été trouvé. Créez votre premier club pour commencer!</p>
                        <button class="btn" onclick="document.getElementById('addClubBtn').click()">
                            <i class="bi bi-plus-circle me-2"></i> Ajouter un Club
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        clubs.forEach(club => {
            const clubCard = document.createElement('div');
            clubCard.className = 'col-12 mb-4';

            // Create logo HTML
            let logoHtml = '';
            if (club.logo) {
                logoHtml = `<img src="${club.logo}" alt="${club.nom}">`;
            } else {
                // Use first letter of club name as placeholder with a nice background color
                const colors = ['#FFD3E0', '#D4F0F0', '#FFECB3', '#E1BEE7', '#C8E6C9'];
                const colorIndex = club.id_club % colors.length;
                const bgColor = colors[colorIndex];
                logoHtml = `<div style="background-color: ${bgColor}; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">${club.nom.charAt(0).toUpperCase()}</div>`;
            }

            // Create status badge
            const statusClass = club.statut === 'actif' ? 'status-active' : 'status-inactive';
            const statusText = club.statut === 'actif' ? 'Actif' : 'Inactif';

            // Format creation date
            const creationDate = new Date(club.date_creation).toLocaleDateString('fr-FR');

            // Create member list
            let membersList = '';

            if (club.president_nom) {
                membersList += `
                    <li>
                        <span class="member-role"><i class="bi bi-person-fill me-1"></i>Président:</span>
                        <span class="member-name">${club.president_nom} ${club.president_prenom}</span>
                    </li>
                `;
            }

            if (club.vice_president_nom) {
                membersList += `
                    <li>
                        <span class="member-role"><i class="bi bi-person me-1"></i>Vice-Président:</span>
                        <span class="member-name">${club.vice_president_nom} ${club.vice_president_prenom}</span>
                    </li>
                `;
            }

            if (club.secretary_nom) {
                membersList += `
                    <li>
                        <span class="member-role"><i class="bi bi-pencil me-1"></i>Secrétaire:</span>
                        <span class="member-name">${club.secretary_nom} ${club.secretary_prenom}</span>
                    </li>
                `;
            }

            if (club.treasurer_nom) {
                membersList += `
                    <li>
                        <span class="member-role"><i class="bi bi-cash-coin me-1"></i>Trésorier:</span>
                        <span class="member-name">${club.treasurer_nom} ${club.treasurer_prenom}</span>
                    </li>
                `;
            }

            if (!membersList) {
                membersList = '<li><span class="text-muted">Aucun membre assigné</span></li>';
            }

            clubCard.innerHTML = `
                <div class="club-card">
                    <div class="club-header">
                        <div class="club-logo">${logoHtml}</div>
                        <div class="club-title">
                            <h3>${club.nom}</h3>
                            <span class="club-status ${statusClass}">
                                <i class="bi ${club.statut === 'actif' ? 'bi-check-circle' : 'bi-x-circle'}"></i>
                                ${statusText}
                            </span>
                        </div>
                    </div>
                    <div class="club-body">
                        <div class="club-description">
                            <i class="bi bi-info-circle me-2"></i>${club.description}
                        </div>
                        <div class="club-info">
                            <p>
                                <i class="bi bi-calendar3 me-2"></i>
                                <strong>Date de création:</strong> ${creationDate}
                            </p>
                        </div>
                        <div class="club-members">
                            <h4><i class="bi bi-people me-2"></i>Bureau du Club</h4>
                            <ul class="member-list">
                                ${membersList}
                            </ul>
                        </div>
                    </div>
                    <div class="club-actions">
                        <button class="btn btn-outline-success view-programme-btn" data-id="${club.id_club}" data-name="${club.nom}">
                            <i class="bi bi-list-check"></i> Voir le Programme
                        </button>
                        <button class="btn btn-outline-primary edit-club-btn" data-id="${club.id_club}">
                            <i class="bi bi-pencil"></i> Modifier
                        </button>
                        <button class="btn btn-outline-danger delete-club-btn" data-id="${club.id_club}" data-name="${club.nom}">
                            <i class="bi bi-trash"></i> Supprimer
                        </button>
                    </div>
                </div>
            `;

            clubsContainer.appendChild(clubCard);
        });

        // Add event listeners to buttons
        document.querySelectorAll('.edit-club-btn').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                editClub(id);
            });
        });

        document.querySelectorAll('.delete-club-btn').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                showDeleteConfirmation(id, name);
            });
        });



        document.querySelectorAll('.view-programme-btn').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                viewClubProgramme(id, name);
            });
        });
    }

    function editClub(id) {
        fetch(`../../route/clubsRoute.php?id=${id}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    populateForm(data.data);
                    clubModalLabel.textContent = 'Modifier le Club';
                    clubModal.show();
                } else {
                    console.error('Error:', data.error);
                    alert('Une erreur est survenue lors du chargement des données du club.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue lors du chargement des données du club.');
            });
    }

    function populateForm(club) {
        clubId.value = club.id_club;
        clubName.value = club.nom;
        clubDescription.value = club.description;
        clubCreationDate.value = club.date_creation;
        clubLogo.value = club.logo || '';
        clubStatus.value = club.statut;
        clubPresident.value = club.president_id || '';
        clubVicePresident.value = club.vice_president_id || '';
        clubSecretary.value = club.secretary_id || '';
        clubTreasurer.value = club.treasurer_id || '';
    }

    function resetForm() {
        clubId.value = '';
        clubForm.reset();
        clubCreationDate.value = today;

        // Clear all selected students
        document.querySelectorAll('.selected-student').forEach(el => {
            el.innerHTML = '';
            el.classList.remove('active');
        });

        // Clear hidden inputs
        document.getElementById('clubPresident').value = '';
        document.getElementById('clubVicePresident').value = '';
        document.getElementById('clubSecretary').value = '';
        document.getElementById('clubTreasurer').value = '';
    }

    function saveClub() {
        // Validate form
        if (!clubForm.checkValidity()) {
            clubForm.reportValidity();
            return;
        }

        // Prepare data
        const data = {
            nom: clubName.value,
            description: clubDescription.value,
            date_creation: clubCreationDate.value,
            logo: clubLogo.value,
            statut: clubStatus.value,
            president_id: clubPresident.value || null,
            vice_president_id: clubVicePresident.value || null,
            secretary_id: clubSecretary.value || null,
            treasurer_id: clubTreasurer.value || null
        };

        const isEdit = clubId.value !== '';
        const url = isEdit ? `../../route/clubsRoute.php?id=${clubId.value}` : '../../route/clubsRoute.php';
        const method = isEdit ? 'PUT' : 'POST';

        fetch(url, {
            method:method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    clubModal.hide();
                    loadClubs(currentPage);
                    alert(isEdit ? 'Club modifié avec succès!' : 'Club créé avec succès!');
                } else {
                    console.error('Error:', data.error);
                    alert(`Une erreur est survenue: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue lors de l\'enregistrement du club.');
            });
    }

    function showDeleteConfirmation(id, name) {
        document.getElementById('deleteClubName').textContent = name;
        confirmDeleteBtn.setAttribute('data-id', id);
        deleteModal.show();
    }

    function deleteClub() {
        const id = confirmDeleteBtn.getAttribute('data-id');

        fetch(`../../route/clubsRoute.php?id=${id}`, {
            method: 'DELETE'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    deleteModal.hide();
                    loadClubs(1); // Retour à la première page après suppression
                    alert('Club supprimé avec succès!');
                } else {
                    console.error('Error:', data.error);
                    alert(`Une erreur est survenue: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue lors de la suppression du club.');
            });
    }

    function showError(message) {
        clubsContainer.innerHTML = `
            <div class="col-12 text-center py-5">
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    ${message}
                </div>
                <button class="btn btn-primary mt-3" onclick="loadClubs()">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    Réessayer
                </button>
            </div>
        `;
    }



    // Club Programme Functions
    function viewClubProgramme(id, name) {
        // Set club name in modal title
        clubProgrammeTitle.textContent = `Programme annuel du club "${name}"`;

        // Store club ID for adding new programme items
        programmeClubId.value = id;

        // Load programme for this club
        loadClubProgramme(id);

        // Show the programme modal
        programmeModal.show();
    }

    function loadClubProgramme(clubId) {
        // Show loading spinner
        programmeTableContainer.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border" role="status" style="color: #80DEEA;">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-3" style="color: #00838F; font-weight: 500;">
                    <i class="bi bi-arrow-repeat me-2"></i>Chargement du programme...
                </p>
            </div>
        `;

        // Fetch programme from API
        fetch(`../../route/clubsRoute.php?id=${clubId}&programme=true`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    displayProgramme(data.data);
                } else {
                    showProgrammeError('Une erreur est survenue lors du chargement du programme.');
                    console.error('Error:', data.error);
                }
            })
            .catch(error => {
                showProgrammeError('Une erreur est survenue lors du chargement du programme.');
                console.error('Error:', error);
            });
    }

    function displayProgramme(programmes) {
        if (programmes.length === 0) {
            programmeTableContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    Aucune activité n'a été trouvée dans le programme de ce club.
                </div>
            `;
            return;
        }

        // Create table to display programme
        let tableHtml = `
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Titre</th>
                        <th>Type</th>
                        <th>Date de début</th>
                        <th>Date de fin</th>
                        <th>Lieu</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
        `;

        programmes.forEach(programme => {
            // Format dates
            const startDate = new Date(programme.date_debut).toLocaleString('fr-FR');
            const endDate = new Date(programme.date_fin).toLocaleString('fr-FR');

            // Format type
            let typeLabel = '';
            let typeIcon = '';
            switch(programme.type) {
                case 'evenement':
                    typeLabel = 'Événement';
                    typeIcon = 'bi-calendar-event';
                    break;
                case 'sortie':
                    typeLabel = 'Sortie';
                    typeIcon = 'bi-geo-alt';
                    break;
                case 'formation':
                    typeLabel = 'Formation';
                    typeIcon = 'bi-book';
                    break;
                case 'reunion':
                    typeLabel = 'Réunion';
                    typeIcon = 'bi-people';
                    break;
                default:
                    typeLabel = 'Autre';
                    typeIcon = 'bi-three-dots';
            }

            // Format status
            let statusLabel = '';
            let statusClass = '';
            switch(programme.statut) {
                case 'planifie':
                    statusLabel = 'Planifié';
                    statusClass = 'text-info';
                    break;
                case 'en_cours':
                    statusLabel = 'En cours';
                    statusClass = 'text-primary';
                    break;
                case 'termine':
                    statusLabel = 'Terminé';
                    statusClass = 'text-success';
                    break;
                case 'annule':
                    statusLabel = 'Annulé';
                    statusClass = 'text-danger';
                    break;
            }

            tableHtml += `
                <tr>
                    <td>${programme.titre}</td>
                    <td><i class="bi ${typeIcon} me-1"></i> ${typeLabel}</td>
                    <td>${startDate}</td>
                    <td>${endDate}</td>
                    <td>${programme.lieu}</td>
                    <td class="${statusClass}"><i class="bi bi-flag me-1"></i> ${statusLabel}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-danger delete-programme-btn"
                                data-id="${programme.id_programme}"
                                data-title="${programme.titre}">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        tableHtml += `
                </tbody>
            </table>
        `;

        programmeTableContainer.innerHTML = tableHtml;

        // Add event listeners to delete buttons
        document.querySelectorAll('.delete-programme-btn').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const title = this.getAttribute('data-title');
                showDeleteProgrammeConfirmation(id, title);
            });
        });
    }

    function showProgrammeError(message) {
        programmeTableContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${message}
            </div>
            <button class="btn btn-primary mt-3" onclick="loadClubProgramme(programmeClubId.value)">
                <i class="bi bi-arrow-clockwise me-2"></i>
                Réessayer
            </button>
        `;
    }

    function resetProgrammeForm() {
        programmeForm.reset();

        // Set default dates (today and tomorrow)
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);

        // Format for datetime-local input
        const formatDatetime = (date) => {
            return date.toISOString().slice(0, 16);
        };

        programmeStartDate.value = formatDatetime(now);
        programmeEndDate.value = formatDatetime(tomorrow);
    }

    function saveProgramme() {
        // Validate form
        if (!programmeForm.checkValidity()) {
            programmeForm.reportValidity();
            return;
        }

        // Prepare data
        const data = {
            id_club: programmeClubId.value,
            titre: programmeTitle.value,
            description: programmeDescription.value,
            type: programmeType.value,
            date_debut: programmeStartDate.value,
            date_fin: programmeEndDate.value,
            lieu: programmeLocation.value,
            statut: programmeStatus.value
        };

        // Send to API
        fetch('../../route/clubsRoute.php?programme=true', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    addProgrammeModal.hide();
                    loadClubProgramme(programmeClubId.value);
                    alert('Activité ajoutée avec succès au programme!');
                } else {
                    console.error('Error:', data.error);
                    alert(`Une erreur est survenue: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue lors de l\'ajout de l\'activité au programme.');
            });
    }

    function showDeleteProgrammeConfirmation(id, title) {
        document.getElementById('deleteProgrammeTitle').textContent = title;
        confirmDeleteProgrammeBtn.setAttribute('data-id', id);
        deleteProgrammeModal.show();
    }

    function deleteProgramme() {
        const id = confirmDeleteProgrammeBtn.getAttribute('data-id');

        fetch(`../../route/clubsRoute.php?programme=true&id_programme=${id}`, {
            method: 'DELETE'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    deleteProgrammeModal.hide();
                    loadClubProgramme(programmeClubId.value);
                    alert('Activité supprimée avec succès du programme!');
                } else {
                    console.error('Error:', data.error);
                    alert(`Une erreur est survenue: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue lors de la suppression de l\'activité du programme.');
            });
    }
});