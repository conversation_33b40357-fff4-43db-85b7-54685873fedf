<?php
require_once "../model/uePreferencesModel.php";
require_once "../utils/response.php";

/**
 * API endpoint to get modules and their teaching units matching a teacher's specialty
 *
 * @param int $teacherId The teacher's ID
 */
function getModulesAndUEsByTeacherSpecialtyAPI($teacherId) {
    $modules = getModulesAndUEsByTeacherSpecialty($teacherId);

    if (isset($modules['error'])) {
        jsonResponse(['error' => $modules['error']], 404);
    }

    jsonResponse(['data' => $modules], 200);
}

/**
 * API endpoint to save a teacher's UE preference
 */
function saveUePreferenceAPI() {
    // Get the request body
    $requestBody = file_get_contents('php://input');
    $data = json_decode($requestBody, true);

    if (!$data) {
        jsonResponse(['error' => 'Invalid request data'], 400);
        return;
    }

    // Validate required fields
    if (empty($data['id_enseignant']) || empty($data['id_ue'])) {
        jsonResponse(['error' => 'Teacher ID and UE ID are required'], 400);
        return;
    }

    $result = saveUePreference($data);

    if ($result === true) {
        jsonResponse(['success' => true, 'message' => 'Preference saved successfully'], 200);
    } else {
        jsonResponse(['error' => $result['error']], 500);
    }
}

/**
 * API endpoint to get a teacher's UE preferences
 *
 * @param int $teacherId The teacher's ID
 */
function getTeacherUePreferencesAPI($teacherId) {
    $preferences = getTeacherUePreferences($teacherId);

    if (isset($preferences['error'])) {
        jsonResponse(['error' => $preferences['error']], 404);
    }

    jsonResponse(['data' => $preferences], 200);
}

/**
 * API endpoint to delete a teacher's UE preference
 *
 * @param int $preferenceId The preference ID
 */
function deleteUePreferenceAPI($preferenceId) {
    $result = deleteUePreference($preferenceId);

    if ($result === true) {
        jsonResponse(['success' => true, 'message' => 'Preference deleted successfully'], 200);
    } else {
        jsonResponse(['error' => $result['error']], 500);
    }
}

/**
 * API endpoint to save multiple UE preferences for a teacher
 */
function saveMultipleUePreferencesAPI() {
    // Get the request body
    $requestBody = file_get_contents('php://input');
    $data = json_decode($requestBody, true);

    if (!$data) {
        jsonResponse(['error' => 'Invalid request data'], 400);
        return;
    }

    // Validate required fields
    if (empty($data['id_enseignant']) || !isset($data['ue_ids'])) {
        jsonResponse(['error' => 'Teacher ID and UE IDs are required'], 400);
        return;
    }

    $teacherId = $data['id_enseignant'];
    $ueIds = $data['ue_ids'];
    $reason = isset($data['reason']) ? $data['reason'] : null;

    $result = saveMultipleUePreferences($teacherId, $ueIds, $reason);

    if ($result === true) {
        jsonResponse(['success' => true, 'message' => 'Preferences saved successfully'], 200);
    } else {
        jsonResponse(['error' => $result['error']], 500);
    }
}

// Handle API requests
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getModulesAndUEsByTeacherSpecialty':
            if (!isset($_GET['teacher_id'])) {
                jsonResponse(['error' => 'Teacher ID is required'], 400);
            }
            getModulesAndUEsByTeacherSpecialtyAPI($_GET['teacher_id']);
            break;

        case 'getTeacherUePreferences':
            if (!isset($_GET['teacher_id'])) {
                jsonResponse(['error' => 'Teacher ID is required'], 400);
            }
            getTeacherUePreferencesAPI($_GET['teacher_id']);
            break;

        case 'saveUePreference':
            saveUePreferenceAPI();
            break;

        case 'saveMultipleUePreferences':
            saveMultipleUePreferencesAPI();
            break;

        case 'deleteUePreference':
            if (!isset($_GET['preference_id'])) {
                jsonResponse(['error' => 'Preference ID is required'], 400);
            }
            deleteUePreferenceAPI($_GET['preference_id']);
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            break;
    }
}
?>
