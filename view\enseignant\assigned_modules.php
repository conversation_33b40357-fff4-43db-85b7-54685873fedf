<?php
/**
 * Vue pour la page des modules assignés aux enseignants
 *
 * Cette page affiche toutes les unités d'enseignement assignées à l'enseignant connecté.
 * Les données sont récupérées via JavaScript et l'API.
 */

// Inclure la vérification d'authentification pour les enseignants
require_once '../includes/auth_check_enseignant.php';

// Récupérer l'ID de l'enseignant depuis la session
$teacherId = $_SESSION['user']['teacher_id'] ?? null;

// Récupérer le nom complet de l'enseignant
$teacherFullName = $_SESSION['user']['name'] ?? 'Enseignant';

// Titre de la page
$pageTitle = "Mes Unités d'Enseignement";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - UniAdmin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <!-- Favicon -->
    <link rel="icon" href="../assets/img/favicon.ico" type="image/x-icon">
</head>
<body data-teacher-id="<?php echo $teacherId; ?>">
    <div class="dashboard-container">
        <!-- Include sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Include header -->
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4 assigned-modules-container">
                <h1 class="page-title"><?php echo $pageTitle; ?></h1>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <p class="text-muted">
                            Consultez toutes les unités d'enseignement (UE) qui vous sont assignées à travers différents modules. Chaque unité d'enseignement représente une composante spécifique (Cours, TD, TP) d'un module dont vous êtes responsable. Utilisez les filtres ci-dessous pour affiner les résultats.
                        </p>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Filtres</h5>
                    </div>
                    <div class="card-body">
                        <form id="filter-form" class="row g-3">
                            <!-- Academic Year Filter -->
                            <div class="col-md-2">
                                <label for="academic_year" class="form-label">Année académique</label>
                                <select class="form-select" id="academic_year" name="academic_year" data-filter="academic_year">
                                    <option value="">Toutes les années</option>
                                    <!-- Options will be loaded via JavaScript -->
                                </select>
                            </div>

                            <!-- Filiere Filter -->
                            <div class="col-md-3">
                                <label for="filiere" class="form-label">Filière/Département</label>
                                <select class="form-select" id="filiere" name="filiere" data-filter="filiere" data-cascade="level,semester">
                                    <option value="">Toutes les filières</option>
                                    <!-- Options will be loaded via JavaScript -->
                                </select>
                            </div>

                            <!-- Level Filter -->
                            <div class="col-md-2">
                                <label for="level" class="form-label">Niveau</label>
                                <select class="form-select" id="level" name="level" data-filter="level" data-cascade="semester" data-depends-on="filiere">
                                    <option value="">Tous les niveaux</option>
                                    <!-- Options will be loaded via JavaScript -->
                                </select>
                            </div>

                            <!-- Semester Filter -->
                            <div class="col-md-2">
                                <label for="semester" class="form-label">Semestre</label>
                                <select class="form-select" id="semester" name="semester" data-filter="semester" data-depends-on="level">
                                    <option value="">Tous les semestres</option>
                                    <!-- Options will be loaded via JavaScript -->
                                </select>
                            </div>

                            <!-- UE Type Filter -->
                            <div class="col-md-2">
                                <label for="type" class="form-label">Type d'UE</label>
                                <select class="form-select" id="type" name="type" data-filter="type">
                                    <option value="">Tous les types</option>
                                    <option value="Cours">Cours</option>
                                    <option value="TD">TD</option>
                                    <option value="TP">TP</option>
                                </select>
                            </div>

                            <!-- Reset Button -->
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="button" id="reset-filters" class="btn btn-outline-secondary w-100">
                                    <i class="bi bi-x-circle me-1"></i> Réinitialiser
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Results Table -->
                <div class="card">
                    <div class="card-header bg-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Unités d'enseignement & Modules</h5>
                        <span class="badge bg-primary" id="module-count">Chargement...</span>
                    </div>
                    <div class="card-body">
                        <div id="results-container">
                            <div class="text-center p-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="mt-3">Chargement des données...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/script.js"></script>

    <!-- Assigned Modules JS -->
    <script src="../assets/js/assigned_modules.js"></script>

    <!-- Sidebar JS -->
    <script src="../assets/js/sidebar.js"></script>
</body>
</html>
