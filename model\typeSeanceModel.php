<?php
require_once "../config/db.php";

// Get all session types
function getAllTypeSeances() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllTypeSeances");
        return ["error" => "Database connection error"];
    }

    $types = [];

    // First try to get types from the dedicated table if it exists
    $checkTableSql = "SHOW TABLES LIKE 'type_seance'";
    $tableExists = mysqli_query($conn, $checkTableSql);

    if ($tableExists && mysqli_num_rows($tableExists) > 0) {
        // Get types from the dedicated table
        $sql = "SELECT * FROM type_seance ORDER BY nom_type";
        $result = mysqli_query($conn, $sql);

        if (!$result) {
            error_log("Error executing query in getAllTypeSeances: " . mysqli_error($conn));
            mysqli_close($conn);
            return ["error" => "Error fetching session types"];
        }

        while ($row = mysqli_fetch_assoc($result)) {
            $types[] = $row;
        }
    }

    // Also get distinct types from the seance table
    $sql = "SELECT DISTINCT type as nom_type FROM seance WHERE type IS NOT NULL AND type != ''";
    $result = mysqli_query($conn, $sql);

    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            // Check if this type already exists in our array
            $typeExists = false;
            foreach ($types as $existingType) {
                if (strtolower($existingType['nom_type']) === strtolower($row['nom_type'])) {
                    $typeExists = true;
                    break;
                }
            }

            if (!$typeExists) {
                $types[] = [
                    'id_type' => $row['nom_type'],
                    'nom_type' => $row['nom_type']
                ];
            }
        }
    }

    // Also get distinct types from the exams table if it exists
    $checkExamsTableSql = "SHOW TABLES LIKE 'exams'";
    $examsTableExists = mysqli_query($conn, $checkExamsTableSql);

    if ($examsTableExists && mysqli_num_rows($examsTableExists) > 0) {
        $sql = "SELECT DISTINCT type as nom_type FROM exams WHERE type IS NOT NULL AND type != ''";
        $result = mysqli_query($conn, $sql);

        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                // Check if this type already exists in our array
                $typeExists = false;
                foreach ($types as $existingType) {
                    if (strtolower($existingType['nom_type']) === strtolower($row['nom_type'])) {
                        $typeExists = true;
                        break;
                    }
                }

                if (!$typeExists) {
                    $types[] = [
                        'id_type' => $row['nom_type'],
                        'nom_type' => $row['nom_type']
                    ];
                }
            }
        }
    }

    // If no types found in the database, return default types
    if (empty($types)) {
        $types = [
            ['id_type' => 'Cours', 'nom_type' => 'Cours'],
            ['id_type' => 'TD', 'nom_type' => 'TD'],
            ['id_type' => 'TP', 'nom_type' => 'TP'],
            ['id_type' => 'Examen', 'nom_type' => 'Examen'],
            ['id_type' => 'Final Exam', 'nom_type' => 'Final Exam'],
            ['id_type' => 'Midterm Exam', 'nom_type' => 'Midterm Exam'],
            ['id_type' => 'Quiz', 'nom_type' => 'Quiz'],
            ['id_type' => 'Test', 'nom_type' => 'Test'],
            ['id_type' => 'Practical Exam', 'nom_type' => 'Practical Exam'],
            ['id_type' => 'Examen Final', 'nom_type' => 'Examen Final'],
            ['id_type' => 'Contrôle Continu', 'nom_type' => 'Contrôle Continu'],
            ['id_type' => 'Rattrapage', 'nom_type' => 'Rattrapage']
        ];
    }

    // Sort types alphabetically
    usort($types, function($a, $b) {
        return strcmp($a['nom_type'], $b['nom_type']);
    });

    mysqli_close($conn);
    return $types;
}

// Get session type by ID
function getTypeSeanceById($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTypeSeanceById");
        return ["error" => "Database connection error"];
    }

    // Check if the type_seance table exists
    $checkTableSql = "SHOW TABLES LIKE 'type_seance'";
    $tableExists = mysqli_query($conn, $checkTableSql);

    if ($tableExists && mysqli_num_rows($tableExists) > 0) {
        // Get type from the dedicated table
        $id = mysqli_real_escape_string($conn, $id);
        $sql = "SELECT * FROM type_seance WHERE id_type = '$id'";
        $result = mysqli_query($conn, $sql);

        if (!$result) {
            error_log("Error executing query in getTypeSeanceById: " . mysqli_error($conn));
            mysqli_close($conn);
            return ["error" => "Error fetching session type"];
        }

        $type = mysqli_fetch_assoc($result);
        mysqli_close($conn);
        return $type;
    } else {
        // If no dedicated table exists, just return the ID as both id and name
        return [
            'id_type' => $id,
            'nom_type' => $id
        ];
    }
}
?>