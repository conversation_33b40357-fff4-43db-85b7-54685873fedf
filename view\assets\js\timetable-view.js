// Function to show a success message
function showSuccessMessage(message) {
    // Create a toast element
    const toast = document.createElement('div');
    toast.className = 'toast-message success-toast';
    toast.textContent = message;

    // Add the toast to the document
    document.body.appendChild(toast);

    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Hide and remove the toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Function to show an error message
function showErrorMessage(message) {
    // Create a toast element
    const toast = document.createElement('div');
    toast.className = 'toast-message error-toast';
    toast.textContent = message;

    // Add the toast to the document
    document.body.appendChild(toast);

    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Hide and remove the toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Function to get URL parameters
function getUrlParams() {
    const params = {};
    const queryString = window.location.search.substring(1);
    const pairs = queryString.split('&');

    for (const pair of pairs) {
        const [key, value] = pair.split('=');
        if (key && value) {
            params[decodeURIComponent(key)] = decodeURIComponent(value);
        }
    }

    return params;
}

// Function to display the applied filters
function displayAppliedFilters(filters) {
    const appliedFiltersContainer = document.getElementById('applied-filters');
    if (!appliedFiltersContainer) return;

    let filtersHtml = '<div class="filter-badges">';

    // Filière
    if (filters.filiere && filters.filiere !== 'none') {
        filtersHtml += `<div class="filter-badge"><span class="filter-label">Filière:</span> <span class="filter-value">${filters.filiereName || filters.filiere}</span></div>`;
    }

    // Niveau
    if (filters.niveau) {
        filtersHtml += `<div class="filter-badge"><span class="filter-label">Niveau:</span> <span class="filter-value">${filters.niveauName || filters.niveau}</span></div>`;
    }

    // Groupe
    if (filters.groupe && filters.groupe !== 'all') {
        filtersHtml += `<div class="filter-badge"><span class="filter-label">Groupe:</span> <span class="filter-value">${filters.groupeName || filters.groupe}</span></div>`;
    }

    // Semestre
    if (filters.semestre) {
        filtersHtml += `<div class="filter-badge"><span class="filter-label">Semestre:</span> <span class="filter-value">${filters.semestre}</span></div>`;
    }

    // Semaine
    if (filters.semaine) {
        filtersHtml += `<div class="filter-badge"><span class="filter-label">Semaine:</span> <span class="filter-value">${filters.semaine}</span></div>`;
    }

    filtersHtml += '</div>';

    appliedFiltersContainer.innerHTML = filtersHtml;
}

// Function to create a class card
function createClassCard(classData) {
    console.log('Creating class card with data:', classData);

    // Determine the class type for styling based on session type
    let classType = 'lecture'; // Default to lecture

    // Get the session type
    let sessionType = classData.type_seance || classData.type || '';
    sessionType = sessionType.toLowerCase();

    // Map session types to CSS classes
    if (sessionType.includes('tp') || sessionType === 'practical' || sessionType === 'practical work') {
        classType = 'tp';
    } else if (sessionType.includes('td') || sessionType === 'directed' || sessionType === 'tutorial') {
        classType = 'td';
    } else if (sessionType.includes('exam') || sessionType.includes('test') || sessionType.includes('contrôle')) {
        classType = 'exam';
    } else if (sessionType.includes('workshop') || sessionType.includes('atelier')) {
        classType = 'workshop';
    } else if (sessionType.includes('seminar') || sessionType.includes('séminaire') || sessionType.includes('conference')) {
        classType = 'seminar';
    } else if (sessionType.includes('cours') || sessionType.includes('lecture') || sessionType.includes('class')) {
        classType = 'lecture';
    }

    // Get the module name
    const moduleName = classData.module || classData.nom_module || classData.title || 'Cours';

    // Create the class card element
    const classCard = document.createElement('div');
    classCard.className = `class-card ${classType}`;
    classCard.dataset.seanceId = classData.id_seance || classData.id || '';

    // Add the class title
    const title = document.createElement('div');
    title.className = 'class-title';
    title.textContent = moduleName;
    classCard.appendChild(title);

    // Add the professor info
    const professor = document.createElement('div');
    professor.className = 'class-info';
    let profName = 'Professor: ';

    if (classData.enseignant) {
        profName += classData.enseignant;
    } else if (classData.professor) {
        profName += classData.professor;
    } else if (classData.prof_nom && classData.prof_prenom) {
        profName += `${classData.prof_prenom} ${classData.prof_nom}`;
    } else {
        profName += 'Not assigned';
    }

    professor.textContent = profName;
    classCard.appendChild(professor);

    // Add the room info
    const room = document.createElement('div');
    room.className = 'class-info';
    let roomName = 'Room: ';

    if (classData.salle) {
        roomName += classData.salle;
    } else if (classData.room) {
        roomName += classData.room;
    } else if (classData.nom_salle) {
        roomName += classData.nom_salle;
    } else {
        roomName += 'Not assigned';
    }

    room.textContent = roomName;
    classCard.appendChild(room);

    // Add the subject/type info
    const subject = document.createElement('div');
    subject.className = 'class-subject';
    let typeName = '';

    if (classData.type_seance) {
        typeName = classData.type_seance;
    } else if (classData.type) {
        typeName = classData.type;
    } else if (classData.subject) {
        typeName = classData.subject;
    } else {
        typeName = 'Lecture';
    }

    // Standardize common session types for display
    const displayType = typeName.toLowerCase();
    if (displayType.includes('tp')) {
        typeName = 'TP';
    } else if (displayType.includes('td')) {
        typeName = 'TD';
    } else if (displayType.includes('cours') || displayType.includes('lecture')) {
        typeName = 'Lecture';
    } else if (displayType.includes('exam')) {
        typeName = 'Exam';
    }

    subject.textContent = typeName;
    classCard.appendChild(subject);

    // Add click event to edit the session
    classCard.addEventListener('click', () => {
        openEditSessionModal(classData);
    });

    console.log('Class card created successfully');
    return classCard;
}

// Function to open the add session modal
function openAddSessionModal(day, timeSlot) {
    console.log('Opening add session modal...');
    console.log('Day:', day, 'Time slot:', timeSlot);

    try {
        // Get the URL parameters
        const params = getUrlParams();
        console.log('URL parameters:', params);

        // Set the hidden fields
        const filiereIdField = document.getElementById('filiere_id');
        const niveauIdField = document.getElementById('niveau_id');
        const groupeIdField = document.getElementById('groupe_id');
        const semestreField = document.getElementById('semestre');

        if (filiereIdField) filiereIdField.value = params.filiere || '';
        if (niveauIdField) niveauIdField.value = params.niveau || '';
        if (groupeIdField) groupeIdField.value = params.groupe || '';
        if (semestreField) semestreField.value = params.semestre || '';

        console.log('Set hidden fields:', {
            filiere: filiereIdField ? filiereIdField.value : 'field not found',
            niveau: niveauIdField ? niveauIdField.value : 'field not found',
            groupe: groupeIdField ? groupeIdField.value : 'field not found',
            semestre: semestreField ? semestreField.value : 'field not found'
        });

        // Set the day and time
        const dayField = document.getElementById('day');
        const timeField = document.getElementById('time');

        if (dayField) dayField.value = day;
        if (timeField) timeField.value = timeSlot;

        console.log('Set day and time fields:', {
            day: dayField ? dayField.value : 'field not found',
            time: timeField ? timeField.value : 'field not found'
        });

        // Load modules, enseignants, salles, and session types
        console.log('Loading dropdown options...');
        Promise.all([
            loadModules(),
            loadEnseignants(),
            loadSalles(),
            loadTypeSeances()
        ]).then(() => {
            console.log('All dropdown options loaded successfully');
        }).catch(error => {
            console.error('Error loading dropdown options:', error);
        });

        // Show the modal
        const modalElement = document.getElementById('addSessionModal');
        if (!modalElement) {
            console.error('Modal element not found');
            return;
        }

        console.log('Showing modal...');
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('Modal shown');
    } catch (error) {
        console.error('Error opening add session modal:', error);
        alert('Erreur lors de l\'ouverture du formulaire: ' + error.message);
    }
}

// Function to open the edit session modal
function openEditSessionModal(sessionData) {
    console.log('Opening edit session modal...', sessionData);

    try {
        // Set the session ID
        const seanceIdField = document.getElementById('edit_seance_id');
        if (seanceIdField) {
            seanceIdField.value = sessionData.id_seance || sessionData.id || '';
            console.log('Set session ID:', seanceIdField.value);
        } else {
            console.error('Session ID field not found');
        }

        // Load modules, enseignants, salles, and session types
        console.log('Loading dropdown options...');
        Promise.all([
            loadModules('edit_module', sessionData.id_module),
            loadEnseignants('edit_enseignant', sessionData.id_enseignant),
            loadSalles('edit_salle', sessionData.id_salle),
            loadTypeSeances('edit_type_seance', sessionData.type_seance || sessionData.type)
        ]).then(() => {
            console.log('All dropdown options loaded successfully');
        }).catch(error => {
            console.error('Error loading dropdown options:', error);
        });

        // Set the other fields
        const typeSeanceField = document.getElementById('edit_type_seance');
        const dayField = document.getElementById('edit_day');
        const timeField = document.getElementById('edit_time');

        if (typeSeanceField) {
            typeSeanceField.value = sessionData.type_seance || sessionData.type || '';
            console.log('Set type_seance:', typeSeanceField.value);
        } else {
            console.error('Type seance field not found');
        }

        if (dayField) {
            // Handle both jour and day fields for backward compatibility
            dayField.value = sessionData.jour || sessionData.day || '';
            console.log('Set day:', dayField.value);
        } else {
            console.error('Day field not found');
        }

        // Set the time slot
        let timeSlot = sessionData.time || '';
        if (!timeSlot && sessionData.heure_debut && sessionData.heure_fin) {
            timeSlot = `${sessionData.heure_debut}-${sessionData.heure_fin}`;
        }

        if (timeField) {
            timeField.value = timeSlot;
            console.log('Set time:', timeField.value);
        } else {
            console.error('Time field not found');
        }

        console.log('Form data set successfully');

        // Show the modal
        const modalElement = document.getElementById('editSessionModal');
        if (!modalElement) {
            console.error('Modal element not found');
            return;
        }

        console.log('Showing modal...');
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('Modal shown');
    } catch (error) {
        console.error('Error opening edit session modal:', error);
        alert('Erreur lors de l\'ouverture du formulaire: ' + error.message);
    }
}

// Function to load modules from the server
async function loadModules(selectId = 'module', selectedValue = null) {
    console.log(`Loading modules for select ${selectId}, selected value: ${selectedValue}`);

    try {
        const selectElement = document.getElementById(selectId);
        if (!selectElement) {
            console.error(`Select element with ID ${selectId} not found`);
            return;
        }

        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="" selected disabled>Sélectionner un module</option>';

        console.log('Fetching modules from ../../route/moduleRoute.php');
        const response = await fetch('../../route/moduleRoute.php');

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Get the raw response text first for debugging
        const responseText = await response.text();
        console.log('Raw module response:', responseText.substring(0, 100) + (responseText.length > 100 ? '...' : ''));

        // Try to parse the response as JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('Error parsing module response JSON:', parseError);
            throw new Error('Invalid JSON response from server');
        }

        console.log(`Loaded ${data.data ? data.data.length : 0} modules`);

        if (data.data && Array.isArray(data.data)) {
            data.data.forEach(module => {
                const option = document.createElement('option');
                option.value = module.id_module;
                option.textContent = module.nom_module;
                if (selectedValue && module.id_module == selectedValue) {
                    option.selected = true;
                }
                selectElement.appendChild(option);
            });
            console.log(`Added ${data.data.length} module options to select`);
        } else {
            console.warn('No modules data found or data is not an array');
        }

        return data;
    } catch (error) {
        console.error('Error loading modules:', error);
        // Add a disabled option indicating the error
        const selectElement = document.getElementById(selectId);
        if (selectElement) {
            const errorOption = document.createElement('option');
            errorOption.disabled = true;
            errorOption.textContent = `Erreur: ${error.message}`;
            selectElement.appendChild(errorOption);
        }
        throw error;
    }
}

// Function to load enseignants from the server
async function loadEnseignants(selectId = 'enseignant', selectedValue = null) {
    console.log(`Loading enseignants for select ${selectId}, selected value: ${selectedValue}`);

    try {
        const selectElement = document.getElementById(selectId);
        if (!selectElement) {
            console.error(`Select element with ID ${selectId} not found`);
            return;
        }

        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="" selected disabled>Sélectionner un enseignant</option>';

        console.log('Fetching enseignants from ../../route/enseignantRoute.php');
        const response = await fetch('../../route/enseignantRoute.php');

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Get the raw response text first for debugging
        const responseText = await response.text();
        console.log('Raw enseignant response:', responseText.substring(0, 100) + (responseText.length > 100 ? '...' : ''));

        // Try to parse the response as JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('Error parsing enseignant response JSON:', parseError);
            throw new Error('Invalid JSON response from server');
        }

        console.log(`Loaded ${data.data ? data.data.length : 0} enseignants`);

        if (data.data && Array.isArray(data.data)) {
            data.data.forEach(enseignant => {
                const option = document.createElement('option');
                option.value = enseignant.id_enseignant; // Use id_enseignant instead of CNI
                option.textContent = `${enseignant.prenom} ${enseignant.nom}`;
                if (selectedValue && enseignant.id_enseignant == selectedValue) {
                    option.selected = true;
                }
                selectElement.appendChild(option);
            });
            console.log(`Added ${data.data.length} enseignant options to select`);
        } else {
            console.warn('No enseignants data found or data is not an array');
        }

        return data;
    } catch (error) {
        console.error('Error loading enseignants:', error);
        // Add a disabled option indicating the error
        const selectElement = document.getElementById(selectId);
        if (selectElement) {
            const errorOption = document.createElement('option');
            errorOption.disabled = true;
            errorOption.textContent = `Erreur: ${error.message}`;
            selectElement.appendChild(errorOption);
        }
        throw error;
    }
}

// Function to load session types from the server
async function loadTypeSeances(selectId = 'type_seance', selectedValue = null) {
    console.log(`Loading session types for select ${selectId}, selected value: ${selectedValue}`);

    try {
        const selectElement = document.getElementById(selectId);
        if (!selectElement) {
            console.error(`Select element with ID ${selectId} not found`);
            return;
        }

        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="" selected disabled>Select session type</option>';

        console.log('Fetching session types from ../../route/typeSeanceRoute.php');
        const response = await fetch('../../route/typeSeanceRoute.php');

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Get the raw response text first for debugging
        const responseText = await response.text();
        console.log('Raw session type response:', responseText.substring(0, 100) + (responseText.length > 100 ? '...' : ''));

        // Try to parse the response as JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('Error parsing session type response JSON:', parseError);
            throw new Error('Invalid JSON response from server');
        }

        console.log(`Loaded ${data.data ? data.data.length : 0} session types`);

        if (data.data && Array.isArray(data.data)) {
            data.data.forEach(type => {
                const option = document.createElement('option');
                option.value = type.id_type || type.nom_type;
                option.textContent = type.nom_type;
                if (selectedValue && (type.id_type == selectedValue || type.nom_type == selectedValue)) {
                    option.selected = true;
                }
                selectElement.appendChild(option);
            });
            console.log(`Added ${data.data.length} session type options to select`);
        } else {
            console.warn('No session types data found or data is not an array');

            // Add default session types if none were returned
            const defaultTypes = [
                { id_type: 'Cours', nom_type: 'Cours' },
                { id_type: 'TD', nom_type: 'TD' },
                { id_type: 'TP', nom_type: 'TP' },
                { id_type: 'Examen', nom_type: 'Examen' }
            ];

            defaultTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.id_type;
                option.textContent = type.nom_type;
                if (selectedValue && type.id_type == selectedValue) {
                    option.selected = true;
                }
                selectElement.appendChild(option);
            });
            console.log('Added default session type options to select');
        }

        return data;
    } catch (error) {
        console.error('Error loading session types:', error);
        // Add a disabled option indicating the error
        const selectElement = document.getElementById(selectId);
        if (selectElement) {
            const errorOption = document.createElement('option');
            errorOption.disabled = true;
            errorOption.textContent = `Error: ${error.message}`;
            selectElement.appendChild(errorOption);

            // Add default session types as fallback
            const defaultTypes = [
                { id_type: 'Cours', nom_type: 'Cours' },
                { id_type: 'TD', nom_type: 'TD' },
                { id_type: 'TP', nom_type: 'TP' },
                { id_type: 'Examen', nom_type: 'Examen' }
            ];

            defaultTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.id_type;
                option.textContent = type.nom_type;
                if (selectedValue && type.id_type == selectedValue) {
                    option.selected = true;
                }
                selectElement.appendChild(option);
            });
            console.log('Added default session type options as fallback');
        }
        throw error;
    }
}

// Function to load salles from the server
async function loadSalles(selectId = 'salle', selectedValue = null) {
    console.log(`Loading salles for select ${selectId}, selected value: ${selectedValue}`);

    try {
        const selectElement = document.getElementById(selectId);
        if (!selectElement) {
            console.error(`Select element with ID ${selectId} not found`);
            return;
        }

        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="" selected disabled>Sélectionner une salle</option>';

        console.log('Fetching salles from ../../route/salleRoute.php');
        const response = await fetch('../../route/salleRoute.php');

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Get the raw response text first for debugging
        const responseText = await response.text();
        console.log('Raw salle response:', responseText.substring(0, 100) + (responseText.length > 100 ? '...' : ''));

        // Try to parse the response as JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('Error parsing salle response JSON:', parseError);
            throw new Error('Invalid JSON response from server');
        }

        console.log(`Loaded ${data.data ? data.data.length : 0} salles`);

        if (data.data && Array.isArray(data.data)) {
            data.data.forEach(salle => {
                const option = document.createElement('option');
                option.value = salle.id_salle;
                option.textContent = salle.nom_salle;
                if (selectedValue && salle.id_salle == selectedValue) {
                    option.selected = true;
                }
                selectElement.appendChild(option);
            });
            console.log(`Added ${data.data.length} salle options to select`);
        } else {
            console.warn('No salles data found or data is not an array');
        }

        return data;
    } catch (error) {
        console.error('Error loading salles:', error);
        // Add a disabled option indicating the error
        const selectElement = document.getElementById(selectId);
        if (selectElement) {
            const errorOption = document.createElement('option');
            errorOption.disabled = true;
            errorOption.textContent = `Erreur: ${error.message}`;
            selectElement.appendChild(errorOption);
        }
        throw error;
    }
}

// Function to add a new session
async function addSession() {
    try {
        console.log('Adding new session...');

        // Get form data
        const moduleId = document.getElementById('module').value;
        const enseignantId = document.getElementById('enseignant').value;
        const salleId = document.getElementById('salle').value;
        const typeSeance = document.getElementById('type_seance').value;
        const day = document.getElementById('day').value;
        const time = document.getElementById('time').value;
        const filiereId = document.getElementById('filiere_id').value;
        const niveauId = document.getElementById('niveau_id').value;
        const groupeId = document.getElementById('groupe_id').value;
        const semestre = document.getElementById('semestre').value;

        console.log('Form data:', {
            moduleId, enseignantId, salleId, typeSeance, day, time,
            filiereId, niveauId, groupeId, semestre
        });

        // Log more detailed information about the teacher ID
        console.log('Teacher ID details:', {
            value: enseignantId,
            type: typeof enseignantId,
            length: enseignantId ? enseignantId.length : 0
        });

        // Validate required fields
        if (!moduleId || !enseignantId || !salleId || !typeSeance || !day || !time || !niveauId || !semestre) {
            alert('Please fill in all required fields');
            return;
        }

        // Parse the time slot
        const [startTime, endTime] = time.split('-');
        console.log('Time slot:', { startTime, endTime });

        // Create the request data
        const requestData = {
            id_module: moduleId,
            id_enseignant: enseignantId,
            id_salle: salleId,
            type_seance: typeSeance,
            jour: day,  // Using jour field for backend compatibility
            heure_debut: startTime,
            heure_fin: endTime,
            id_niveau: niveauId,
            semestre: semestre
        };

        // Add optional fields if they exist
        if (filiereId && filiereId !== 'none') {
            requestData.id_filiere = filiereId;
        }

        if (groupeId && groupeId !== 'all') {
            requestData.id_groupe = groupeId;
        }

        console.log('Request data:', requestData);
        console.log('JSON data:', JSON.stringify(requestData));

        // Send the request
        console.log('Sending POST request to ../../route/seanceRoute.php');
        const response = await fetch('../../route/seanceRoute.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        console.log('Response status:', response.status);

        // Get the raw response text first for debugging
        const responseText = await response.text();
        console.log('Raw response:', responseText);

        // Try to parse the response as JSON
        let data;
        try {
            data = JSON.parse(responseText);
            console.log('Parsed response data:', data);
        } catch (parseError) {
            console.error('Error parsing response JSON:', parseError);
            throw new Error('Invalid JSON response from server: ' + responseText.substring(0, 100));
        }

        if (data.error) {
            throw new Error(data.error);
        }

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addSessionModal'));
        if (modal) {
            modal.hide();
        } else {
            console.warn('Modal instance not found');
            // Try to close it using jQuery if available
            if (typeof $ !== 'undefined') {
                $('#addSessionModal').modal('hide');
            }
        }

        // Get the module, teacher, and room names
        let moduleName = '';
        let teacherName = '';
        let roomName = '';

        // Get the module name from the select element
        const moduleSelect = document.getElementById('module');
        if (moduleSelect && moduleSelect.selectedIndex >= 0) {
            moduleName = moduleSelect.options[moduleSelect.selectedIndex].text;
        }

        // Get the teacher name from the select element
        const teacherSelect = document.getElementById('enseignant');
        if (teacherSelect && teacherSelect.selectedIndex >= 0) {
            teacherName = teacherSelect.options[teacherSelect.selectedIndex].text;
        }

        // Get the room name from the select element
        const roomSelect = document.getElementById('salle');
        if (roomSelect && roomSelect.selectedIndex >= 0) {
            roomName = roomSelect.options[roomSelect.selectedIndex].text;
        }

        // Create a session object with the data we have
        const newSession = {
            id_seance: data.id || data.data.id,
            id_module: moduleId,
            id_enseignant: enseignantId,
            id_salle: salleId,
            module: moduleName,
            enseignant: teacherName,
            salle: roomName,
            type_seance: typeSeance,
            jour: day,
            day: day,
            heure_debut: startTime,
            heure_fin: endTime
        };

        console.log('New session object:', newSession);

        // Add the session to the timetable immediately
        addSessionToTimetable(newSession);

        // Store the session in localStorage to ensure it persists even if the page is refreshed
        storeSessionInLocalStorage(newSession);

        // Don't reload the timetable immediately as it might clear the newly added session
        // Instead, set a timeout to reload after a delay to ensure the session is saved in the database
        setTimeout(() => {
            loadTimetableData();
        }, 2000);

        // Show success message
        showSuccessMessage('Session added successfully');
    } catch (error) {
        console.error('Error adding session:', error);
        alert(`Error adding session: ${error.message}`);
    }
}

// Function to update a session
async function updateSession() {
    try {
        console.log('Updating session...');

        // Get form data
        const seanceId = document.getElementById('edit_seance_id').value;
        const moduleId = document.getElementById('edit_module').value;
        const enseignantId = document.getElementById('edit_enseignant').value;
        const salleId = document.getElementById('edit_salle').value;
        const typeSeance = document.getElementById('edit_type_seance').value;
        const day = document.getElementById('edit_day').value;
        const time = document.getElementById('edit_time').value;

        console.log('Form data:', {
            seanceId, moduleId, enseignantId, salleId, typeSeance, day, time
        });

        // Validate required fields
        if (!seanceId || !moduleId || !enseignantId || !salleId || !typeSeance || !day || !time) {
            alert('Please fill in all required fields');
            return;
        }

        // Parse the time slot
        const [startTime, endTime] = time.split('-');
        console.log('Time slot:', { startTime, endTime });

        // Create the request data
        const requestData = {
            id_seance: seanceId,
            id_module: moduleId,
            id_enseignant: enseignantId,
            id_salle: salleId,
            type_seance: typeSeance,
            jour: day,  // Using jour field for backend compatibility
            heure_debut: startTime,
            heure_fin: endTime
        };

        console.log('Request data:', requestData);
        console.log('JSON data:', JSON.stringify(requestData));

        // Send the request
        console.log('Sending PUT request to ../../route/seanceRoute.php');
        const response = await fetch('../../route/seanceRoute.php', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        console.log('Response status:', response.status);

        // Get the raw response text first for debugging
        const responseText = await response.text();
        console.log('Raw response:', responseText);

        // Try to parse the response as JSON
        let data;
        try {
            data = JSON.parse(responseText);
            console.log('Parsed response data:', data);
        } catch (parseError) {
            console.error('Error parsing response JSON:', parseError);
            throw new Error('Invalid JSON response from server: ' + responseText.substring(0, 100));
        }

        if (data.error) {
            throw new Error(data.error);
        }

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('editSessionModal'));
        if (modal) {
            modal.hide();
        } else {
            console.warn('Modal instance not found');
            // Try to close it using jQuery if available
            if (typeof $ !== 'undefined') {
                $('#editSessionModal').modal('hide');
            }
        }

        // Reload the timetable
        loadTimetableData();

        // Show success message
        showSuccessMessage('Session updated successfully');
    } catch (error) {
        console.error('Error updating session:', error);
        alert(`Error updating session: ${error.message}`);
    }
}

// Function to delete a session
async function deleteSession() {
    try {
        console.log('Deleting session...');

        // Confirm deletion
        if (!confirm('Are you sure you want to delete this session?')) {
            console.log('Deletion cancelled by user');
            return;
        }

        // Get the session ID
        const seanceId = document.getElementById('edit_seance_id').value;
        console.log('Session ID to delete:', seanceId);

        if (!seanceId) {
            alert('Invalid session ID');
            return;
        }

        // Send the request
        console.log('Sending DELETE request to ../../route/seanceRoute.php?id=' + seanceId);
        const response = await fetch(`../../route/seanceRoute.php?id=${seanceId}`, {
            method: 'DELETE'
        });

        console.log('Response status:', response.status);

        // Get the raw response text first for debugging
        const responseText = await response.text();
        console.log('Raw response:', responseText);

        // Try to parse the response as JSON
        let data;
        try {
            data = JSON.parse(responseText);
            console.log('Parsed response data:', data);
        } catch (parseError) {
            console.error('Error parsing response JSON:', parseError);
            throw new Error('Invalid JSON response from server: ' + responseText.substring(0, 100));
        }

        if (data.error) {
            throw new Error(data.error);
        }

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('editSessionModal'));
        if (modal) {
            modal.hide();
        } else {
            console.warn('Modal instance not found');
            // Try to close it using jQuery if available
            if (typeof $ !== 'undefined') {
                $('#editSessionModal').modal('hide');
            }
        }

        // Reload the timetable
        loadTimetableData();

        // Show success message
        showSuccessMessage('Session deleted successfully');
    } catch (error) {
        console.error('Error deleting session:', error);
        alert(`Error deleting session: ${error.message}`);
    }
}

// Function to load filière name by ID
async function loadFiliereNameById(filiereId) {
    try {
        const response = await fetch(`../../route/filiereRoute.php?id=${filiereId}`);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Handle potential JSON parsing errors
        let data;
        try {
            const text = await response.text();
            // Check if the response is valid JSON
            if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
                data = JSON.parse(text);
            } else {
                console.error('Invalid JSON response:', text);
                return null;
            }
        } catch (parseError) {
            console.error('Error parsing JSON:', parseError);
            return null;
        }

        if (data && data.data && data.data.nom_filiere) {
            return data.data.nom_filiere;
        }
        return null;
    } catch (error) {
        console.error('Error loading filière name:', error);
        return null;
    }
}

// Function to load level name by ID
async function loadNiveauNameById(niveauId) {
    try {
        const response = await fetch(`../../route/niveauRoute.php?id=${niveauId}`);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Handle potential JSON parsing errors
        let data;
        try {
            const text = await response.text();
            // Check if the response is valid JSON
            if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
                data = JSON.parse(text);
            } else {
                console.error('Invalid JSON response:', text);
                return null;
            }
        } catch (parseError) {
            console.error('Error parsing JSON:', parseError);
            return null;
        }

        if (data && data.data) {
            // Return the niveau value as the name
            return data.data.niveau;
        }
        return null;
    } catch (error) {
        console.error('Error loading niveau name:', error);
        return null;
    }
}



// Function to map day names to day numbers
function mapDayToNumber(day) {
    if (!day) return '';

    // Create a mapping of day names to numbers
    const dayToNumber = {
        'monday': '1',
        'tuesday': '2',
        'wednesday': '3',
        'thursday': '4',
        'friday': '5',
        'saturday': '6',
        'sunday': '7',
        // Also handle capitalized versions
        'Monday': '1',
        'Tuesday': '2',
        'Wednesday': '3',
        'Thursday': '4',
        'Friday': '5',
        'Saturday': '6',
        'Sunday': '7',
        // French day names
        'lundi': '1',
        'mardi': '2',
        'mercredi': '3',
        'jeudi': '4',
        'vendredi': '5',
        'samedi': '6',
        'dimanche': '7',
        // Capitalized French day names
        'Lundi': '1',
        'Mardi': '2',
        'Mercredi': '3',
        'Jeudi': '4',
        'Vendredi': '5',
        'Samedi': '6',
        'Dimanche': '7'
    };

    // Check if the day is in our mapping
    if (dayToNumber[day]) {
        console.log(`Mapping day name "${day}" to number "${dayToNumber[day]}"`);
        return dayToNumber[day];
    }

    // If it's already a number, return it as is
    if (!isNaN(day)) {
        return day.toString();
    }

    // If it's not in our mapping, return the original
    return day;
}

// Function to translate French day names to English
function translateDayName(day) {
    if (!day) return '';

    // Create a mapping of French to English day names
    const dayTranslation = {
        'lundi': 'Monday',
        'mardi': 'Tuesday',
        'mercredi': 'Wednesday',
        'jeudi': 'Thursday',
        'vendredi': 'Friday',
        'samedi': 'Saturday',
        'dimanche': 'Sunday',
        // Also handle capitalized versions
        'Lundi': 'Monday',
        'Mardi': 'Tuesday',
        'Mercredi': 'Wednesday',
        'Jeudi': 'Thursday',
        'Vendredi': 'Friday',
        'Samedi': 'Saturday',
        'Dimanche': 'Sunday',
        // Handle day numbers
        '1': 'Monday',
        '2': 'Tuesday',
        '3': 'Wednesday',
        '4': 'Thursday',
        '5': 'Friday',
        '6': 'Saturday',
        '7': 'Sunday',
        // Handle day numbers as integers
        1: 'Monday',
        2: 'Tuesday',
        3: 'Wednesday',
        4: 'Thursday',
        5: 'Friday',
        6: 'Saturday',
        7: 'Sunday'
    };

    // Check if the day is in French and needs translation
    if (dayTranslation[day]) {
        console.log(`Translating day name from "${day}" to "${dayTranslation[day]}"`);
        return dayTranslation[day];
    }

    // If it's not in our translation map, return the original
    return day;
}

// Function to check and place all sessions
function checkAndPlaceAllSessions(sessions) {
    console.log('Checking and placing all sessions:', sessions);

    // First, check all timetable cells
    checkAllTimetableCells();

    let placedCount = 0;
    let failedCount = 0;
    let results = [];

    // Process each session
    sessions.forEach((session, index) => {
        console.log(`Processing session ${index + 1}:`, session);

        // Get the day and time
        let day = session.day || session.jour;

        // Translate day name if needed
        const translatedDay = translateDayName(day);

        // Map day to number
        const dayNumber = mapDayToNumber(day);

        console.log(`Day translations: original=${day}, translated=${translatedDay}, number=${dayNumber}`);

        const timeSlot = session.heure_debut + '-' + session.heure_fin;

        // Try different formats for the time slot
        const timeFormats = [
            timeSlot,
            formatTimeSlot(timeSlot),
            timeSlot.replace(/(\d+)h/g, '$1:00').replace(/(\d+):(\d+)h/g, '$1:$2'),  // Convert "08h-10h" to "08:00-10:00"
            timeSlot.replace(/(\d+):(\d+)/g, '$1h$2').replace(/(\d+):00/g, '$1h'),   // Convert "08:00-10:00" to "08h-10h"

            // Add more specific formats
            '08h-10h', '10h-12h', '14h-16h', '16h-18h'
        ];

        // Try with all day formats
        let cell = null;

        // Try with translated day
        cell = findMatchingCell(translatedDay, timeSlot, timeFormats);

        // If not found, try with day number
        if (!cell) {
            cell = findMatchingCell(dayNumber, timeSlot, timeFormats);
        }

        // If still not found, try with original day
        if (!cell) {
            cell = findMatchingCell(day, timeSlot, timeFormats);
        }

        if (cell) {
            console.log(`Cell found for session ${index + 1}`);



            // Clear the cell
            cell.innerHTML = '';
            cell.className = 'session-cell';

            // Create and append the class card
            const classCard = createClassCard(session);
            cell.appendChild(classCard);

            console.log(`Session ${index + 1} has been placed in the cell!`);

            placedCount++;
            results.push({
                session: index + 1,
                placed: true,
                cell: `day=${cell.dataset.day}, time=${cell.dataset.time}`
            });
        } else {
            console.warn(`Cell not found for session ${index + 1} with any day format`);
            failedCount++;
            results.push({
                session: index + 1,
                placed: false,
                reason: 'No matching cell found'
            });
        }
    });

    console.log(`Placed ${placedCount} sessions, failed to place ${failedCount} sessions`);
    console.log('Placement results:', results);

    return {
        placedCount,
        failedCount,
        results
    };
}
// Function to load timetable data from the server
async function loadTimetableData(forceRefresh = false) {
    try {
        console.log('Loading timetable data, forceRefresh =', forceRefresh);

        // Clear any existing error messages
        hideErrorMessage();

        // Always clear the timetable first to ensure a fresh start
        clearTimetable();

        // Get the URL parameters
        const params = getUrlParams();
        console.log('URL parameters:', params);

        // If we have a filière ID but no name, try to fetch the name
        if (params.filiere && params.filiere !== 'none' && !params.filiereName) {
            try {
                const filiereName = await loadFiliereNameById(params.filiere);
                if (filiereName) {
                    params.filiereName = filiereName;
                }
            } catch (error) {
                console.error('Error loading filière name:', error);
                // Continue with the ID if we can't get the name
            }
        }

        // If we have a niveau ID but no name, try to fetch the name
        if (params.niveau && !params.niveauName) {
            try {
                const niveauName = await loadNiveauNameById(params.niveau);
                if (niveauName) {
                    params.niveauName = niveauName;
                }
            } catch (error) {
                console.error('Error loading niveau name:', error);
                // Continue with the ID if we can't get the name
            }
        }

        // Display the applied filters
        displayAppliedFilters(params);

        // Build the query string for the API request
        const queryParams = new URLSearchParams();

        if (params.niveau) {
            queryParams.append('niveau', params.niveau);
            console.log('Added niveau filter:', params.niveau);
        }

        if (params.semestre) {
            queryParams.append('semestre', params.semestre);
            console.log('Added semestre filter:', params.semestre);
        }

        // Only add filiere if it's not 'none'
        if (params.filiere && params.filiere !== 'none') {
            queryParams.append('filiere', params.filiere);
            console.log('Added filiere filter:', params.filiere);
        }

        // Only add groupe if it's not 'all'
        if (params.groupe && params.groupe !== 'all') {
            queryParams.append('groupe', params.groupe);
            console.log('Added groupe filter:', params.groupe);
        }

        if (params.semaine) {
            queryParams.append('semaine', params.semaine);
            console.log('Added semaine filter:', params.semaine);
        }

        // Add a cache-busting parameter with a random component
        queryParams.append('_', Date.now() + Math.random().toString(36).substring(2, 15));

        // Make the API request with cache-busting headers
        const url = `../../route/seanceRoute.php?${queryParams.toString()}`;
        console.log('Fetching timetable data from:', url);

        const response = await fetch(url, {
            method: 'GET',
            cache: 'no-store',  // Strongest cache prevention
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0',
                'X-Requested-With': 'XMLHttpRequest'  // Helps prevent caching in some servers
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Get the response text
        const text = await response.text();
        console.log('Raw response:', text.substring(0, 200) + (text.length > 200 ? '...' : ''));
        console.log('Response length:', text.length);

        // Check if the response is empty
        if (!text || text.trim() === '') {
            throw new Error('Empty response from server');
        }

        // Handle potential JSON parsing errors
        let data;
        try {
            // Check if the response is valid JSON
            if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
                data = JSON.parse(text);
            } else {
                console.error('Invalid JSON response:', text);
                throw new Error('Invalid JSON response from server');
            }
        } catch (parseError) {
            console.error('Error parsing JSON:', parseError);
            throw new Error('Error parsing server response');
        }

        console.log('Timetable data:', data);

        if (data.error) {
            throw new Error(data.error);
        }

        // Clear the timetable
        clearTimetable();

        // Hide class title - we don't want to display it
        const classTitle = document.getElementById('class-title');
        if (classTitle) {
            classTitle.textContent = '';
            classTitle.classList.add('d-none');
        }

        // Process the data and update the timetable
        if (data.data && data.data.length > 0) {
            console.log(`Found ${data.data.length} sessions to display`);
            updateTimetable(data.data);
            document.getElementById('no-classes-message').classList.add('d-none');
        } else {
            console.log('No sessions found for the current filters');
            // Show the no classes message
            document.getElementById('no-classes-message').classList.remove('d-none');
        }
    } catch (error) {
        console.error('Error loading timetable data:', error);
        showErrorMessage(error.message || 'Erreur inconnue');

        // Show the no classes message
        document.getElementById('no-classes-message').classList.remove('d-none');
    }
}

// Function to show error message
function showErrorMessage(message) {
    console.log('Showing error message:', message);

    // First try to use the error-container
    const errorContainer = document.getElementById('error-container');
    if (errorContainer) {
        errorContainer.className = 'alert alert-danger mt-4';
        errorContainer.innerHTML = `
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            Error loading data: ${message}
        `;
        errorContainer.classList.remove('d-none');
        return;
    }

    // If error-container doesn't exist, create a new error element
    const newErrorElement = document.createElement('div');
    newErrorElement.className = 'alert alert-danger mt-4';
    newErrorElement.id = 'dynamic-error-message';
    newErrorElement.innerHTML = `
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        Error loading data: ${message}
    `;

    // Find a suitable container to append the error to
    const container = document.querySelector('.timetable-display-container') ||
                     document.querySelector('.main-content') ||
                     document.body;

    if (container) {
        // Insert after the filters section if it exists
        const filtersSection = document.querySelector('.card.animate-fade-in.mb-4');
        if (filtersSection && filtersSection.parentNode === container) {
            filtersSection.insertAdjacentElement('afterend', newErrorElement);
        } else {
            // Otherwise insert at the beginning of the container
            container.insertAdjacentElement('afterbegin', newErrorElement);
        }
    }
}

// Function to hide error message
function hideErrorMessage() {
    // Hide the main error container if it exists
    const errorContainer = document.getElementById('error-container');
    if (errorContainer) {
        errorContainer.classList.add('d-none');
    }

    // Also remove any dynamically created error messages
    const dynamicError = document.getElementById('dynamic-error-message');
    if (dynamicError) {
        dynamicError.remove();
    }

    // Remove any other alert-danger elements that might have been created
    const otherErrors = document.querySelectorAll('.alert-danger:not(#error-container):not(#dynamic-error-message)');
    otherErrors.forEach(error => error.remove());
}

// Function to show success message
function showSuccessMessage(message) {
    console.log('Showing success message:', message);

    // Hide any existing error messages
    hideErrorMessage();

    // Hide any existing success messages
    hideSuccessMessage();

    // Create a new success element
    const successElement = document.createElement('div');
    successElement.className = 'alert alert-success mt-4';
    successElement.id = 'dynamic-success-message';
    successElement.innerHTML = `
        <i class="bi bi-check-circle-fill me-2"></i>
        ${message}
    `;

    // Find a suitable container to append the success message to
    const container = document.querySelector('.timetable-display-container') ||
                     document.querySelector('.main-content') ||
                     document.body;

    if (container) {
        // Insert after the filters section if it exists
        const filtersSection = document.querySelector('.card.animate-fade-in.mb-4');
        if (filtersSection && filtersSection.parentNode === container) {
            filtersSection.insertAdjacentElement('afterend', successElement);
        } else {
            // Otherwise insert at the beginning of the container
            container.insertAdjacentElement('afterbegin', successElement);
        }
    }

    // Automatically hide the success message after 5 seconds
    setTimeout(() => {
        hideSuccessMessage();
    }, 5000);
}

// Function to hide success message
function hideSuccessMessage() {
    // Remove any dynamically created success messages
    const dynamicSuccess = document.getElementById('dynamic-success-message');
    if (dynamicSuccess) {
        dynamicSuccess.remove();
    }

    // Remove any other alert-success elements that might have been created
    const otherSuccesses = document.querySelectorAll('.alert-success:not(#dynamic-success-message)');
    otherSuccesses.forEach(success => success.remove());
}

// Function to add a session directly to the timetable
function addSessionToTimetable(session) {
    console.log('Adding session to timetable:', session);

    // Find the cell for this day and time
    const day = session.day || session.jour;
    const timeSlot = `${session.heure_debut}-${session.heure_fin}`;

    console.log(`Looking for cell with day=${day}, time=${timeSlot}`);

    const cell = document.querySelector(`td[data-day="${day}"][data-time="${timeSlot}"]`);
    if (!cell) {
        console.error(`Cell not found for day=${day}, time=${timeSlot}`);
        // Try with different time format (e.g., "08h-10h" vs "08:00-10:00")
        const alternativeTimeSlot = formatTimeSlot(timeSlot);
        console.log(`Trying alternative time format: ${alternativeTimeSlot}`);

        const alternativeCell = document.querySelector(`td[data-day="${day}"][data-time="${alternativeTimeSlot}"]`);
        if (!alternativeCell) {
            console.error(`Alternative cell not found either`);
            return;
        }

        // Create the class card
        const classCard = createClassCard(session);

        // Add the card to the cell
        alternativeCell.innerHTML = '';
        alternativeCell.className = 'session-cell';
        alternativeCell.appendChild(classCard);

        console.log('Session added to timetable with alternative time format successfully');
        return;
    }

    // Create the class card
    const classCard = createClassCard(session);

    // Add the card to the cell
    cell.innerHTML = '';
    cell.className = 'session-cell';
    cell.appendChild(classCard);

    console.log('Session added to timetable successfully');
}

// Function to format time slot for compatibility
function formatTimeSlot(timeSlot) {
    if (!timeSlot) return '';

    console.log('Formatting time slot:', timeSlot);

    // Normalize the time slot to handle various formats
    let normalizedTimeSlot = timeSlot;

    // If the time is in format "08:00-10:00", convert to "08h-10h"
    if (timeSlot.includes(':')) {
        // Handle "08:00-10:00" format
        normalizedTimeSlot = timeSlot.replace(/(\d+):00-(\d+):00/g, '$1h-$2h');

        // Handle "08:30-10:30" format (with minutes)
        if (normalizedTimeSlot === timeSlot) {
            normalizedTimeSlot = timeSlot.replace(/(\d+):(\d+)-(\d+):(\d+)/g, function(match, h1, m1, h2, m2) {
                let start = m1 === '00' ? h1 + 'h' : h1 + 'h' + m1;
                let end = m2 === '00' ? h2 + 'h' : h2 + 'h' + m2;
                return start + '-' + end;
            });
        }
    }

    // If the time is in format "08h-10h", convert to "08:00-10:00" for alternative matching
    if (normalizedTimeSlot.includes('h')) {
        // First, check if we're dealing with a format like "08h30-10h30"
        const complexFormat = /(\d+)h(\d+)-(\d+)h(\d+)/;
        if (complexFormat.test(normalizedTimeSlot)) {
            // We'll keep this as is, but also return the simple format for matching
            return normalizedTimeSlot;
        }

        // Simple format like "08h-10h"
        return normalizedTimeSlot;
    }

    console.log('Normalized time slot:', normalizedTimeSlot);
    return normalizedTimeSlot;
}

// Function to clear the timetable
function clearTimetable() {
    console.log('Clearing timetable completely');

    // Get all cells except the time slots
    const cells = document.querySelectorAll('td[data-day][data-time]');

    // Clear each cell
    cells.forEach(cell => {
        cell.innerHTML = '';
        cell.className = 'empty-cell';

        // Remove any data attributes except day and time
        const attributes = Array.from(cell.attributes);
        attributes.forEach(attr => {
            if (attr.name !== 'data-day' && attr.name !== 'data-time' && attr.name.startsWith('data-')) {
                cell.removeAttribute(attr.name);
            }
        });
    });

    // Clear any stored sessions
    clearSessionsFromLocalStorage();

    // Hide the class title
    const classTitle = document.getElementById('class-title');
    if (classTitle) {
        classTitle.textContent = '';
        classTitle.classList.add('d-none');
    }

    // Log the cleared cells
    console.log(`Cleared ${cells.length} cells in the timetable`);
}

// Function removed - was used for testing

// Function to find the best matching cell for a session
function findMatchingCell(day, timeSlot, allTimeFormats) {
    // Translate day name if needed
    const translatedDay = translateDayName(day);

    // Map day to number
    const dayNumber = mapDayToNumber(day);

    console.log(`Finding matching cell for day=${day}, translatedDay=${translatedDay}, dayNumber=${dayNumber}, timeSlot=${timeSlot}`);

    // Try with translated day name
    for (const format of allTimeFormats) {
        const cell = document.querySelector(`td[data-day="${translatedDay}"][data-time="${format}"]`);
        if (cell) {
            console.log(`Found cell with translated day and format: ${format}`);
            return cell;
        }
    }

    // Try with day number
    for (const format of allTimeFormats) {
        const cell = document.querySelector(`td[data-day="${dayNumber}"][data-time="${format}"]`);
        if (cell) {
            console.log(`Found cell with day number and format: ${format}`);
            return cell;
        }
    }

    // Try with original day
    for (const format of allTimeFormats) {
        const cell = document.querySelector(`td[data-day="${day}"][data-time="${format}"]`);
        if (cell) {
            console.log(`Found cell with original day and format: ${format}`);
            return cell;
        }
    }

    // If no exact match, try to find a cell based on the time range
    if (timeSlot.includes('-')) {
        const [startTime, endTime] = timeSlot.split('-');
        console.log(`Trying to match by time range: ${startTime} to ${endTime}`);

        // Extract hours from start and end times
        let startHour, endHour;

        if (startTime.includes('h')) {
            startHour = parseInt(startTime.split('h')[0]);
        } else if (startTime.includes(':')) {
            startHour = parseInt(startTime.split(':')[0]);
        } else {
            startHour = parseInt(startTime);
        }

        if (endTime.includes('h')) {
            endHour = parseInt(endTime.split('h')[0]);
        } else if (endTime.includes(':')) {
            endHour = parseInt(endTime.split(':')[0]);
        } else {
            endHour = parseInt(endTime);
        }

        console.log(`Extracted hours: start=${startHour}, end=${endHour}`);

        // Check for standard time slots
        const standardTimeSlots = {
            '8-10': '08h-10h',
            '10-12': '10h-12h',
            '14-16': '14h-16h',
            '16-18': '16h-18h'
        };

        const key = `${startHour}-${endHour}`;
        if (standardTimeSlots[key]) {
            const standardFormat = standardTimeSlots[key];
            console.log(`Matched to standard time slot: ${standardFormat}`);
            const cell = document.querySelector(`td[data-day="${day}"][data-time="${standardFormat}"]`);
            if (cell) {
                console.log(`Found cell with standard format: ${standardFormat}`);
                return cell;
            }
        }
    }

    // If still no match, try all cells for this day and check if any time format matches approximately
    const dayCells = document.querySelectorAll(`td[data-day="${day}"]`);
    console.log(`Found ${dayCells.length} cells for day ${day}`);

    for (const cell of dayCells) {
        const cellTime = cell.dataset.time;
        console.log(`Checking cell with time ${cellTime}`);

        // Try to extract hours from the cell time
        if (cellTime && cellTime.includes('-')) {
            const [cellStart, cellEnd] = cellTime.split('-');
            let cellStartHour, cellEndHour;

            if (cellStart.includes('h')) {
                cellStartHour = parseInt(cellStart.split('h')[0]);
            } else if (cellStart.includes(':')) {
                cellStartHour = parseInt(cellStart.split(':')[0]);
            }

            if (cellEnd.includes('h')) {
                cellEndHour = parseInt(cellEnd.split('h')[0]);
            } else if (cellEnd.includes(':')) {
                cellEndHour = parseInt(cellEnd.split(':')[0]);
            }

            // If we have a time range for both the session and the cell
            if (typeof startHour !== 'undefined' && typeof endHour !== 'undefined' &&
                typeof cellStartHour !== 'undefined' && typeof cellEndHour !== 'undefined') {

                // Check if the time ranges match approximately
                if (startHour === cellStartHour && endHour === cellEndHour) {
                    console.log(`Found matching cell with approximate time range: ${cellTime}`);
                    return cell;
                }
            }
        }
    }

    console.log('No matching cell found');
    return null;
}

// Function to update the timetable with class data
function updateTimetable(classes) {
    console.log('Updating timetable with', classes.length, 'classes');

    // Debug: Log all cells in the timetable
    const allCells = document.querySelectorAll('td[data-day][data-time]');
    console.log(`Found ${allCells.length} cells in the timetable`);
    console.log('Available cells:');
    allCells.forEach(cell => {
        console.log(`Cell: day=${cell.dataset.day}, time=${cell.dataset.time}`);
    });

    // Process each class
    classes.forEach(classData => {
        // Get the day and time (handle both jour and day fields for backward compatibility)
        let day = classData.jour || classData.day;

        // Log the original day value
        console.log(`Original day value: ${day}, type: ${typeof day}`);

        // Translate day name if needed
        const translatedDay = translateDayName(day);

        // Map day to number
        const dayNumber = mapDayToNumber(day);

        console.log(`Day translations: original=${day}, translated=${translatedDay}, number=${dayNumber}`);

        // Use the translated day for further processing
        day = translatedDay;

        let timeSlot = '';

        // Format the time slot
        if (classData.standard_time_slot) {
            // Use the standard time slot if available
            timeSlot = classData.standard_time_slot;
            console.log(`Using standard time slot: ${timeSlot}`);
        } else if (classData.heure_debut && classData.heure_fin) {
            timeSlot = `${classData.heure_debut}-${classData.heure_fin}`;
        } else if (classData.time) {
            timeSlot = classData.time;
        }

        console.log(`Looking for cell with day=${day}, time=${timeSlot} for class:`, classData);
        console.log(`Class details: id=${classData.id_seance}, module=${classData.nom_module || classData.module}, day=${day}, time=${timeSlot}`);

        // Try different formats for the time slot
        const timeFormats = [
            timeSlot,
            formatTimeSlot(timeSlot),
            timeSlot.replace(/(\d+)h/g, '$1:00').replace(/(\d+):(\d+)h/g, '$1:$2'),  // Convert "08h-10h" to "08:00-10:00"
            timeSlot.replace(/(\d+):(\d+)/g, '$1h$2').replace(/(\d+):00/g, '$1h'),   // Convert "08:00-10:00" to "08h-10h"

            // Add more specific formats
            classData.heure_debut + '-' + classData.heure_fin,  // Direct format from database

            // Try standard time slots
            '08h-10h', '10h-12h', '14h-16h', '16h-18h'
        ];

        // Log the time formats we're trying
        console.log('Original time slot:', timeSlot);
        console.log('Trying time formats:', timeFormats);

        // Use the findMatchingCell function to find the best matching cell
        const cell = findMatchingCell(day, timeSlot, timeFormats);

        if (cell) {
            console.log(`Cell found for day=${day}, time=${timeSlot}`);

            // Remove the empty-cell class
            cell.classList.remove('empty-cell');
            cell.className = 'session-cell';

            // Clear any existing content
            cell.innerHTML = '';

            // Create and append the class card
            const classCard = createClassCard(classData);
            cell.appendChild(classCard);

            console.log('Class card added to cell');
        } else {
            console.warn(`Cell not found for day ${day} and time ${timeSlot} after trying all formats`);
            console.warn('Class data that could not be placed:', classData);

            // Try a direct approach for standard time slots
            if (classData.heure_debut && classData.heure_fin) {
                // Extract hours
                let startHour, endHour;

                if (classData.heure_debut.includes('h')) {
                    startHour = parseInt(classData.heure_debut.split('h')[0]);
                } else if (classData.heure_debut.includes(':')) {
                    startHour = parseInt(classData.heure_debut.split(':')[0]);
                } else {
                    startHour = parseInt(classData.heure_debut);
                }

                if (classData.heure_fin.includes('h')) {
                    endHour = parseInt(classData.heure_fin.split('h')[0]);
                } else if (classData.heure_fin.includes(':')) {
                    endHour = parseInt(classData.heure_fin.split(':')[0]);
                } else {
                    endHour = parseInt(classData.heure_fin);
                }

                console.warn(`Direct approach - extracted hours: start=${startHour}, end=${endHour}`);

                // Map to standard time slots based on start hour
                let standardTimeSlot = null;
                if (startHour === 8) standardTimeSlot = '08h-10h';
                else if (startHour === 10) standardTimeSlot = '10h-12h';
                else if (startHour === 14) standardTimeSlot = '14h-16h';
                else if (startHour === 16) standardTimeSlot = '16h-18h';

                if (standardTimeSlot) {
                    console.warn(`Mapped to standard time slot: ${standardTimeSlot}`);
                    const standardCell = document.querySelector(`td[data-day="${day}"][data-time="${standardTimeSlot}"]`);

                    if (standardCell) {
                        console.warn(`Found standard cell with day=${day}, time=${standardTimeSlot}`);

                        // Remove the empty-cell class
                        standardCell.classList.remove('empty-cell');
                        standardCell.className = 'session-cell';

                        // Clear any existing content
                        standardCell.innerHTML = '';

                        // Create and append the class card
                        const classCard = createClassCard(classData);
                        standardCell.appendChild(classCard);

                        console.log('Class card added to cell with standard time slot mapping');
                    }
                }
            }
        }
    });
}

// Function to set up empty cell click handlers
function setupEmptyCellClickHandlers() {
    // Get all empty cells
    const emptyCells = document.querySelectorAll('td[data-day][data-time]');

    // Add click event to each cell
    emptyCells.forEach(cell => {
        cell.addEventListener('click', () => {
            // Only open the modal if the cell is empty
            if (cell.classList.contains('empty-cell')) {
                const day = cell.dataset.day;
                const timeSlot = cell.dataset.time;
                openAddSessionModal(day, timeSlot);
            }
        });
    });
}

// Function to store a session in localStorage
function storeSessionInLocalStorage(session) {
    try {
        console.log('Storing session in localStorage:', session);

        // Get existing sessions from localStorage
        const storedSessions = getSessionsFromLocalStorage();

        // Add the new session
        storedSessions.push(session);

        // Store back in localStorage
        localStorage.setItem('timetableSessions', JSON.stringify(storedSessions));

        console.log('Session stored in localStorage successfully');
    } catch (error) {
        console.error('Error storing session in localStorage:', error);
    }
}

// Function to get sessions from localStorage
function getSessionsFromLocalStorage() {
    try {
        const storedSessions = localStorage.getItem('timetableSessions');
        if (storedSessions) {
            return JSON.parse(storedSessions);
        }
    } catch (error) {
        console.error('Error getting sessions from localStorage:', error);
    }
    return [];
}

// Function to clear sessions from localStorage
function clearSessionsFromLocalStorage() {
    try {
        localStorage.removeItem('timetableSessions');
        console.log('Sessions cleared from localStorage');
    } catch (error) {
        console.error('Error clearing sessions from localStorage:', error);
    }
}

// Function to restore sessions from localStorage
function restoreSessionsFromLocalStorage() {
    try {
        const storedSessions = getSessionsFromLocalStorage();
        if (storedSessions && storedSessions.length > 0) {
            console.log(`Restoring ${storedSessions.length} sessions from localStorage`);

            // Add each session to the timetable
            storedSessions.forEach(session => {
                addSessionToTimetable(session);
            });

            console.log('Sessions restored from localStorage successfully');
        } else {
            console.log('No sessions found in localStorage');
        }
    } catch (error) {
        console.error('Error restoring sessions from localStorage:', error);
    }
}

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    console.log('Timetable view page loaded');

    // Clear any existing data first
    clearTimetable();
    clearSessionsFromLocalStorage();

    // Load the timetable data with a fresh request
    loadTimetableData(true);

    // Set up empty cell click handlers
    setupEmptyCellClickHandlers();

    // Event listeners for testing buttons removed

    // Set up the add session button
    const addSessionBtn = document.getElementById('add-session-btn');
    if (addSessionBtn) {
        addSessionBtn.addEventListener('click', () => {
            openAddSessionModal('', '');
        });
    }

    // Set up the download PDF button
    const downloadPdfBtn = document.getElementById('download-pdf-btn');
    if (downloadPdfBtn) {
        downloadPdfBtn.addEventListener('click', () => {
            console.log('Download PDF button clicked');
            // The actual PDF generation is handled in timetable-pdf.js
        });
    }

    // Set up the save session button
    const saveSessionBtn = document.getElementById('saveSessionBtn');
    if (saveSessionBtn) {
        saveSessionBtn.addEventListener('click', addSession);
    }

    // Set up the update session button
    const updateSessionBtn = document.getElementById('updateSessionBtn');
    if (updateSessionBtn) {
        updateSessionBtn.addEventListener('click', updateSession);
    }

    // Set up the delete session button
    const deleteSessionBtn = document.getElementById('deleteSessionBtn');
    if (deleteSessionBtn) {
        deleteSessionBtn.addEventListener('click', deleteSession);
    }
});