<?php
/**
 * Controller for managing department modules
 * This controller handles the logic for displaying modules and teaching units by department
 */

// Include the model
require_once __DIR__ . '/../model/listerUEdepartModel.php';
require_once __DIR__ . '/../model/visitsModel.php';

/**
 * Get department information by ID
 *
 * @param int $departmentId The department ID
 * @return array Department information or error
 */
function getDepartmentInfo($departmentId) {
    if (!$departmentId) {
        return null;
    }

    return getDepartementById($departmentId);
}

/**
 * Get all filières for a department
 *
 * @param int $departmentId The department ID
 * @return array List of filières or error
 */
function getDepartmentFilieres($departmentId) {
    if (!$departmentId) {
        return ["error" => "No department ID provided"];
    }

    return getFilieresByDepartement($departmentId);
}

/**
 * Get all modules grouped by filière for a department
 *
 * @param int $departmentId The department ID
 * @return array Modules grouped by filière or error
 */
function getDepartmentModules($departmentId) {
    if (!$departmentId) {
        return ["error" => "No department ID provided"];
    }

    return getModulesByDepartement($departmentId);
}

/**
 * Get teaching units for a specific module
 *
 * @param int $moduleId The module ID
 * @return array Teaching units or error
 */
function getModuleUnits($moduleId) {
    if (!$moduleId) {
        return ["error" => "No module ID provided"];
    }

    return getUniteEnseignementByModule($moduleId);
}

/**
 * Record a visit to the department modules page
 *
 * @param string $userRole The user role
 * @return void
 */
function recordDepartmentModulesVisit($userRole) {
    recordVisit($userRole, 'listerUEdepart');
}

/**
 * Process AJAX request for filières by department
 *
 * @param int $departmentId The department ID
 * @return array JSON response
 */
function processFilieresByDepartmentRequest($departmentId) {
    header('Content-Type: application/json');

    if (!$departmentId) {
        echo json_encode(["success" => false, "message" => "No department ID provided"]);
        exit;
    }

    $filieres = getFilieresByDepartement($departmentId);

    if (isset($filieres['error'])) {
        echo json_encode(["success" => false, "message" => $filieres['error']]);
        exit;
    }

    echo json_encode(["success" => true, "data" => $filieres]);
    exit;
}

/**
 * Process AJAX request for modules by department
 *
 * @param int $departmentId The department ID
 * @return array JSON response
 */
function processModulesByDepartmentRequest($departmentId) {
    header('Content-Type: application/json');

    if (!$departmentId) {
        echo json_encode(["success" => false, "message" => "No department ID provided"]);
        exit;
    }

    $modulesByFiliere = getModulesByDepartement($departmentId);

    if (isset($modulesByFiliere['error'])) {
        echo json_encode(["success" => false, "message" => $modulesByFiliere['error']]);
        exit;
    }

    echo json_encode(["success" => true, "data" => $modulesByFiliere]);
    exit;
}

/**
 * Process AJAX request for teaching units by module
 *
 * @param int $moduleId The module ID
 * @return array JSON response
 */
function processUnitsByModuleRequest($moduleId) {
    header('Content-Type: application/json');

    if (!$moduleId) {
        echo json_encode(["success" => false, "message" => "No module ID provided"]);
        exit;
    }

    $units = getUniteEnseignementByModule($moduleId);

    if (isset($units['error'])) {
        echo json_encode(["success" => false, "message" => $units['error']]);
        exit;
    }

    echo json_encode(["success" => true, "data" => $units]);
    exit;
}

// Handle AJAX requests
if (isset($_GET['action'])) {
    // AJAX request for filières
    if ($_GET['action'] === 'getFilieres' && isset($_GET['departmentId'])) {
        processFilieresByDepartmentRequest($_GET['departmentId']);
    }

    // AJAX request for modules
    if ($_GET['action'] === 'getModules' && isset($_GET['department_id'])) {
        processModulesByDepartmentRequest($_GET['department_id']);
    }

    // AJAX request for teaching units
    if ($_GET['action'] === 'getUnits' && isset($_GET['module_id'])) {
        processUnitsByModuleRequest($_GET['module_id']);
    }
}
