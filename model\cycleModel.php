<?php
require_once __DIR__ . "/../config/db.php";

/**
 * Get all cycles
 *
 * @return array Array of cycles
 */
function getAllCycles() {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $sql = "SELECT * FROM cycle ORDER BY id";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ["error" => "Error fetching cycles: " . $error];
    }

    $cycles = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $cycles[] = $row;
    }

    mysqli_close($conn);
    return $cycles;
}

/**
 * Get cycle by ID
 *
 * @param int $id Cycle ID
 * @return array|null Cycle data or error array
 */
function getCycleById($id) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);
    $sql = "SELECT * FROM cycle WHERE id = '$id'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ["error" => "Error fetching cycle: " . $error];
    }

    if (mysqli_num_rows($result) > 0) {
        $cycle = mysqli_fetch_assoc($result);
        mysqli_close($conn);
        return $cycle;
    } else {
        mysqli_close($conn);
        return ["error" => "No cycle found with this ID"];
    }
}
?>
