<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';

require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../model/notificationsModel.php';
require_once __DIR__ . '/../../controller/notificationsController.php';

// Handle AJAX requests for receiver data
if (isset($_GET['action']) && $_GET['action'] === 'get_receivers') {
    // Get the type of receivers to fetch
    $type = isset($_GET['type']) ? $_GET['type'] : 'all';

    $response = [
        'success' => true,
        'data' => []
    ];

    // Connect to the database
    $conn = getConnection();

    // Fetch students if requested
    if ($type === 'all' || $type === 'students') {
        $studentsResult = $conn->query("SELECT id_etudiant, nom, prenom FROM etudiant LIMIT 20");
        $students = [];

        if ($studentsResult && $studentsResult->num_rows > 0) {
            while ($student = $studentsResult->fetch_assoc()) {
                $students[] = [
                    'id' => $student['id_etudiant'],
                    'name' => $student['prenom'] . ' ' . $student['nom']
                ];
            }
        }

        $response['data']['students'] = $students;
    }

    // Fetch teachers if requested
    if ($type === 'all' || $type === 'teachers') {
        $teachersResult = $conn->query("SELECT id_enseignant, nom, prenom FROM enseignant LIMIT 20");
        $teachers = [];

        if ($teachersResult && $teachersResult->num_rows > 0) {
            while ($teacher = $teachersResult->fetch_assoc()) {
                $teachers[] = [
                    'id' => $teacher['id_enseignant'],
                    'name' => $teacher['prenom'] . ' ' . $teacher['nom']
                ];
            }
        }

        $response['data']['teachers'] = $teachers;
    }

    // Fetch users if requested
    if ($type === 'all' || $type === 'users') {
        $usersResult = $conn->query("SELECT id_user, username, role FROM users LIMIT 20");
        $users = [];

        if ($usersResult && $usersResult->num_rows > 0) {
            while ($user = $usersResult->fetch_assoc()) {
                $users[] = [
                    'id' => $user['id_user'],
                    'username' => $user['username'] ?? 'N/A',
                    'role' => $user['role'] ?? 'N/A'
                ];
            }
        }

        $response['data']['users'] = $users;
    }

    // Return the data as JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Get unread count
$unreadCount = getUnreadNotificationsCount();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniAdmin - Notifications</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/messages.css">
</head>
<body>
    <div class="dashboards-container">

        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="messages-content">
                <div class="messages-header">
                    <h1>NOTIFICATIONS</h1>
                    <div class="messages-actions">
                        <button id="createNotificationBtn" class="create-message-btn">Create Notification</button>
                        <button id="markAllBtn" class="mark-all-btn">Mark all as read</button>
                    </div>
                </div>



                <div id="notificationsList" class="notifications-list">
                    <!-- Notifications will be rendered dynamically via JS -->
                </div>

                <!-- Create Notification Modal -->
                <div class="modal fade" id="createNotificationModal" tabindex="-1" aria-labelledby="createNotificationModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="createNotificationModalLabel">Create New Notification</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="createNotificationForm">
                                    <div class="mb-3">
                                        <label for="notificationTitle" class="form-label">Title</label>
                                        <input type="text" class="form-control" id="notificationTitle" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="notificationType" class="form-label">Type</label>
                                        <select class="form-select" id="notificationType" required>
                                            <option value="message">Message</option>
                                            <option value="assignment">Assignment</option>
                                            <option value="meeting">Meeting</option>
                                            <option value="system">System</option>
                                            <option value="event">Event</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="notificationContent" class="form-label">Notification Content</label>
                                        <textarea class="form-control" id="notificationContent" rows="4" required></textarea>
                                    </div>

                                    <!-- Toolbar for media URL and file path -->
                                    <div class="mb-3">
                                        <div class="message-toolbar">
                                            <button type="button" class="btn btn-icon" id="toggleMediaUrlBtn" title="Add Media URL">
                                                <i class="fas fa-link"></i>
                                            </button>
                                            <button type="button" class="btn btn-icon" id="toggleFilePathBtn" title="Add File Path">
                                                <i class="fas fa-paperclip"></i>
                                            </button>
                                        </div>

                                        <!-- Media URL input -->
                                        <div class="collapse mb-2" id="mediaUrlCollapse">
                                            <div class="input-group">
                                                <input type="url" class="form-control" id="mediaUrlInput" placeholder="Enter media URL (https://example.com/media)">
                                                <button class="btn btn-primary" type="button" id="addMediaUrlToNotificationBtn"><i class="fas fa-check"></i></button>
                                                <button class="btn btn-light" type="button" id="cancelMediaUrlBtn"><i class="fas fa-times"></i></button>
                                            </div>
                                        </div>

                                        <!-- File Upload input -->
                                        <div class="collapse mb-2" id="filePathCollapse">
                                            <div class="input-group">
                                                <input type="file" class="form-control" id="fileInput">
                                                <button class="btn btn-primary" type="button" id="addFileToNotificationBtn"><i class="fas fa-check"></i></button>
                                                <button class="btn btn-light" type="button" id="cancelFilePathBtn"><i class="fas fa-times"></i></button>
                                            </div>
                                        </div>

                                        <!-- Attachment indicators -->
                                        <div id="attachmentIndicators"></div>
                                    </div>

                                    <!-- Hidden fields for media URL and file path -->
                                    <input type="hidden" id="mediaUrl">
                                    <input type="hidden" id="filePath">
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="sendNotificationBtn">Send Notification</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification Detail Modal -->
                <div class="modal fade" id="notificationDetailModal" tabindex="-1" aria-labelledby="notificationDetailModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="notificationDetailModalLabel">Notification Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="notificationDetailContent">
                                <!-- Le contenu sera injecté dynamiquement -->
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Success Modal -->
                <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-success text-white">
                                <h5 class="modal-title" id="successModalLabel"><i class="fas fa-check-circle me-2"></i>Succès</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                </div>
                                <p class="text-center fs-5" id="successMessage">Notification envoyée avec succès!</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-success" data-bs-dismiss="modal">OK</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Confirmation Modal -->
                <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-white">
                                <h5 class="modal-title" id="confirmationModalLabel"><i class="fas fa-exclamation-triangle me-2"></i>Confirmation</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <i class="fas fa-question-circle text-warning" style="font-size: 4rem;"></i>
                                </div>
                                <p class="text-center fs-5" id="confirmationMessage">Êtes-vous sûr de vouloir effectuer cette action?</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-warning" id="confirmActionBtn">Confirmer</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="../assets/js/notifications.js"></script>
    <script src="../assets/js/sidebar.js"></script>
</body>
</html>