<?php
require_once "../model/staffModel.php";
require_once "../utils/response.php";

// Afficher tous les membres du personnel
function getAllStaffAPI() {
    $staff = getAllStaff();
    
    if (isset($staff['error'])) {
        jsonResponse(['error' => $staff['error']], 404);
    }
    
    jsonResponse(['data' => $staff], 200);
}

// Afficher un membre du personnel par CNI
function getStaffByCNIAPI($cni) {
    $staff = getStaffByCNI($cni);
    
    if (isset($staff['error'])) {
        jsonResponse(['error' => $staff['error']], 404);
    }
    
    jsonResponse(['data' => $staff], 200);
}

// Créer un nouveau membre du personnel
function createStaffAPI() {
    $json = file_get_contents("php://input");
    $data = json_decode($json, true);

    if (!$data) {
        jsonResponse(['error' => 'Données JSON invalides'], 400);
    }

    $requiredFields = ['CNI', 'nom', 'prenom', 'phone', 'sexe', 'role'];
    
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            jsonResponse(['error' => "Le champ $field est obligatoire"], 400);
        }
    }
    
    $result = insertStaff(
        $data['CNI'],
        $data['nom'],
        $data['prenom'],
        $data['phone'],
        $data['sexe'],
        $data['role']
    );
    
    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
    } else if (isset($result['success'])) {
        jsonResponse(['message' => 'Membre du personnel créé avec succès', 'CNI' => $data['CNI']], 201);
    } else {
        jsonResponse(['error' => 'Erreur inattendue lors de la création du membre du personnel'], 500);
    }
}

// Mettre à jour un membre du personnel
function updateStaffAPI($cni) {
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!$data) {
        jsonResponse(['error' => 'Données JSON invalides'], 400);
    }

    $requiredFields = ['nom', 'prenom', 'phone', 'sexe', 'role'];
    
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            jsonResponse(['error' => "Le champ $field est obligatoire"], 400);
        }
    }

    $result = updateStaff(
        $cni,
        $data['nom'],
        $data['prenom'],
        $data['phone'],
        $data['sexe'],
        $data['role']
    );

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
    } else if (isset($result['success'])) {
        jsonResponse(['message' => 'Les informations du membre du personnel ont été mises à jour avec succès'], 200);
    } else {
        jsonResponse(['error' => 'Erreur inattendue lors de la mise à jour des informations du membre du personnel'], 500);
    }
}

// Supprimer un membre du personnel
function deleteStaffAPI($cni) {
    $staff = getStaffByCNI($cni);
    
    if (isset($staff['error'])) {
        jsonResponse(['error' => $staff['error']], 404);
    }
    
    $result = deleteStaffByCNI($cni);
    
    if ($result) {
        jsonResponse(['message' => 'Membre du personnel supprimé avec succès'], 200);
    } else {
        jsonResponse(['error' => 'Erreur lors de la suppression du membre du personnel'], 500);
    }
}

// Obtenir les rôles disponibles
function getAvailableRolesAPI() {
    $roles = getAvailableRoles();
    jsonResponse(['data' => $roles], 200);
}
?>