// Student Grades JavaScript

document.addEventListener('DOMContentLoaded', function() {
    console.log('Student Grades page loaded');

    // Utiliser les variables définies dans le PHP si disponibles
    // Ces variables sont définies dans student_grades.php
    let filiereId, niveauId, semestre, session, moduleId, filiereName, niveauName, moduleName;

    // Vérifier si les variables PHP sont disponibles
    if (typeof urlFiliereId !== 'undefined') {
        console.log('Utilisation des variables PHP pour les paramètres');

        // Récupérer les paramètres depuis les variables PHP
        filiereId = urlFiliereId;
        niveauId = urlNiveauId;
        semestre = urlSemestre;
        session = urlSession;
        moduleId = urlModuleId;
        filiereName = urlFiliereName;
        niveauName = urlNiveauName;
        moduleName = urlModuleName;

        // Vérifier si les paramètres sont des chaînes vides
        if (filiereId === '') filiereId = null;
        if (niveauId === '') niveauId = null;
        if (semestre === '') semestre = null;
        if (session === '') session = null;
        if (moduleId === '') moduleId = null;
        if (filiereName === '') filiereName = null;
        if (niveauName === '') niveauName = null;
        if (moduleName === '') moduleName = null;
    } else {
        // Fallback: récupérer depuis l'URL si les variables PHP ne sont pas disponibles
        console.log('Variables PHP non disponibles, récupération depuis l\'URL');
        const urlParams = new URLSearchParams(window.location.search);

        // Récupérer et décoder les paramètres de l'URL
        filiereId = urlParams.get('filiere') ? decodeURIComponent(urlParams.get('filiere')) : null;
        niveauId = urlParams.get('niveau') ? decodeURIComponent(urlParams.get('niveau')) : null;
        semestre = urlParams.get('semestre') ? decodeURIComponent(urlParams.get('semestre')) : null;
        session = urlParams.get('session') ? decodeURIComponent(urlParams.get('session')) : null;
        moduleId = urlParams.get('module') ? decodeURIComponent(urlParams.get('module')) : null;
        filiereName = urlParams.get('filiere_name') ? decodeURIComponent(urlParams.get('filiere_name')) : null;
        niveauName = urlParams.get('niveau_name') ? decodeURIComponent(urlParams.get('niveau_name')) : null;
        moduleName = urlParams.get('module_name') ? decodeURIComponent(urlParams.get('module_name')) : null;

        // Vérifier si les paramètres sont des chaînes vides
        if (filiereId === '') filiereId = null;
        if (niveauId === '') niveauId = null;
        if (semestre === '') semestre = null;
        if (session === '') session = null;
        if (moduleId === '') moduleId = null;
        if (filiereName === '') filiereName = null;
        if (niveauName === '') niveauName = null;
        if (moduleName === '') moduleName = null;
    }

    // Vérifier que les paramètres sont bien définis
    const parametresManquants = [];
    if (!filiereId) parametresManquants.push('filiere');
    if (!niveauId) parametresManquants.push('niveau');
    if (!semestre) parametresManquants.push('semestre');
    if (!session) parametresManquants.push('session');
    if (!moduleId) parametresManquants.push('module');

    if (parametresManquants.length > 0) {
        console.error('Paramètres manquants après récupération:', parametresManquants.join(', '));
    }

    // Log the parameters for debugging
    console.log('Paramètres finaux utilisés:', {
        filiereId,
        niveauId,
        semestre,
        session,
        moduleId,
        filiereName,
        niveauName,
        moduleName
    });

    // Afficher les valeurs des filtres dans les cartes
    setTimeout(function() {
        // Mettre à jour les valeurs des filtres dans les cartes en utilisant les IDs
        if (filiereName) document.getElementById('filiere-value').textContent = filiereName;
        if (niveauName) document.getElementById('niveau-value').textContent = niveauName;
        if (urlSemestreName) document.getElementById('semestre-value').textContent = urlSemestreName;
        if (session) document.getElementById('session-value').textContent = session;
        if (moduleName) document.getElementById('module-value').textContent = moduleName;

        console.log('Valeurs des filtres mises à jour dans les cartes');
    }, 500);

    // Pagination variables
    let currentPage = 1;
    const rowsPerPage = 10;
    let totalStudents = 0;
    let allStudents = [];

    // Load student grades
    loadStudentGrades();

    // Search functionality
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterStudents(this.value);
        });
    }

    // PDF Download functionality
    const downloadBtn = document.getElementById('download-pdf');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            generatePDF(true);
        });
    }

    // Send to coordinator functionality
    const sendToCoordinatorBtn = document.getElementById('send-to-coordinator');
    if (sendToCoordinatorBtn) {
        sendToCoordinatorBtn.addEventListener('click', sendPDFToCoordinator);
    }

    // Save All Grades functionality
    const saveAllBtn = document.getElementById('save-all-grades');
    if (saveAllBtn) {
        saveAllBtn.addEventListener('click', saveAllGrades);
    }

    // Function to load student grades from the server
    function loadStudentGrades() {
        // Show loading state
        document.getElementById('students-list').innerHTML = '<tr><td colspan="6" class="text-center">Chargement des données...</td></tr>';

        // Vérifier que tous les paramètres nécessaires sont disponibles
        if (!moduleId || !niveauId || !filiereId || !semestre || !session) {
            console.error('Paramètres manquants dans loadStudentGrades:', {
                moduleId, niveauId, filiereId, semestre, session
            });

            // Afficher un message d'erreur dans le tableau
            document.getElementById('students-list').innerHTML = '<tr><td colspan="6" class="text-center text-danger">Erreur: Paramètres de filtrage incomplets</td></tr>';
            document.getElementById('no-students-message').style.display = 'block';
            document.getElementById('no-students-message').textContent = 'Erreur: Paramètres de filtrage incomplets. Veuillez retourner à la page de filtrage et réessayer.';

            // Ajouter un bouton pour retourner à la page de filtrage
            const returnButton = document.createElement('button');
            returnButton.className = 'btn btn-primary mt-3';
            returnButton.innerHTML = '<i class="bi bi-arrow-left me-2"></i>Retour à la page de filtrage';
            returnButton.onclick = function() {
                window.location.href = 'upload_grades.php';
            };
            document.getElementById('no-students-message').appendChild(returnButton);

            return; // Arrêter l'exécution de la fonction
        }

        console.log('Paramètres utilisés pour charger les notes:', { moduleId, niveauId, filiereId, semestre, session });

        // Prepare the request data
        const requestData = {
            filiere: filiereId,
            niveau: niveauId,
            semestre: semestre,
            session: session,
            module: moduleId,
            action: 'get_student_grades'
        };

        console.log('Requête pour charger les notes des étudiants:', requestData);

        // Show loading notification
        showNotification('Chargement des données en cours...', 'info');

        // Make AJAX request to get student grades
        $.ajax({
            url: '../../route/noteRoute.php',
            type: 'GET',
            data: requestData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Store all students
                    allStudents = response.data;

                    // Log the raw data for debugging
                    console.log('Raw student data from API:', JSON.stringify(allStudents));

                    // Validate and process student data
                    allStudents = allStudents.filter(student => {
                        // Check if student has required fields
                        if (!student.nom || !student.prenom) {
                            console.warn('Skipping student with missing name data:', student);
                            return false;
                        }

                        // Ensure id_etudiant is present
                        if (!student.id_etudiant) {
                            console.warn('Student missing id_etudiant, trying to find alternative:', student);
                            if (student.id) {
                                student.id_etudiant = student.id;
                                console.log('Using student.id as id_etudiant:', student.id);
                            } else {
                                console.error('Cannot find valid ID for student:', student);
                                return false;
                            }
                        }

                        // IMPORTANT: Ensure has_grade is a boolean
                        if (typeof student.has_grade === 'string') {
                            student.has_grade = student.has_grade === '1' || student.has_grade === 'true';
                        } else if (typeof student.has_grade === 'number') {
                            student.has_grade = student.has_grade === 1;
                        }

                        // Check if student has a valid grade value
                        const hasValidValue = student.valeur !== null &&
                                             student.valeur !== undefined &&
                                             student.valeur !== '' &&
                                             !isNaN(parseFloat(student.valeur));

                        // IMPORTANT: Always check the actual value regardless of has_grade flag
                        // If student has a valid grade value, ensure has_grade is true
                        if (hasValidValue) {
                            student.has_grade = true;
                            // Format to 2 decimal places
                            const numValue = parseFloat(student.valeur);
                            student.valeur = numValue.toFixed(2);
                            // Ensure the value is displayed with 2 decimal places
                            if (student.valeur.indexOf('.') === -1) {
                                student.valeur += '.00';
                            } else if (student.valeur.split('.')[1].length === 1) {
                                student.valeur += '0';
                            }
                            console.log(`Student ${student.nom} ${student.prenom} has grade: ${student.valeur}`);
                        } else {
                            // If no valid value, ensure valeur is empty and has_grade is false
                            student.valeur = '';
                            student.has_grade = false;
                        }

                        // Log the student data for debugging
                        console.log(`Processed student data for ${student.nom} ${student.prenom}:`, {
                            id_etudiant: student.id_etudiant,
                            has_grade: student.has_grade,
                            valeur: student.valeur,
                            hasValidValue: hasValidValue
                        });

                        return true;
                    });

                    // Log how many students have grades
                    const studentsWithGrades = allStudents.filter(student => student.has_grade).length;
                    console.log(`Loaded ${allStudents.length} students, ${studentsWithGrades} with grades`);

                    // If we have students with grades, show a success message
                    if (studentsWithGrades > 0) {
                        showNotification(`${studentsWithGrades} étudiants avec des notes chargés avec succès`, 'success');
                    }

                    totalStudents = allStudents.length;

                    console.log('Loaded and validated students:', allStudents);

                    // Update top performers
                    updateTopPerformers(allStudents);

                    // Display students with pagination
                    displayStudents(allStudents, currentPage);

                    // Update pagination
                    updatePagination();

                    // Update total students count
                    document.getElementById('total-students').textContent = `Total: ${totalStudents} étudiants`;

                    // Show/hide no students message
                    if (totalStudents === 0) {
                        document.getElementById('no-students-message').style.display = 'block';
                        document.getElementById('no-students-message').textContent = 'Aucun étudiant trouvé pour les filtres sélectionnés';
                    } else {
                        document.getElementById('no-students-message').style.display = 'none';
                        showNotification(`${totalStudents} étudiants chargés avec succès`, 'success');
                    }
                } else {
                    // Show error message
                    document.getElementById('students-list').innerHTML = '<tr><td colspan="6" class="text-center text-danger">Erreur lors du chargement des données</td></tr>';
                    document.getElementById('no-students-message').style.display = 'block';
                    document.getElementById('no-students-message').textContent = response.message || 'Erreur lors du chargement des données';
                    showNotification(response.message || 'Erreur lors du chargement des données', 'error');
                }
            },
            error: function(xhr, status, error) {
                // Try to parse the response to get more detailed error information
                let errorMessage = 'Erreur de connexion au serveur: ' + error;
                try {
                    const responseJson = JSON.parse(xhr.responseText);
                    if (responseJson && responseJson.error) {
                        errorMessage = 'Erreur: ' + responseJson.error;
                    }
                } catch (e) {
                    // If parsing fails, use the raw response text if available
                    if (xhr.responseText) {
                        errorMessage = 'Erreur: ' + xhr.responseText;
                    }
                }

                // Log detailed error information
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);

                // Show error message
                document.getElementById('students-list').innerHTML = `<tr><td colspan="6" class="text-center text-danger">${errorMessage}</td></tr>`;
                document.getElementById('no-students-message').style.display = 'block';
                document.getElementById('no-students-message').textContent = errorMessage;
                showNotification(errorMessage, 'error');
            }
        });
    }

    // Function to update top performers section
    function updateTopPerformers(students) {
        // Filter students with valid grades
        const studentsWithGrades = students.filter(student => {
            // First check if student has a valid grade value
            const hasValidValue = student.valeur !== null &&
                                 student.valeur !== undefined &&
                                 student.valeur !== '' &&
                                 !isNaN(parseFloat(student.valeur));

            // Ensure has_grade is a boolean
            if (typeof student.has_grade === 'string') {
                student.has_grade = student.has_grade === '1' || student.has_grade === 'true';
            } else if (typeof student.has_grade === 'number') {
                student.has_grade = student.has_grade === 1;
            }

            // Include student if they have has_grade=true AND a valid grade value
            const hasGrade = student.has_grade === true && hasValidValue && parseFloat(student.valeur) > 0;

            // Log for debugging
            if (hasGrade) {
                console.log(`Including student ${student.nom} ${student.prenom} in top performers with grade ${student.valeur}`);
            }

            return hasGrade;
        });

        // Sort students by grade in descending order
        const sortedStudents = [...studentsWithGrades].sort((a, b) => {
            const gradeA = parseFloat(a.valeur) || 0;
            const gradeB = parseFloat(b.valeur) || 0;
            return gradeB - gradeA;
        });

        // Get top 3 students (or less if there are fewer students)
        const topStudents = sortedStudents.slice(0, 3);

        // Get the top-students container
        const topStudentsContainer = document.querySelector('.top-students');

        // Clear existing content
        topStudentsContainer.innerHTML = '';

        // Add top students
        if (topStudents.length > 0) {
            topStudents.forEach((student, index) => {
                // Format the grade to 2 decimal places
                let formattedGrade = parseFloat(student.valeur).toFixed(2);
                // Ensure the value is displayed with 2 decimal places
                if (formattedGrade.indexOf('.') === -1) {
                    formattedGrade += '.00';
                } else if (formattedGrade.split('.')[1].length === 1) {
                    formattedGrade += '0';
                }

                const studentElement = document.createElement('div');
                studentElement.className = 'top-student';
                studentElement.innerHTML = `
                    <div class="student-rank">${index + 1}</div>
                    <div class="student-info">
                        <div class="student-name">${student.nom} ${student.prenom}</div>
                        <div class="student-grade">${formattedGrade}</div>
                    </div>
                `;
                topStudentsContainer.appendChild(studentElement);
            });
        } else {
            // If no students with grades, show a message
            topStudentsContainer.innerHTML = '<div class="no-top-students">Aucun étudiant avec des notes</div>';
        }
    }

    // Function to display students with pagination
    function displayStudents(students, page) {
        const tableBody = document.getElementById('students-list');
        tableBody.innerHTML = '';

        // Calculate start and end indices for current page
        const startIndex = (page - 1) * rowsPerPage;
        const endIndex = Math.min(startIndex + rowsPerPage, students.length);

        // Get students for current page
        const pageStudents = students.slice(startIndex, endIndex);

        // Add students to table
        if (pageStudents.length > 0) {
            pageStudents.forEach((student, index) => {
                const row = document.createElement('tr');

                // Calculate student number (considering pagination)
                const studentNumber = startIndex + index + 1;

                // Validate student data
                if (!student.id_etudiant) {
                    console.error('Student missing id_etudiant:', student);
                    // Try to find id_etudiant in other properties
                    if (student.id) {
                        student.id_etudiant = student.id;
                        console.log('Using student.id as id_etudiant:', student.id);
                    }
                }

                // Ensure we have a valid student ID
                const studentId = student.id_etudiant || '';

                // Ensure has_grade is a boolean
                if (typeof student.has_grade === 'string') {
                    student.has_grade = student.has_grade === '1' || student.has_grade === 'true';
                } else if (typeof student.has_grade === 'number') {
                    student.has_grade = student.has_grade === 1;
                }

                // Check if student has a valid grade value
                const hasValidValue = student.valeur !== null &&
                                     student.valeur !== undefined &&
                                     student.valeur !== '' &&
                                     !isNaN(parseFloat(student.valeur));

                // If student has a valid grade value but has_grade is false, update has_grade
                if (hasValidValue && !student.has_grade) {
                    student.has_grade = true;
                }

                // If has_grade is true but no valid value, set has_grade to false
                if (!hasValidValue && student.has_grade) {
                    student.has_grade = false;
                }

                // Format the grade value for display
                let displayGrade = '';
                if (hasValidValue) {
                    // Format to 2 decimal places
                    displayGrade = parseFloat(student.valeur).toFixed(2);
                    // Ensure the value is displayed with 2 decimal places
                    if (displayGrade.indexOf('.') === -1) {
                        displayGrade += '.00';
                    } else if (displayGrade.split('.')[1].length === 1) {
                        displayGrade += '0';
                    }
                }

                // Determine validation status based on has_grade and valid value
                const hasGrade = student.has_grade && hasValidValue;
                const gradeValue = hasGrade ? parseFloat(student.valeur) : 0;
                const isValidated = hasGrade && gradeValue >= 12;
                const validationStatus = hasGrade ? (isValidated ? 'V' : 'R') : '-';
                const statusClass = hasGrade ? (isValidated ? 'validated' : 'rejected') : '';

                // Log for debugging
                if (hasGrade) {
                    console.log(`Student ${student.nom} ${student.prenom} has grade: ${displayGrade}, validation: ${validationStatus}, has_grade flag: ${student.has_grade}`);
                }

                // Log student data for debugging
                console.log(`Student ${index + 1}:`, {
                    id_etudiant: studentId,
                    CNE: student.CNE || student.cne || '',
                    nom: student.nom,
                    prenom: student.prenom,
                    valeur: displayGrade
                });

                row.innerHTML = `
                    <td>${studentNumber}</td>
                    <td>${student.CNE || student.cne || ''}</td>
                    <td>${student.nom}</td>
                    <td>${student.prenom}</td>
                    <td class="editable-grade" data-student-id="${studentId}" data-original-value="${displayGrade}">${displayGrade}</td>
                    <td><span class="validation-status ${statusClass}">${validationStatus}</span></td>
                `;

                tableBody.appendChild(row);
            });

            // Add event listeners for editable grades
            addGradeEditListeners();
        } else {
            // If no students, show a message
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center">Aucun étudiant trouvé</td></tr>';
        }
    }

    // Function to add event listeners for editable grades
    function addGradeEditListeners() {
        const editableGrades = document.querySelectorAll('.editable-grade');

        editableGrades.forEach(grade => {
            grade.addEventListener('click', function() {
                // Get current value
                const currentValue = this.textContent;
                const studentId = this.dataset.studentId;
                const originalValue = this.dataset.originalValue;

                // Validate student ID
                if (!studentId) {
                    console.error('Missing student ID in editable grade element:', this);
                    showNotification('Erreur: ID étudiant manquant. Impossible de modifier la note.', 'error');
                    return;
                }

                // Log for debugging
                console.log('Editing grade for student ID:', studentId, 'Current value:', currentValue);

                // Create input element
                const input = document.createElement('input');
                input.type = 'number';
                input.min = '0';
                input.max = '20';
                input.step = '0.25';
                input.value = currentValue;
                input.className = 'grade-input';

                // Replace text with input
                this.textContent = '';
                this.appendChild(input);

                // Focus on input
                input.focus();

                // Handle input blur (save on focus out)
                input.addEventListener('blur', function() {
                    if (this.value !== currentValue) {
                        saveGrade(studentId, this.value, grade);
                    } else {
                        // If value hasn't changed, just restore the original text
                        grade.textContent = currentValue;
                    }
                });

                // Handle enter key
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault(); // Prevent default behavior
                        if (this.value !== currentValue) {
                            this.blur(); // Trigger blur event which saves the grade
                            saveGrade(studentId, this.value, grade);
                        } else {
                            // If value hasn't changed, just restore the original text
                            grade.textContent = currentValue;
                        }
                    } else if (e.key === 'Escape') {
                        // Cancel edit on escape
                        e.preventDefault();
                        grade.textContent = originalValue || '';
                    }
                });
            });
        });
    }

    // Function to save grade
    function saveGrade(studentId, newValue, gradeElement) {
        // Validate input
        newValue = parseFloat(newValue);
        if (isNaN(newValue) || newValue < 0 || newValue > 20) {
            console.error('Note invalide:', newValue);
            showNotification('Veuillez entrer une note valide entre 0 et 20', 'error');
            gradeElement.textContent = gradeElement.dataset.originalValue || '';
            return;
        }

        // Format to 2 decimal places
        newValue = newValue.toFixed(2);

        // Vérifier que tous les paramètres nécessaires sont disponibles
        if (!moduleId || !niveauId || !filiereId || !semestre || !session) {
            console.error('Paramètres manquants pour la sauvegarde de la note:', {
                moduleId, niveauId, filiereId, semestre, session
            });
            showNotification('Erreur: Paramètres de filtrage incomplets. Impossible de sauvegarder la note.', 'error');
            gradeElement.textContent = gradeElement.dataset.originalValue || '';
            return;
        }

        // Vérifier que l'ID de l'étudiant est valide
        if (!studentId) {
            console.error('ID étudiant manquant pour la sauvegarde de la note');
            showNotification('Erreur: ID étudiant manquant. Impossible de sauvegarder la note.', 'error');
            gradeElement.textContent = gradeElement.dataset.originalValue || '';
            return;
        }

        console.log('Paramètres utilisés pour la sauvegarde de la note:', {
            studentId, moduleId, niveauId, filiereId, semestre, session, newValue
        });

        // Prepare the request data
        const requestData = {
            student_id: studentId,
            note: newValue,
            module: moduleId,
            niveau: niveauId,
            filiere: filiereId,
            semestre: semestre,
            session: session,
            action: 'update_student_grade'
        };

        // Show loading notification
        logMessage('Sauvegarde de la note en cours...', 'info', false);

        // Make AJAX request to update grade
        console.log('Envoi de la note au serveur:', requestData);

        $.ajax({
            url: '../../route/noteRoute.php?action=update_student_grade',
            type: 'POST',
            data: JSON.stringify(requestData),
            contentType: 'application/json',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Update the grade element
                    gradeElement.textContent = newValue;
                    gradeElement.dataset.originalValue = newValue;

                    // Update validation status
                    const row = gradeElement.parentElement;
                    const statusCell = row.querySelector('.validation-status');
                    const isValidated = parseFloat(newValue) >= 12;

                    statusCell.textContent = isValidated ? 'V' : 'R';
                    statusCell.className = `validation-status ${isValidated ? 'validated' : 'rejected'}`;

                    // Update the student in allStudents array
                    const studentIndex = allStudents.findIndex(s => s.id_etudiant === studentId);
                    if (studentIndex !== -1) {
                        // Update the grade value
                        allStudents[studentIndex].valeur = newValue;

                        // Set the has_grade flag to true if the grade is valid
                        const hasValidValue = newValue !== null &&
                                             newValue !== undefined &&
                                             newValue !== '' &&
                                             !isNaN(parseFloat(newValue));

                        // Update the has_grade flag
                        allStudents[studentIndex].has_grade = hasValidValue;

                        console.log(`Updated student ${allStudents[studentIndex].nom} ${allStudents[studentIndex].prenom} grade to ${newValue}, has_grade=${hasValidValue}`);

                        // Update top performers
                        updateTopPerformers(allStudents);

                        // Update the validation status in the UI
                        const studentRow = document.querySelector(`tr[data-student-id="${studentId}"]`);
                        if (studentRow) {
                            const validationCell = studentRow.querySelector('.validation-status');
                            if (validationCell) {
                                const isValidated = hasValidValue && parseFloat(newValue) >= 12;
                                validationCell.textContent = hasValidValue ? (isValidated ? 'V' : 'R') : '-';
                                validationCell.className = 'validation-status ' + (hasValidValue ? (isValidated ? 'validated' : 'rejected') : '');
                            }
                        }

                        // Force refresh of the student's grade display
                        const gradeCell = document.querySelector(`.editable-grade[data-student-id="${studentId}"]`);
                        if (gradeCell) {
                            // Update the displayed value and data attribute
                            gradeCell.textContent = hasValidValue ? parseFloat(newValue).toFixed(2) : '';
                            gradeCell.dataset.originalValue = hasValidValue ? parseFloat(newValue).toFixed(2) : '';
                        }
                    } else {
                        console.warn(`Could not find student with ID ${studentId} in allStudents array`);
                    }

                    // Show success message
                    logMessage('Note mise à jour avec succès', 'success', false);
                } else {
                    // Show error message with details if available
                    const errorMsg = response.message || response.error || 'Erreur lors de la mise à jour de la note';
                    console.error('Error response:', response);
                    logMessage(errorMsg, 'error', true);
                    gradeElement.textContent = gradeElement.dataset.originalValue || '';
                }
            },
            error: function(xhr, status, error) {
                // Try to parse the response to get more detailed error information
                let errorMessage = 'Erreur de connexion au serveur: ' + error;
                try {
                    const responseJson = JSON.parse(xhr.responseText);
                    if (responseJson && responseJson.error) {
                        errorMessage = 'Erreur: ' + responseJson.error;
                    }
                } catch (e) {
                    // If parsing fails, use the raw response text if available
                    if (xhr.responseText) {
                        errorMessage = 'Erreur: ' + xhr.responseText;
                    }
                }

                // Log detailed error information
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);
                console.error('Request Data:', requestData);

                // Show error message
                showNotification(errorMessage, 'error');
                gradeElement.textContent = gradeElement.dataset.originalValue || '';
            }
        });
    }

    // Function to update pagination
    function updatePagination() {
        const totalPages = Math.ceil(totalStudents / rowsPerPage);
        const paginationPages = document.getElementById('pagination-pages');
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');

        // Clear pagination
        paginationPages.innerHTML = '';

        // Add page numbers
        for (let i = 1; i <= totalPages; i++) {
            const pageNumber = document.createElement('span');
            pageNumber.className = `page-number ${i === currentPage ? 'active' : ''}`;
            pageNumber.textContent = i;

            pageNumber.addEventListener('click', function() {
                currentPage = i;
                displayStudents(allStudents, currentPage);
                updatePagination();
            });

            paginationPages.appendChild(pageNumber);
        }

        // Update prev/next buttons
        prevBtn.disabled = currentPage === 1;
        nextBtn.disabled = currentPage === totalPages || totalPages === 0;

        // Add event listeners for prev/next buttons
        prevBtn.onclick = function() {
            if (currentPage > 1) {
                currentPage--;
                displayStudents(allStudents, currentPage);
                updatePagination();
            }
        };

        nextBtn.onclick = function() {
            if (currentPage < totalPages) {
                currentPage++;
                displayStudents(allStudents, currentPage);
                updatePagination();
            }
        };
    }

    // Function to filter students by search term
    function filterStudents(searchTerm) {
        searchTerm = searchTerm.toLowerCase();

        if (searchTerm === '') {
            // If search term is empty, show all students
            displayStudents(allStudents, 1);
            totalStudents = allStudents.length;
        } else {
            // Filter students by search term
            const filteredStudents = allStudents.filter(student => {
                const cne = student.CNE || student.cne || '';
                return (
                    cne.toLowerCase().includes(searchTerm) ||
                    student.nom.toLowerCase().includes(searchTerm) ||
                    student.prenom.toLowerCase().includes(searchTerm)
                );
            });

            // Update display with filtered students
            displayStudents(filteredStudents, 1);
            totalStudents = filteredStudents.length;
        }

        // Reset to first page
        currentPage = 1;

        // Update pagination
        updatePagination();

        // Update total students count
        document.getElementById('total-students').textContent = `Total: ${totalStudents} étudiants`;

        // Show/hide no students message
        if (totalStudents === 0) {
            document.getElementById('no-students-message').style.display = 'block';
        } else {
            document.getElementById('no-students-message').style.display = 'none';
        }
    }

    // Function to save all grades
    function saveAllGrades() {
        console.log('Début de la fonction saveAllGrades');

        // Vérifier que tous les paramètres nécessaires sont disponibles
        if (!moduleId || !niveauId || !filiereId || !semestre || !session) {
            console.error('Paramètres manquants pour la sauvegarde des notes:', {
                moduleId, niveauId, filiereId, semestre, session
            });
            logMessage('Erreur: Paramètres de filtrage incomplets. Impossible de sauvegarder les notes.', 'error', true);
            return;
        }

        console.log('Paramètres utilisés pour la sauvegarde des notes:', {
            moduleId, niveauId, filiereId, semestre, session
        });

        // Récupérer toutes les notes des étudiants
        const grades = [];

        // Récupérer toutes les notes saisies dans le tableau
        const gradeElements = document.querySelectorAll('.editable-grade');

        gradeElements.forEach(gradeElement => {
            const studentId = gradeElement.dataset.studentId;
            const gradeValue = gradeElement.textContent.trim();

            // Ne sauvegarder que les notes qui ont une valeur
            if (gradeValue !== null && gradeValue !== undefined && gradeValue !== '') {
                // Validate the grade value
                const numericValue = parseFloat(gradeValue);
                if (!isNaN(numericValue) && numericValue >= 0 && numericValue <= 20) {
                    grades.push({
                        id_etudiant: studentId,
                        id_module: moduleId,
                        id_niveau: niveauId,
                        id_filiere: filiereId,
                        semestre: semestre,
                        session: session,
                        valeur: numericValue.toFixed(2)
                    });
                } else {
                    console.warn(`Invalid grade value for student ${studentId}: ${gradeValue}`);
                }
            }
        });

        // Vérifier s'il y a des notes à sauvegarder
        if (grades.length === 0) {
            logMessage('Aucune note à sauvegarder', 'info', false);
            return;
        }

        // Afficher un message de chargement
        logMessage('Sauvegarde des notes en cours...', 'info', false);

        // Envoyer les notes au serveur
        console.log('Envoi des notes au serveur:', grades);

        $.ajax({
            url: '../../route/noteRoute.php?action=saveGrades',
            type: 'POST',
            data: JSON.stringify({ grades: grades }),
            contentType: 'application/json',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    logMessage('Toutes les notes ont été sauvegardées avec succès', 'success', true);

                    // Update the allStudents array with the new grades
                    grades.forEach(grade => {
                        const studentIndex = allStudents.findIndex(s => s.id_etudiant === grade.id_etudiant);
                        if (studentIndex !== -1) {
                            // Update the grade value
                            allStudents[studentIndex].valeur = grade.valeur;

                            // Set the has_grade flag to true if the grade is valid
                            const hasValidValue = grade.valeur !== null &&
                                                 grade.valeur !== undefined &&
                                                 grade.valeur !== '' &&
                                                 !isNaN(parseFloat(grade.valeur));

                            // Update the has_grade flag
                            allStudents[studentIndex].has_grade = hasValidValue;

                            console.log(`Updated student ${allStudents[studentIndex].nom} ${allStudents[studentIndex].prenom} grade to ${grade.valeur}, has_grade=${hasValidValue}`);

                            // Force refresh of the student's grade display in the UI
                            const gradeCell = document.querySelector(`.editable-grade[data-student-id="${grade.id_etudiant}"]`);
                            if (gradeCell) {
                                // Update the displayed value and data attribute
                                gradeCell.textContent = hasValidValue ? parseFloat(grade.valeur).toFixed(2) : '';
                                gradeCell.dataset.originalValue = hasValidValue ? parseFloat(grade.valeur).toFixed(2) : '';
                            }

                            // Update the validation status in the UI
                            const studentRow = document.querySelector(`tr[data-student-id="${grade.id_etudiant}"]`);
                            if (studentRow) {
                                const validationCell = studentRow.querySelector('.validation-status');
                                if (validationCell) {
                                    const isValidated = hasValidValue && parseFloat(grade.valeur) >= 12;
                                    validationCell.textContent = hasValidValue ? (isValidated ? 'V' : 'R') : '-';
                                    validationCell.className = 'validation-status ' + (hasValidValue ? (isValidated ? 'validated' : 'rejected') : '');
                                }
                            }
                        }
                    });

                    // Update top performers
                    updateTopPerformers(allStudents);

                    // Update the displayed grades (in case they were formatted)
                    gradeElements.forEach(gradeElement => {
                        const studentId = gradeElement.dataset.studentId;
                        const student = allStudents.find(s => s.id_etudiant === studentId);
                        if (student && student.valeur) {
                            gradeElement.textContent = student.valeur;
                            gradeElement.dataset.originalValue = student.valeur;

                            // Update validation status
                            const row = gradeElement.parentElement;
                            const statusCell = row.querySelector('.validation-status');
                            const isValidated = parseFloat(student.valeur) >= 12;

                            statusCell.textContent = isValidated ? 'V' : 'R';
                            statusCell.className = `validation-status ${isValidated ? 'validated' : 'rejected'}`;
                        }
                    });
                } else {
                    const errorMsg = response.message || response.error || 'Erreur lors de la sauvegarde des notes';
                    console.error('Error response:', response);
                    logMessage(errorMsg, 'error', true);
                }
            },
            error: function(xhr, status, error) {
                // Try to parse the response to get more detailed error information
                let errorMessage = 'Erreur de connexion au serveur: ' + error;
                try {
                    const responseJson = JSON.parse(xhr.responseText);
                    if (responseJson && responseJson.error) {
                        errorMessage = 'Erreur: ' + responseJson.error;
                    }
                } catch (e) {
                    // If parsing fails, use the raw response text if available
                    if (xhr.responseText) {
                        errorMessage = 'Erreur: ' + xhr.responseText;
                    }
                }

                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);
                logMessage(errorMessage, 'error', true);
            }
        });
    }

    // Function to generate PDF - simplified version
    function generatePDF(saveToFile = true) {
        return new Promise((resolve, reject) => {
            try {
                // Show loading state
                logMessage('Génération du PDF en cours...', 'info', false);

                // Get the elements to include in the PDF
                const compactHeader = document.querySelector('.compact-header');
                const gradesContainer = document.querySelector('.grades-container');

                // Check if all required elements exist
                if (!compactHeader || !gradesContainer) {
                    console.error('Missing required elements for PDF generation:', {
                        compactHeader: !!compactHeader,
                        gradesContainer: !!gradesContainer
                    });
                    logMessage('Erreur: Éléments manquants pour la génération du PDF', 'error', true);
                    reject(new Error('Missing required elements for PDF generation'));
                    return;
                }

        // Create a clone of the elements to avoid modifying the original
        const pdfContent = document.createElement('div');
        pdfContent.appendChild(compactHeader.cloneNode(true));

        // Create a clone of the grades container
        const gradesClone = gradesContainer.cloneNode(true);

        // Remove elements we don't want in the PDF
        const elementsToRemove = [
            '.search-box',
            '#download-pdf',
            '#send-to-coordinator',
            '.grades-actions',
            '.pagination-container',
            '.top-performances'
        ];

        elementsToRemove.forEach(selector => {
            const element = gradesClone.querySelector(selector);
            if (element) element.remove();
        });

        // Add all students to the table (without pagination)
        const tableBody = gradesClone.querySelector('#students-list');
        if (tableBody) {
            tableBody.innerHTML = '';

            allStudents.forEach((student, index) => {
                const row = document.createElement('tr');

                // Determine validation status
                const hasGrade = student.valeur !== null && student.valeur !== undefined && student.valeur !== '';
                const isValidated = hasGrade && parseFloat(student.valeur) >= 12;
                const validationStatus = hasGrade ? (isValidated ? 'V' : 'R') : '-';
                const statusClass = hasGrade ? (isValidated ? 'validated' : 'rejected') : '';

                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${student.CNE || student.cne || ''}</td>
                    <td>${student.nom}</td>
                    <td>${student.prenom}</td>
                    <td>${student.valeur || '-'}</td>
                    <td><span class="validation-status ${statusClass}">${validationStatus}</span></td>
                `;

                tableBody.appendChild(row);
            });
        }

        // Add the grades container to the PDF content
        pdfContent.appendChild(gradesClone);

        // Styling for PDF with compressed filter section
        const style = document.createElement('style');
        style.textContent = `
            /* Common container for consistent width */
            .university-header, .filters-container, .grades-container {
                width: 100% !important;
                max-width: 800px !important;
                margin-left: auto !important;
                margin-right: auto !important;
                box-sizing: border-box !important;
            }

            /* University header styling */
            .university-header {
                margin-bottom: 10px !important;
                padding: 10px !important;
                background-color: #f8f9fa !important;
                border-radius: 5px !important;
                text-align: center !important;
            }
            .university-logo {
                height: 50px !important;
                margin-bottom: 5px !important;
            }
            .university-name {
                font-size: 18px !important;
                margin: 0 !important;
                font-weight: bold !important;
            }
            .university-school {
                font-size: 14px !important;
                margin: 0 !important;
            }

            /* Compressed filter section */
            .filters-container {
                margin-bottom: 10px !important;
                padding: 10px !important;
                background-color: #f8f9fa !important;
                border-radius: 5px !important;
            }
            .filters-container .row {
                margin: 0 !important;
                display: flex !important;
                flex-wrap: wrap !important;
                justify-content: space-between !important;
            }
            .filters-container .col-md-6 {
                width: 50% !important;
                padding: 2px !important;
                box-sizing: border-box !important;
            }
            .filter-card {
                padding: 5px !important;
                margin-bottom: 4px !important;
                min-height: auto !important;
                height: auto !important;
                display: flex !important;
                align-items: center !important;
                border-radius: 4px !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            }
            .filter-icon {
                width: 24px !important;
                height: 24px !important;
                font-size: 0.8rem !important;
                margin-right: 5px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                border-radius: 50% !important;
            }
            .filter-content {
                flex: 1 !important;
            }
            .filter-label {
                font-size: 0.7rem !important;
                margin-bottom: 2px !important;
                line-height: 1.1 !important;
                color: #666 !important;
            }
            .filter-value {
                font-size: 0.8rem !important;
                line-height: 1.2 !important;
                font-weight: 500 !important;
            }

            /* Module, Coordinator and Teacher cards - more compact */
            .module-card {
                width: 100% !important;
                padding: 8px !important;
                margin-bottom: 5px !important;
                background: linear-gradient(135deg, #e8f4f8 0%, #d1e6f0 100%) !important;
                border-radius: 4px !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            }
            .coordinator-card {
                padding: 8px !important;
                margin-bottom: 5px !important;
                background: linear-gradient(135deg, #f0f8e8 0%, #e0f0d1 100%) !important;
                border-radius: 4px !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            }
            .teacher-card {
                padding: 8px !important;
                margin-bottom: 5px !important;
                background: linear-gradient(135deg, #f8e8f4 0%, #f0d1e6 100%) !important;
                border-radius: 4px !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            }

            /* Grades table styling */
            .grades-container {
                width: 100% !important;
                margin-top: 10px !important;
                padding: 10px !important;
                background-color: #f8f9fa !important;
                border-radius: 5px !important;
            }
            .grades-header {
                padding: 5px !important;
                margin-bottom: 5px !important;
                border-bottom: 1px solid #ddd !important;
            }
            .grades-title {
                font-size: 14px !important;
                font-weight: bold !important;
            }
            .validation-status.validated { color: green !important; font-weight: bold !important; }
            .validation-status.rejected { color: red !important; font-weight: bold !important; }
            table { width: 100% !important; border-collapse: collapse !important; margin-top: 5px !important; }
            th, td { border: 1px solid #ddd !important; padding: 5px !important; font-size: 12px !important; text-align: center !important; }
            th { background-color: #4a89dc !important; color: white !important; }
        `;
        pdfContent.appendChild(style);

        // PDF options optimized for quality and consistent width
        const options = {
            margin: [15, 15, 15, 15], // top, right, bottom, left
            filename: 'notes_etudiants.pdf',
            image: { type: 'jpeg', quality: 1.0 },
            html2canvas: {
                scale: 2,
                useCORS: true,
                logging: false,
                letterRendering: true,
                width: 800 // Fixed width to match container max-width
            },
            jsPDF: {
                unit: 'mm',
                format: 'a4',
                orientation: 'portrait',
                compress: true,
                precision: 16
            }
        };

                // Generate and return PDF with improved error handling
                if (saveToFile) {
                    // Generate PDF and save to file
                    html2pdf().from(pdfContent).set(options).save().then(() => {
                        logMessage('PDF généré avec succès', 'success', true);
                        resolve();
                    }).catch(error => {
                        console.error('Error generating PDF:', error);
                        logMessage('Erreur lors de la génération du PDF', 'error', true);
                        reject(error);
                    });
                } else {
                    // Generate PDF and return as blob
                    html2pdf().from(pdfContent).set(options).outputPdf('blob').then(blob => {
                        resolve(blob);
                    }).catch(error => {
                        console.error('Error generating PDF:', error);
                        logMessage('Erreur lors de la génération du PDF', 'error', true);
                        reject(error);
                    });
                }
            } catch (error) {
                console.error('Erreur inattendue lors de la génération du PDF:', error);
                logMessage('Erreur inattendue lors de la génération du PDF', 'error', true);
                reject(error);
            }
        });
    }

    // Function to send PDF to coordinator
    function sendPDFToCoordinator() {
        // Show loading state
        logMessage('Préparation de l\'envoi au coordinateur...', 'info', false);

        try {
            // Vérifier si les éléments nécessaires existent
            const compactHeader = document.querySelector('.compact-header');
            const gradesContainer = document.querySelector('.grades-container');

            if (!compactHeader || !gradesContainer) {
                console.error('Missing required elements for PDF generation:', {
                    compactHeader: !!compactHeader,
                    gradesContainer: !!gradesContainer
                });
                logMessage('Erreur: Éléments manquants pour la génération du PDF', 'error', true);
                return;
            }

            // Vérifier si les éléments de filtre nécessaires existent
            const niveauElement = document.getElementById('niveau-value');
            const semestreElement = document.getElementById('semestre-value');
            const sessionElement = document.getElementById('session-value');

            if (!niveauElement || !semestreElement || !sessionElement) {
                console.error('Missing filter elements for PDF generation:', {
                    niveauElement: !!niveauElement,
                    semestreElement: !!semestreElement,
                    sessionElement: !!sessionElement
                });
                logMessage('Erreur: Informations de filtrage manquantes', 'error', true);
                return;
            }

            // Generate PDF as blob
            generatePDF(false).then(pdfBlob => {
                // Convert blob to base64
                const reader = new FileReader();
                reader.readAsDataURL(pdfBlob);
                reader.onloadend = function() {
                    const base64data = reader.result;

                    // Get teacher name from the teacher card
                    const teacherCard = document.querySelector('.teacher-card');
                    const teacherName = teacherCard ? teacherCard.querySelector('.filter-value').textContent : 'Enseignant';

                    // Get niveau name from the niveau card
                    const niveauValue = niveauElement.textContent;

                    // Get semestre name from the semestre card
                    const semestreValue = semestreElement.textContent;

                    // Get session value from the session card
                    const sessionValue = sessionElement.textContent;

                    // Récupérer l'ID de l'enseignant depuis la variable globale si disponible
                    const teacherId = window.teacherId || null;

                    // Récupérer les variables globales depuis les paramètres URL
                    const urlParams = new URLSearchParams(window.location.search);
                    const moduleIdFromUrl = urlParams.get('module');
                    const filiereIdFromUrl = urlParams.get('filiere');
                    const niveauIdFromUrl = urlParams.get('niveau');
                    const semestreFromUrl = urlParams.get('semestre');
                    const moduleNameFromUrl = urlParams.get('module_name');
                    const filiereNameFromUrl = urlParams.get('filiere_name');

                    // Utiliser les variables globales ou les paramètres URL comme fallback
                    const finalModuleId = moduleId || moduleIdFromUrl;
                    const finalFiliereId = filiereId || filiereIdFromUrl;
                    const finalNiveauId = niveauId || niveauIdFromUrl;
                    const finalSemestre = semestre || semestreFromUrl;
                    const finalModuleName = moduleName || moduleNameFromUrl || 'Module';
                    const finalFiliereName = filiereName || filiereNameFromUrl || 'Filière';

                    // Vérifier que toutes les données nécessaires sont disponibles
                    if (!finalModuleId || !finalFiliereId || !finalNiveauId || !finalSemestre) {
                        console.error('Missing required data for sending PDF:', {
                            moduleId: finalModuleId,
                            filiereId: finalFiliereId,
                            niveauId: finalNiveauId,
                            semestre: finalSemestre,
                            sessionValue
                        });
                        logMessage('Erreur: Données manquantes pour l\'envoi au coordinateur', 'error', true);
                        return;
                    }

                    // Prepare data to send to server
                    const data = {
                        pdf_content: base64data,
                        module_id: finalModuleId,
                        filiere_id: finalFiliereId,
                        niveau_id: finalNiveauId,
                        semestre_id: finalSemestre,
                        session: sessionValue,
                        module_name: finalModuleName,
                        filiere_name: finalFiliereName,
                        niveau_name: niveauValue,
                        semestre_name: semestreValue,
                        teacher_name: teacherName,
                        teacher_id: teacherId
                    };

                    console.log('Sending PDF to coordinator with data:', {
                        module_id: finalModuleId,
                        filiere_id: finalFiliereId,
                        niveau_id: finalNiveauId,
                        semestre_id: finalSemestre,
                        session: sessionValue
                    });

                    // Send data to server
                    fetch('../../route/noteRoute.php?action=send_to_coordinator', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            logMessage('Notes envoyées au coordinateur avec succès', 'success', true);
                        } else {
                            logMessage('Erreur lors de l\'envoi des notes: ' + (data.error || 'Erreur inconnue'), 'error', true);
                        }
                    })
                    .catch(error => {
                        console.error('Error sending PDF to coordinator:', error);
                        logMessage('Erreur lors de l\'envoi des notes au coordinateur', 'error', true);
                    });
                };
            }).catch(error => {
                console.error('Error generating PDF for coordinator:', error);
                logMessage('Erreur lors de la génération du PDF pour le coordinateur', 'error', true);
            });
        } catch (error) {
            console.error('Error in sendPDFToCoordinator:', error);
            logMessage('Erreur lors de la préparation de l\'envoi au coordinateur', 'error', true);
        }
    }

    // Function to log message to console and optionally show notification
    function logMessage(message, type = 'info', showAlert = false) {
        // Always log to console
        switch (type) {
            case 'success':
                console.log('✅ ' + message);
                break;
            case 'error':
                console.error('❌ ' + message);
                break;
            default:
                console.info('ℹ️ ' + message);
        }

        // Only show visual notification for important messages or if explicitly requested
        if (showAlert) {
            showVisualNotification(message, type);
        }
    }

    // Function to show visual notification (only for important messages)
    function showVisualNotification(message, type = 'info') {
        // Check if notification container exists
        let notificationContainer = document.getElementById('notification-container');

        // Create container if it doesn't exist
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'notification-container';
            document.body.appendChild(notificationContainer);
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // Set icon based on type
        let icon = '';
        switch (type) {
            case 'success':
                icon = '<i class="bi bi-check-circle"></i>';
                break;
            case 'error':
                icon = '<i class="bi bi-exclamation-circle"></i>';
                break;
            default:
                icon = '<i class="bi bi-info-circle"></i>';
        }

        // Set notification content
        notification.innerHTML = `
            <div class="notification-icon">${icon}</div>
            <div class="notification-message">${message}</div>
            <div class="notification-close"><i class="bi bi-x"></i></div>
        `;

        // Add notification to container
        notificationContainer.appendChild(notification);

        // Add close event
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', function() {
            notification.style.animation = 'slideOut 0.25s ease-in forwards';
            setTimeout(() => {
                notification.remove();
            }, 250);
        });

        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOut 0.25s ease-in forwards';
                setTimeout(() => {
                    notification.remove();
                }, 250);
            }
        }, 4000);
    }

    // Legacy function for backward compatibility
    function showNotification(message, type = 'info') {
        // By default, only show visual notifications for errors
        const showAlert = type === 'error';
        logMessage(message, type, showAlert);
    }
});