<?php
/**
 * Debug script to check why vacant UEs are not showing
 */

require_once 'config/db.php';

echo "<h1>Debug Vacant UEs</h1>";

$conn = getConnection();
if (!$conn) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
}

$filiereId = 1; // Testing with filiere 1
$academicYear = '2024-2025'; // Current academic year

echo "<h2>Step 1: Check ue_vacantes table</h2>";
$sql1 = "SELECT * FROM ue_vacantes WHERE is_vacant = 1 AND academic_year = '$academicYear'";
$result1 = mysqli_query($conn, $sql1);

if ($result1) {
    echo "<p>Found " . mysqli_num_rows($result1) . " vacant UEs in ue_vacantes table:</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>UE_ID</th><th>Department_ID</th><th>Academic_Year</th><th>Is_Vacant</th><th>Marked_By</th></tr>";
    while ($row = mysqli_fetch_assoc($result1)) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['ue_id']}</td>";
        echo "<td>{$row['department_id']}</td>";
        echo "<td>{$row['academic_year']}</td>";
        echo "<td>{$row['is_vacant']}</td>";
        echo "<td>{$row['marked_by']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>Error: " . mysqli_error($conn) . "</p>";
}

echo "<h2>Step 2: Check if these UEs exist in uniteenseignement table</h2>";
$sql2 = "SELECT ue.id, ue.type, ue.volume_horaire, ue.module_id 
         FROM uniteenseignement ue 
         WHERE ue.id IN (9, 18, 3)";
$result2 = mysqli_query($conn, $sql2);

if ($result2) {
    echo "<p>Found " . mysqli_num_rows($result2) . " UEs in uniteenseignement table:</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>UE_ID</th><th>Type</th><th>Volume_Horaire</th><th>Module_ID</th></tr>";
    while ($row = mysqli_fetch_assoc($result2)) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['type']}</td>";
        echo "<td>{$row['volume_horaire']}</td>";
        echo "<td>{$row['module_id']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>Error: " . mysqli_error($conn) . "</p>";
}

echo "<h2>Step 3: Check modules and their filières</h2>";
$sql3 = "SELECT m.id, m.nom, m.filiere_id, f.nom_filiere 
         FROM module m 
         LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
         WHERE m.id IN (
             SELECT ue.module_id FROM uniteenseignement ue WHERE ue.id IN (9, 18, 3)
         )";
$result3 = mysqli_query($conn, $sql3);

if ($result3) {
    echo "<p>Found " . mysqli_num_rows($result3) . " modules:</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Module_ID</th><th>Module_Name</th><th>Filiere_ID</th><th>Filiere_Name</th></tr>";
    while ($row = mysqli_fetch_assoc($result3)) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['nom']}</td>";
        echo "<td>{$row['filiere_id']}</td>";
        echo "<td>{$row['nom_filiere']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>Error: " . mysqli_error($conn) . "</p>";
}

echo "<h2>Step 4: Check if UEs are already assigned</h2>";
$sql4 = "SELECT aff.id, aff.professeur_id, aff.unite_enseignement_id, aff.annee_academique 
         FROM affectation aff 
         WHERE aff.unite_enseignement_id IN (9, 18, 3) 
         AND aff.annee_academique = '$academicYear'";
$result4 = mysqli_query($conn, $sql4);

if ($result4) {
    echo "<p>Found " . mysqli_num_rows($result4) . " existing assignments:</p>";
    if (mysqli_num_rows($result4) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Affectation_ID</th><th>Professeur_ID</th><th>UE_ID</th><th>Academic_Year</th></tr>";
        while ($row = mysqli_fetch_assoc($result4)) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['professeur_id']}</td>";
            echo "<td>{$row['unite_enseignement_id']}</td>";
            echo "<td>{$row['annee_academique']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No existing assignments found - good!</p>";
    }
} else {
    echo "<p style='color: red;'>Error: " . mysqli_error($conn) . "</p>";
}

echo "<h2>Step 5: Test the full query</h2>";
$sql5 = "SELECT DISTINCT ue.id as ue_id, ue.type as ue_type, ue.volume_horaire, ue.nb_groupes,
        m.id as module_id, m.nom as module_name, m.volume_total,
        f.nom_filiere, f.id_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre,
        uv.is_vacant, uv.comments, uv.marked_at,
        CONCAT(e.prenom, ' ', e.nom) as marked_by_name
        FROM uniteenseignement ue
        LEFT JOIN module m ON ue.module_id = m.id
        LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
        LEFT JOIN niveaux n ON m.id_niveau = n.id
        LEFT JOIN specialite s ON m.specialite_id = s.id
        LEFT JOIN semestre sem ON m.id_semestre = sem.id
        LEFT JOIN ue_vacantes uv ON ue.id = uv.ue_id AND uv.academic_year = '$academicYear'
        LEFT JOIN enseignant e ON uv.marked_by = e.id_enseignant
        WHERE f.id_filiere = '$filiereId'
        AND uv.is_vacant = 1
        AND ue.id NOT IN (
            SELECT DISTINCT aff.unite_enseignement_id
            FROM affectation aff
            WHERE aff.annee_academique = '$academicYear'
            AND aff.unite_enseignement_id IS NOT NULL
        )
        ORDER BY m.nom, ue.type";

$result5 = mysqli_query($conn, $sql5);

if ($result5) {
    echo "<p>Full query returned " . mysqli_num_rows($result5) . " results:</p>";
    if (mysqli_num_rows($result5) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>UE_ID</th><th>Type</th><th>Module</th><th>Filiere</th><th>Niveau</th><th>Semestre</th></tr>";
        while ($row = mysqli_fetch_assoc($result5)) {
            echo "<tr>";
            echo "<td>{$row['ue_id']}</td>";
            echo "<td>{$row['ue_type']}</td>";
            echo "<td>{$row['module_name']}</td>";
            echo "<td>{$row['nom_filiere']}</td>";
            echo "<td>{$row['niveau']}</td>";
            echo "<td>{$row['semestre']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>No results from full query!</p>";
    }
} else {
    echo "<p style='color: red;'>Error in full query: " . mysqli_error($conn) . "</p>";
}

echo "<h2>Step 6: Check filiere 1 specifically</h2>";
$sql6 = "SELECT * FROM filiere WHERE id_filiere = 1";
$result6 = mysqli_query($conn, $sql6);

if ($result6) {
    echo "<p>Filiere 1 details:</p>";
    while ($row = mysqli_fetch_assoc($result6)) {
        echo "<pre>" . print_r($row, true) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>Error: " . mysqli_error($conn) . "</p>";
}

mysqli_close($conn);
echo "<h2>Debug Complete</h2>";
?>
