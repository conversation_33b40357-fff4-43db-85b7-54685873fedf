/* Personnel Page Styles */

.personnel-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Header styles with standard blue */
.personnel-header {
    margin-bottom: 30px;
    background: #1a73e8; /* Standard royal blue */
    padding: 25px;
    border-radius: 15px;
    color: white; /* Changed for better contrast */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.personnel-header h1 {
    font-weight: 700;
    margin-bottom: 10px;
    font-size: 2.2rem;
}

.personnel-header .subtitle {
    opacity: 0.9;
    font-size: 1.1rem;
    margin-bottom: 0;
}

.search-container {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.search-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 15px;
}

.search-input {
    border-radius: 20px;
    padding-left: 40px;
    border: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-btn {
    border-radius: 20px;
    background-color: #1a73e8; /* Standard royal blue */
    color: white;
    padding: 8px 20px;
    border: none;
    transition: all 0.3s;
    font-size: 0.9rem;
    height: 38px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-btn:hover {
    background-color: #1765cc; /* Darker shade of royal blue */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.search-btn:active {
    background-color: #1557b0; /* Even darker shade for active state */
}

.cancel-btn {
    border-radius: 20px;
    background-color: #f8f9fa;
    color: #495057;
    padding: 8px 20px;
    border: 1px solid #dee2e6;
    transition: all 0.3s;
    font-size: 0.9rem;
    height: 38px;
}

.cancel-btn:hover {
    background-color: #e9ecef;
    color: #212529;
}

.personnel-table {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.personnel-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.personnel-table td {
    vertical-align: middle;
}

.profile-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e0e0e0;
}

.role-badge {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Different pastel colors for each role */
.role-enseignant {
    background-color: #e3f2fd;
    color: #4285F4;
}

.role-chef {
    background-color: #fff8e1;
    color: #F4B400;
}

.role-coordinateur {
    background-color: #e8f5e9;
    color: #0F9D58;
}

.role-vacataire {
    background-color: #f8e6ff;
    color: #9575CD;
}

.role-chef-filiere {
    background-color: #ffebee;
    color: #DB4437;
}

.results-count {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .search-container {
        padding: 15px;
    }

    .search-title {
        font-size: 1rem;
    }

    .personnel-table th,
    .personnel-table td {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .search-container .row {
        flex-direction: column;
    }

    .search-container .col-md-4 {
        margin-top: 10px;
    }

    .profile-pic {
        width: 30px;
        height: 30px;
    }

    .role-badge {
        padding: 3px 8px;
        font-size: 0.7rem;
    }
}