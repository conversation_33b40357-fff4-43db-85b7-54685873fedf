<?php
// Include necessary files
require_once "../controller/affectationController.php";

// Set headers for JSON response
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Handle GET requests
        if (isset($_GET['action'])) {
            $action = $_GET['action'];

            switch ($action) {
                case 'getUePreferencesByDepartment':
                    if (!isset($_GET['department_id'])) {
                        jsonResponse(['error' => 'Department ID is required'], 400);
                    }
                    getUePreferencesByDepartmentAPI($_GET['department_id']);
                    break;

                case 'getAffectationsByDepartment':
                    if (!isset($_GET['department_id'])) {
                        jsonResponse(['error' => 'Department ID is required'], 400);
                    }
                    getAffectationsByDepartmentAPI($_GET['department_id']);
                    break;

                default:
                    jsonResponse(['error' => 'Invalid action'], 400);
                    break;
            }
        } else {
            jsonResponse(['error' => 'Action is required'], 400);
        }
        break;

    case 'POST':
        // Handle POST requests
        if (isset($_GET['action'])) {
            $action = $_GET['action'];

            switch ($action) {
                case 'acceptUePreference':
                    acceptUePreferenceAPI();
                    break;

                case 'rejectUePreference':
                    rejectUePreferenceAPI();
                    break;

                default:
                    jsonResponse(['error' => 'Invalid action'], 400);
                    break;
            }
        } else {
            jsonResponse(['error' => 'Action is required'], 400);
        }
        break;

    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}
?>
