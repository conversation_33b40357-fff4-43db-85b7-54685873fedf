<?php
require_once __DIR__ . '/../model/demandesModel.php';
require_once __DIR__ . '/../utils/response.php';

function handleDemand($action, $data) {
    header('Content-Type: application/json');

    try {
        switch ($action) {
            case 'getAll':
                $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
                $perPage = isset($_GET['perPage']) ? intval($_GET['perPage']) : 8;

                // Ensure valid pagination parameters
                $page = max(1, $page);
                $perPage = max(1, min(50, $perPage));

                $result = getAllDemands($page, $perPage);
                echo json_encode($result);
                break;

            case 'updateStatus':
                if (!isset($data['id']) || !isset($data['status'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Demand ID and status are required']);
                    return;
                }
                $success = updateDemandStatus($data['id'], $data['status']);
                echo json_encode(['success' => $success]);
                break;

            case 'deleteDemand':
                if (!isset($data['id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Demand ID is required']);
                    return;
                }
                $success = deleteDemand($data['id']);
                echo json_encode(['success' => $success]);
                break;

            case 'sendMessage':
                if (!isset($data['id']) || !isset($data['message'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Demand ID and message are required']);
                    return;
                }
                $success = updateDemandMessage($data['id'], $data['message']);
                echo json_encode(['success' => $success]);
                break;

            case 'create':
                if (!isset($data['title']) || !isset($data['description']) ||
                    !isset($data['type']) || !isset($data['author_name'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Missing required fields']);
                    return;
                }
                $avatar = isset($data['author_avatar']) ?
                    $data['author_avatar'] :
                    'https://ui-avatars.com/api/?name=' . urlencode($data['author_name']);

                // Handle author_id if provided
                $author_id = isset($data['author_id']) ? $data['author_id'] : null;

                $success = createDemand(
                    $data['title'],
                    $data['description'],
                    $data['type'],
                    $data['author_name'],
                    $avatar,
                    $author_id
                );
                echo json_encode(['success' => $success]);
                break;

            default:
                http_response_code(400);
                echo json_encode(['error' => 'Invalid action']);
                break;
        }
    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Error in handleDemand: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());

        http_response_code(500);
        echo json_encode([
            'error' => 'Server error: ' . $e->getMessage(),
            'details' => 'Check server logs for more information'
        ]);
    }
}
?>
