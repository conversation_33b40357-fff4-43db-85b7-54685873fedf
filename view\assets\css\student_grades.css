/* Student Grades Page Styles */

/* Notification Styles */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
    max-width: 350px;
}

.notification {
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    min-width: 280px;
    max-width: 100%;
    animation: slideIn 0.25s ease-out forwards;
    font-size: 0.85rem;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

.notification.info {
    background-color: #e8f4f9;
    border-left: 3px solid #6a89cc;
    color: #2c3e50;
}

.notification.success {
    background-color: #e8f6ef;
    border-left: 3px solid #65b891;
    color: #2c3e50;
}

.notification.error {
    background-color: #fbeaea;
    border-left: 3px solid #e57373;
    color: #2c3e50;
}

.notification-icon {
    margin-right: 12px;
    font-size: 1rem;
    color: inherit;
}

.notification-message {
    flex: 1;
    line-height: 1.4;
}

.notification-close {
    margin-left: 12px;
    cursor: pointer;
    font-size: 0.9rem;
    opacity: 0.6;
    transition: opacity 0.15s ease;
    color: inherit;
}

.notification-close:hover {
    opacity: 1;
}

/* Compact Header Styles */
.compact-header {
    background-color: #f8f9fa;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    max-height: 120px; /* Increased maximum height constraint */
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
}

.header-top {
    padding: 6px 12px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    height: 38px; /* Increased fixed height for top section */
}

.compact-logo {
    width: 28px;
    height: 28px;
    object-fit: contain;
    margin-right: 10px;
}

.header-titles {
    line-height: 1.1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.university-name {
    color: #6a89cc;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1px;
    line-height: 1.2;
}

.university-school {
    color: #777;
    font-size: 0.7rem;
    margin-bottom: 0;
    line-height: 1.2;
}

.academic-year {
    font-size: 0.75rem;
    color: #777;
    text-align: right;
    margin-left: auto;
    white-space: nowrap;
}

.year-label {
    font-weight: 500;
    margin-right: 3px;
}

.year-value {
    font-weight: 600;
    color: #444;
}

.module-info-container {
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 82px; /* Increased fixed height for bottom section */
}

.module-main-info {
    flex: 1;
    min-width: 0; /* Allow shrinking */
    overflow: hidden; /* Prevent overflow */
    margin-right: 12px;
}

.module-name {
    font-size: 1.05rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.module-name i {
    color: #6a89cc;
    flex-shrink: 0;
    font-size: 0.95rem;
    margin-right: 6px;
}

.module-details {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    color: #777;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 2px;
}

.detail-item {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.detail-label {
    font-weight: 500;
    margin-right: 3px;
}

.detail-value {
    font-weight: 600;
    color: #444;
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.detail-separator {
    margin: 0 5px;
    color: #ddd;
    flex-shrink: 0;
}

.personnel-info {
    display: flex;
    flex-direction: column;
    font-size: 0.8rem;
    color: #777;
    flex-shrink: 0;
    max-width: 220px;
    border-left: 1px solid #f0f0f0;
    padding-left: 12px;
    margin-left: 4px;
    justify-content: center;
}

.personnel-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    white-space: nowrap;
}

.personnel-item:last-child {
    margin-bottom: 0;
}

.personnel-item i {
    font-size: 0.85rem;
    color: #6a89cc;
    flex-shrink: 0;
    margin-right: 4px;
}

.personnel-label {
    font-weight: 500;
    margin-right: 4px;
    flex-shrink: 0;
}

.personnel-value {
    font-weight: 600;
    color: #444;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Grades Container */
.grades-container {
    background-color: white;
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}

.grades-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.grades-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
}

.grades-title i {
    color: #6a89cc;
    margin-right: 8px;
}

.grades-actions {
    display: flex;
    gap: 10px;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 6px 12px 6px 30px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    width: 220px;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.search-box input:focus {
    border-color: #6a89cc;
    box-shadow: 0 0 0 3px rgba(106, 137, 204, 0.1);
    outline: none;
}

.search-box i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 0.85rem;
}

.btn {
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border: none;
}

.btn i {
    margin-right: 6px;
}

.btn-primary {
    background-color: #6a89cc;
    color: white;
}

.btn-primary:hover {
    background-color: #5a79bc;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(106, 137, 204, 0.2);
}

.btn-info {
    background-color: #78a6c8;
    color: white;
}

.btn-info:hover {
    background-color: #6896b8;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(120, 166, 200, 0.2);
}

.btn-success {
    background-color: #65b891;
    color: white;
}

.btn-success:hover {
    background-color: #55a881;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(101, 184, 145, 0.2);
}

.grades-description {
    color: #777;
    margin-bottom: 16px;
    font-size: 0.85rem;
}

/* Top Performances */
.top-performances {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
}

.top-title {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.top-title i {
    color: #f9a825;
    margin-right: 8px;
}

.top-students {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.top-student {
    flex: 1;
    min-width: 180px;
    background: white;
    border-radius: 6px;
    padding: 10px;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.top-student:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
}

.student-rank {
    width: 26px;
    height: 26px;
    background-color: #e0e0e0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    margin-right: 10px;
    font-size: 0.85rem;
}

.top-student:nth-child(1) .student-rank {
    background-color: #f9a825;
}

.top-student:nth-child(2) .student-rank {
    background-color: #b0bec5;
}

.top-student:nth-child(3) .student-rank {
    background-color: #bf8970;
}

.student-info {
    flex: 1;
}

.student-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
    font-size: 0.9rem;
}

.student-grade {
    color: #65b891;
    font-weight: 600;
    font-size: 0.85rem;
}

.no-top-students {
    color: #777;
    font-style: italic;
    font-size: 0.85rem;
    text-align: center;
    padding: 10px;
}

/* Grades Table */
.grades-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 16px;
    font-size: 0.9rem;
}

.grades-table thead th {
    background-color: #f8f9fa;
    color: #444;
    font-weight: 600;
    padding: 10px 12px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.grades-table thead th:first-child {
    border-top-left-radius: 6px;
}

.grades-table thead th:last-child {
    border-top-right-radius: 6px;
    text-align: center;
}

.grades-table tbody tr {
    transition: all 0.15s ease;
}

.grades-table tbody tr:hover {
    background-color: #f8f9fa;
}

.grades-table tbody td {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    color: #333;
    vertical-align: middle;
}

.grades-table tbody td:last-child {
    text-align: center;
}

.grades-table tbody tr:last-child td {
    border-bottom: none;
}

.editable-grade {
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 4px;
    transition: all 0.15s ease;
    font-family: 'Consolas', monospace;
    text-align: right;
    font-weight: 500;
}

.editable-grade:hover {
    background-color: #e8f4f9;
}

.editable-grade:empty::before {
    content: "-";
    color: #ccc;
}

.grade-input {
    width: 60px;
    padding: 4px 6px;
    border: 1px solid #ccc;
    border-radius: 4px;
    text-align: right;
    font-family: 'Consolas', monospace;
}

.grade-input:focus {
    border-color: #6a89cc;
    box-shadow: 0 0 0 2px rgba(106, 137, 204, 0.1);
    outline: none;
}

.validation-status {
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    min-width: 24px;
    text-align: center;
    font-size: 0.8rem;
}

.validation-status.validated {
    color: #65b891;
    background-color: rgba(101, 184, 145, 0.1);
}

.validation-status.rejected {
    color: #e57373;
    background-color: rgba(229, 115, 115, 0.1);
}

/* No Students Message */
.no-students-message {
    text-align: center;
    padding: 20px;
    color: #777;
    font-style: italic;
    font-size: 0.9rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 16px;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 6px 12px;
    color: #444;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
}

.pagination-btn i {
    font-size: 0.8rem;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #f0f0f0;
    border-color: #d0d0d0;
}

.pagination-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.pagination-pages {
    display: flex;
    gap: 4px;
}

.page-number {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.15s ease;
    font-size: 0.85rem;
    border: 1px solid transparent;
}

.page-number:hover {
    background-color: #f0f0f0;
    border-color: #e0e0e0;
}

.page-number.active {
    background-color: #6a89cc;
    color: white;
    border-color: #6a89cc;
}

.total-students {
    color: #777;
    font-size: 0.85rem;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    /* Compact header responsive styles for medium screens */
    .personnel-value {
        max-width: 100px;
    }

    .detail-value {
        max-width: 80px;
    }

    .search-box input {
        width: 180px;
    }
}

@media (max-width: 768px) {
    /* Compact header responsive styles for small screens */
    .compact-header {
        max-height: none; /* Remove height constraint on mobile */
    }

    .header-top {
        height: auto;
        flex-wrap: wrap;
        justify-content: center;
        padding: 6px 10px;
    }

    .compact-logo {
        width: 24px;
        height: 24px;
        margin-right: 8px;
    }

    .university-name {
        font-size: 0.85rem;
    }

    .university-school {
        font-size: 0.65rem;
    }

    .academic-year {
        font-size: 0.7rem;
        margin-left: 10px;
    }

    .module-info-container {
        height: auto;
        flex-direction: column;
        padding: 8px 10px;
    }

    .module-main-info {
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0;
    }

    .module-name {
        font-size: 0.95rem;
        margin-bottom: 5px;
    }

    .module-details {
        flex-wrap: wrap;
        margin-bottom: 5px;
        gap: 6px;
        font-size: 0.75rem;
    }

    .detail-item {
        margin-right: 0;
        margin-bottom: 3px;
    }

    .detail-separator {
        margin: 0 3px;
    }

    .personnel-info {
        width: 100%;
        border-top: 1px solid #f0f0f0;
        border-left: none;
        padding-top: 8px;
        padding-left: 0;
        max-width: none;
        font-size: 0.75rem;
    }

    .personnel-item {
        margin-bottom: 4px;
    }

    .personnel-value {
        max-width: none;
    }

    /* Table adjustments */
    .grades-table thead th {
        padding: 8px 10px;
        font-size: 0.8rem;
    }

    .grades-table tbody td {
        padding: 6px 10px;
        font-size: 0.85rem;
    }

    /* Top performances */
    .top-students {
        gap: 8px;
    }

    .top-student {
        min-width: 160px;
        padding: 8px;
    }
}

@media (max-width: 576px) {
    /* Compact header responsive styles for extra small screens */
    .header-top {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 6px;
    }

    .compact-logo {
        margin-right: 0;
        margin-bottom: 3px;
    }

    .academic-year {
        margin-top: 3px;
        margin-left: 0;
        text-align: center;
    }

    .module-name {
        font-size: 0.9rem;
        text-align: center;
        justify-content: center;
    }

    .module-details {
        flex-direction: column;
        align-items: flex-start;
    }

    .detail-separator {
        display: none;
    }

    .detail-item {
        width: 100%;
        margin-bottom: 4px;
    }

    .personnel-info {
        padding-top: 6px;
        margin-top: 2px;
    }

    .personnel-item {
        justify-content: flex-start;
    }

    /* Other responsive styles */
    .grades-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .grades-actions {
        width: 100%;
        flex-direction: column;
        gap: 8px;
    }

    .search-box {
        width: 100%;
    }

    .search-box input {
        width: 100%;
    }

    .top-students {
        flex-direction: column;
    }

    .top-student {
        min-width: 0;
    }

    .pagination-container {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .pagination {
        width: 100%;
        justify-content: space-between;
    }

    .pagination-pages {
        flex-wrap: wrap;
        justify-content: center;
    }

    /* Adjust table for small screens */
    .grades-table {
        font-size: 0.8rem;
    }

    .grades-table thead th,
    .grades-table tbody td {
        padding: 6px 8px;
    }

    /* Hide less important columns on very small screens */
    .grades-table th:nth-child(1),
    .grades-table td:nth-child(1) {
        display: none;
    }
}