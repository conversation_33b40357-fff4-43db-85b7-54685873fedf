/* PDF Generation Specific Styles */

/* Ensure colors are properly displayed in PDF */
.generating-pdf .class-card.lecture {
    background-color: #f0f7ff !important;
    border-top: 4px solid #4361ee !important;
}

.generating-pdf .class-card.lecture .class-subject {
    background-color: #4361ee !important;
    color: white !important;
}

.generating-pdf .class-card.tp {
    background-color: #e6ffe6 !important;
    border-top: 4px solid #2b9348 !important;
}

.generating-pdf .class-card.tp .class-subject {
    background-color: #2b9348 !important;
    color: white !important;
}

.generating-pdf .class-card.td {
    background-color: #fff7e6 !important;
    border-top: 4px solid #ff9f1c !important;
}

.generating-pdf .class-card.td .class-subject {
    background-color: #ff9f1c !important;
    color: white !important;
}

.generating-pdf .class-card.exam {
    background-color: #ffe6e6 !important;
    border-top: 4px solid #e63946 !important;
}

.generating-pdf .class-card.exam .class-subject {
    background-color: #e63946 !important;
    color: white !important;
}

.generating-pdf .class-card.workshop {
    background-color: #f0e6ff !important;
    border-top: 4px solid #7209b7 !important;
}

.generating-pdf .class-card.workshop .class-subject {
    background-color: #7209b7 !important;
    color: white !important;
}

.generating-pdf .class-card.seminar {
    background-color: #e6f9ff !important;
    border-top: 4px solid #118ab2 !important;
}

.generating-pdf .class-card.seminar .class-subject {
    background-color: #118ab2 !important;
    color: white !important;
}

/* Default styling for other card types */
.generating-pdf .class-card {
    background-color: #f8f9fa !important;
    border-top: 4px solid #6c757d !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08) !important;
    border-radius: 8px !important;
    padding: 12px !important;
    margin: 5px !important;
    min-height: 130px !important;
}

.generating-pdf .class-card .class-subject {
    background-color: #6c757d !important;
    color: white !important;
    position: absolute !important;
    bottom: 10px !important;
    left: 10px !important;
    padding: 3px 8px !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    font-weight: bold !important;
}

/* Table styling for PDF */
.generating-pdf .timetable {
    width: 100% !important;
    table-layout: fixed !important;
    border-collapse: collapse !important;
    border: 1px solid #dee2e6 !important;
}

.generating-pdf .timetable th,
.generating-pdf .timetable td {
    border: 1px solid #dee2e6 !important;
    padding: 8px !important;
    text-align: center !important;
    vertical-align: top !important;
}

.generating-pdf .day-slot {
    font-weight: 600 !important;
    color: #343a40 !important;
    background: #f8f9fa !important;
    border-right: 2px solid #dee2e6 !important;
}

.generating-pdf .time-header {
    font-weight: 600 !important;
    color: #343a40 !important;
    background: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
}

/* PDF loading overlay */
.pdf-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.pdf-loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
}

.pdf-loading-text {
    color: white;
    font-size: 18px;
    font-weight: 500;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}