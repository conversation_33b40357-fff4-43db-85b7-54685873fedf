<?php
// Ensure no output before headers
ob_start();

require_once "../utils/response.php";
require_once "../controller/seanceController.php";

// Set proper content type and CORS headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Get seance by ID
        if (isset($_GET['id'])) {
            getSeanceByIdAPI($_GET['id']);
        }
        // Get seances by filters
        else if (isset($_GET['filiere']) || isset($_GET['niveau']) || isset($_GET['groupe']) || isset($_GET['semestre']) || isset($_GET['semaine'])) {
            $filiere = isset($_GET['filiere']) ? $_GET['filiere'] : null;
            $niveau = isset($_GET['niveau']) ? $_GET['niveau'] : null;
            $groupe = isset($_GET['groupe']) ? $_GET['groupe'] : null;
            $semestre = isset($_GET['semestre']) ? $_GET['semestre'] : null;
            $semaine = isset($_GET['semaine']) ? $_GET['semaine'] : null;

            getSeancesByFiltersAPI($filiere, $niveau, $groupe, $semestre, $semaine);
        }
        // Get all seances
        else {
            getAllSeancesAPI();
        }
        break;

    case 'POST':
        // Get JSON data from request body
        $jsonInput = file_get_contents('php://input');

        // Log the raw input for debugging
        error_log("POST request to seanceRoute.php - Raw input: " . $jsonInput);

        // Check if the input is empty
        if (empty($jsonInput)) {
            jsonResponse(['error' => 'Empty request body'], 400);
        }

        // Check if the input is valid JSON
        if (!isValidJson($jsonInput)) {
            error_log("Invalid JSON in POST request: " . json_last_error_msg());
            jsonResponse(['error' => 'Invalid JSON data: ' . json_last_error_msg()], 400);
        }

        $data = json_decode($jsonInput, true);
        error_log("Decoded JSON data: " . print_r($data, true));

        try {
            addSeanceAPI($data);
        } catch (Exception $e) {
            error_log("Exception in addSeanceAPI: " . $e->getMessage());
            jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
        }
        break;

    case 'PUT':
        // Get JSON data from request body
        $jsonInput = file_get_contents('php://input');

        // Log the raw input for debugging
        error_log("PUT request to seanceRoute.php - Raw input: " . $jsonInput);

        // Check if the input is empty
        if (empty($jsonInput)) {
            jsonResponse(['error' => 'Empty request body'], 400);
        }

        // Check if the input is valid JSON
        if (!isValidJson($jsonInput)) {
            error_log("Invalid JSON in PUT request: " . json_last_error_msg());
            jsonResponse(['error' => 'Invalid JSON data: ' . json_last_error_msg()], 400);
        }

        $data = json_decode($jsonInput, true);
        error_log("Decoded JSON data for update: " . print_r($data, true));

        try {
            updateSeanceAPI($data);
        } catch (Exception $e) {
            error_log("Exception in updateSeanceAPI: " . $e->getMessage());
            jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
        }
        break;

    case 'DELETE':
        error_log("DELETE request to seanceRoute.php - Parameters: " . print_r($_GET, true));

        if (!isset($_GET['id'])) {
            jsonResponse(['error' => 'Missing ID parameter'], 400);
        }

        $id = $_GET['id'];
        error_log("Deleting seance with ID: " . $id);

        try {
            deleteSeanceAPI($id);
        } catch (Exception $e) {
            error_log("Exception in deleteSeanceAPI: " . $e->getMessage());
            jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

// Using jsonResponse from utils/response.php

// Function to check if a string is valid JSON
function isValidJson($string) {
    if (!is_string($string) || trim($string) === '') {
        return false;
    }

    // Attempt to decode the string
    json_decode($string);

    // Check for errors
    return json_last_error() === JSON_ERROR_NONE;
}
?>