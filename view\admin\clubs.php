<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';

// Check if user is logged in and is admin
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Clubs - UniAdmin</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/clubs.css">
</head>
<body>
    <div class="dashboard-container">
        <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';
 include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="clubs-container">
                    <!-- Page Title with gradient header -->
                    <div class="clubs-header">
                        <h1><i class="bi bi-people-fill me-2"></i>Clubs Universitaires</h1>
                        <p class="subtitle">Gérer les clubs et leurs membres pour enrichir la vie étudiante</p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons mb-4">
                        <button id="addClubBtn" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Ajouter un Club
                        </button>
                    </div>

                    <!-- Clubs List -->
                    <div class="clubs-list">
                        <div class="row" id="clubsContainer">
                            <!-- Clubs will be dynamically added here -->
                            <div class="col-12 text-center py-5">
                                <div class="spinner-border" role="status" style="color: #80DEEA;">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="mt-3" style="color: #00838F; font-weight: 500;">
                                    <i class="bi bi-arrow-repeat me-2"></i>Chargement des clubs...
                                </p>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div id="paginationContainer" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Club Modal -->
    <div class="modal fade" id="clubModal" tabindex="-1" aria-labelledby="clubModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="clubModalLabel">Ajouter un Club</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="clubForm">
                        <input type="hidden" id="clubId">

                        <div class="mb-3">
                            <label for="clubName" class="form-label"><i class="bi bi-tag me-2"></i>Nom du Club</label>
                            <input type="text" class="form-control" id="clubName" required placeholder="Entrez le nom du club">
                        </div>

                        <div class="mb-3">
                            <label for="clubDescription" class="form-label"><i class="bi bi-card-text me-2"></i>Description</label>
                            <textarea class="form-control" id="clubDescription" rows="3" required placeholder="Décrivez les activités et objectifs du club"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="clubCreationDate" class="form-label"><i class="bi bi-calendar-event me-2"></i>Date de Création</label>
                            <input type="date" class="form-control" id="clubCreationDate" required>
                        </div>

                        <div class="mb-3">
                            <label for="clubLogo" class="form-label"><i class="bi bi-image me-2"></i>Logo (URL)</label>
                            <input type="text" class="form-control" id="clubLogo" placeholder="https://exemple.com/image.png">
                            <small class="text-muted">Laissez vide pour utiliser une lettre comme logo par défaut</small>
                        </div>

                        <div class="mb-3">
                            <label for="clubStatus" class="form-label"><i class="bi bi-toggle-on me-2"></i>Statut</label>
                            <select class="form-select" id="clubStatus">
                                <option value="actif">Actif</option>
                                <option value="inactif">Inactif</option>
                            </select>
                        </div>

                        <h5 class="mt-4 mb-3"><i class="bi bi-people-fill me-2"></i>Membres du Bureau</h5>

                        <div class="mb-3">
                            <label for="clubPresidentSearch" class="form-label"><i class="bi bi-person-fill me-2" style="color: #FF9800;"></i>Président</label>
                            <div class="student-search-container">
                                <input type="text" class="form-control student-search" id="clubPresidentSearch" placeholder="Rechercher un étudiant..." autocomplete="off">
                                <input type="hidden" id="clubPresident">
                                <div class="student-search-results" id="clubPresidentResults"></div>
                                <div class="selected-student" id="clubPresidentSelected"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="clubVicePresidentSearch" class="form-label"><i class="bi bi-person me-2" style="color: #03A9F4;"></i>Vice-Président</label>
                            <div class="student-search-container">
                                <input type="text" class="form-control student-search" id="clubVicePresidentSearch" placeholder="Rechercher un étudiant..." autocomplete="off">
                                <input type="hidden" id="clubVicePresident">
                                <div class="student-search-results" id="clubVicePresidentResults"></div>
                                <div class="selected-student" id="clubVicePresidentSelected"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="clubSecretarySearch" class="form-label"><i class="bi bi-pencil me-2" style="color: #9C27B0;"></i>Secrétaire</label>
                            <div class="student-search-container">
                                <input type="text" class="form-control student-search" id="clubSecretarySearch" placeholder="Rechercher un étudiant..." autocomplete="off">
                                <input type="hidden" id="clubSecretary">
                                <div class="student-search-results" id="clubSecretaryResults"></div>
                                <div class="selected-student" id="clubSecretarySelected"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="clubTreasurerSearch" class="form-label"><i class="bi bi-cash-coin me-2" style="color: #4CAF50;"></i>Trésorier</label>
                            <div class="student-search-container">
                                <input type="text" class="form-control student-search" id="clubTreasurerSearch" placeholder="Rechercher un étudiant..." autocomplete="off">
                                <input type="hidden" id="clubTreasurer">
                                <div class="student-search-results" id="clubTreasurerResults"></div>
                                <div class="selected-student" id="clubTreasurerSelected"></div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>Annuler
                    </button>
                    <button type="button" class="btn btn-primary" id="saveClubBtn">
                        <i class="bi bi-check-circle me-2"></i>Enregistrer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Confirmer la Suppression</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <i class="bi bi-exclamation-triangle-fill text-warning" style="font-size: 3rem;"></i>
                    </div>
                    <p class="text-center">Êtes-vous sûr de vouloir supprimer ce club? Cette action est irréversible.</p>
                    <p class="text-center fw-bold fs-5 mt-3"><span id="deleteClubName" style="color: #C62828;"></span></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>Annuler
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="bi bi-trash me-2"></i>Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>



    <!-- Club Programme Modal -->
    <div class="modal fade" id="programmeModal" tabindex="-1" aria-labelledby="programmeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="programmeModalLabel">Programme Annuel du Club</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 id="clubProgrammeTitle" class="mb-0"></h4>
                        <button id="addProgrammeBtn" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle me-2"></i>Ajouter une Activité
                        </button>
                    </div>

                    <div id="programmeTableContainer">
                        <div class="text-center py-4">
                            <div class="spinner-border" role="status" style="color: #80DEEA;">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-3" style="color: #00838F; font-weight: 500;">
                                <i class="bi bi-arrow-repeat me-2"></i>Chargement du programme...
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Programme Item Modal -->
    <div class="modal fade" id="addProgrammeModal" tabindex="-1" aria-labelledby="addProgrammeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addProgrammeModalLabel">Ajouter une Activité au Programme</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="programmeForm">
                        <input type="hidden" id="programmeClubId">

                        <div class="mb-3">
                            <label for="programmeTitle" class="form-label"><i class="bi bi-tag me-2"></i>Titre</label>
                            <input type="text" class="form-control" id="programmeTitle" required placeholder="Titre de l'activité">
                        </div>

                        <div class="mb-3">
                            <label for="programmeDescription" class="form-label"><i class="bi bi-card-text me-2"></i>Description</label>
                            <textarea class="form-control" id="programmeDescription" rows="3" required placeholder="Description de l'activité"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="programmeType" class="form-label"><i class="bi bi-list-check me-2"></i>Type d'activité</label>
                            <select class="form-select" id="programmeType" required>
                                <option value="evenement">Événement</option>
                                <option value="sortie">Sortie</option>
                                <option value="formation">Formation</option>
                                <option value="reunion">Réunion</option>
                                <option value="autre">Autre</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="programmeStartDate" class="form-label"><i class="bi bi-calendar-event me-2"></i>Date de début</label>
                            <input type="datetime-local" class="form-control" id="programmeStartDate" required>
                        </div>

                        <div class="mb-3">
                            <label for="programmeEndDate" class="form-label"><i class="bi bi-calendar-event me-2"></i>Date de fin</label>
                            <input type="datetime-local" class="form-control" id="programmeEndDate" required>
                        </div>

                        <div class="mb-3">
                            <label for="programmeLocation" class="form-label"><i class="bi bi-geo-alt me-2"></i>Lieu</label>
                            <input type="text" class="form-control" id="programmeLocation" required placeholder="Lieu de l'activité">
                        </div>

                        <div class="mb-3">
                            <label for="programmeStatus" class="form-label"><i class="bi bi-flag me-2"></i>Statut</label>
                            <select class="form-select" id="programmeStatus">
                                <option value="planifie">Planifié</option>
                                <option value="en_cours">En cours</option>
                                <option value="termine">Terminé</option>
                                <option value="annule">Annulé</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>Annuler
                    </button>
                    <button type="button" class="btn btn-primary" id="saveProgrammeBtn">
                        <i class="bi bi-check-circle me-2"></i>Enregistrer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Programme Confirmation Modal -->
    <div class="modal fade" id="deleteProgrammeModal" tabindex="-1" aria-labelledby="deleteProgrammeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteProgrammeModalLabel">Confirmer la Suppression</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <i class="bi bi-exclamation-triangle-fill text-warning" style="font-size: 3rem;"></i>
                    </div>
                    <p class="text-center">Êtes-vous sûr de vouloir supprimer cette activité du programme? Cette action est irréversible.</p>
                    <p class="text-center fw-bold fs-5 mt-3"><span id="deleteProgrammeTitle" style="color: #C62828;"></span></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>Annuler
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteProgrammeBtn">
                        <i class="bi bi-trash me-2"></i>Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/clubs.js"></script>
    <script src="../assets/js/sidebar.js"></script>
</body>
</html>