<?php
/**
 * Contrôleur commun pour les fonctions partagées
 *
 * Ce fichier contient les fonctions de contrôle communes à plusieurs parties de l'application
 * pour éviter la redondance de code et faciliter la maintenance.
 */

// Inclure le modèle commun
require_once __DIR__ . '/../model/commonModel.php';

/**
 * API pour récupérer les semestres
 *
 * @param int $niveauId Optionnel - ID du niveau pour filtrer les semestres
 */
function getSemestersAPI($niveauId = null) {
    // Récupérer les semestres
    $semesters = getSemesters($niveauId);

    // Vérifier s'il y a une erreur
    if (isset($semesters['error'])) {
        jsonResponse(['error' => $semesters['error']], 500);
        exit;
    }

    // Retourner les données
    jsonResponse(['data' => $semesters], 200);
}

/**
 * API pour récupérer les niveaux
 *
 * @param int $cycleId Optionnel - ID du cycle pour filtrer les niveaux
 * @param int $filiereId Optionnel - ID de la filière pour filtrer les niveaux
 */
function getLevelsAPI($cycleId = null, $filiereId = null) {
    // Récupérer les niveaux
    $levels = getLevels($cycleId, $filiereId);

    // Vérifier s'il y a une erreur
    if (isset($levels['error'])) {
        jsonResponse(['error' => $levels['error']], 500);
        exit;
    }

    // Retourner les données
    jsonResponse(['data' => $levels], 200);
}

/**
 * API pour récupérer les filières
 *
 * @param int $departementId Optionnel - ID du département pour filtrer les filières
 */
function getFilieresAPI($departementId = null) {
    // Récupérer les filières
    $filieres = getFilieres($departementId);

    // Vérifier s'il y a une erreur
    if (isset($filieres['error'])) {
        jsonResponse(['error' => $filieres['error']], 500);
        exit;
    }

    // Retourner les données
    jsonResponse(['data' => $filieres], 200);
}

/**
 * API pour récupérer les départements
 */
function getDepartementsAPI() {
    // Récupérer les départements
    $departements = getDepartements();

    // Vérifier s'il y a une erreur
    if (isset($departements['error'])) {
        jsonResponse(['error' => $departements['error']], 500);
        exit;
    }

    // Retourner les données
    jsonResponse(['data' => $departements], 200);
}

/**
 * API pour récupérer les cycles
 */
function getCyclesAPI() {
    // Récupérer les cycles
    $cycles = getCycles();

    // Vérifier s'il y a une erreur
    if (isset($cycles['error'])) {
        jsonResponse(['error' => $cycles['error']], 500);
        exit;
    }

    // Retourner les données
    jsonResponse(['data' => $cycles], 200);
}

/**
 * API pour récupérer les types d'UE
 */
function getUETypesAPI() {
    // Récupérer les types d'UE
    $ueTypes = getUETypes();

    // Retourner les données
    jsonResponse(['data' => $ueTypes], 200);
}

/**
 * API pour récupérer les années académiques
 *
 * @param int $count Nombre d'années à générer
 */
function getAcademicYearsAPI($count = 6) {
    // Récupérer les années académiques
    $academicYears = getAcademicYears($count);

    // Retourner les données
    jsonResponse(['data' => $academicYears], 200);
}

/**
 * Fonction utilitaire pour retourner une réponse JSON
 *
 * @param array $data Données à retourner
 * @param int $statusCode Code de statut HTTP
 */
function jsonResponse($data, $statusCode = 200) {
    // Désactiver l'affichage des erreurs PHP
    $displayErrors = ini_get('display_errors');
    ini_set('display_errors', 0);

    try {
        // Définir le code de statut HTTP
        http_response_code($statusCode);

        // Définir les en-têtes pour empêcher la mise en cache
        header('Content-Type: application/json; charset=utf-8');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Encoder les données en JSON
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        // Vérifier si l'encodage a réussi
        if ($jsonData === false) {
            // Journaliser l'erreur
            error_log("Erreur d'encodage JSON: " . json_last_error_msg());

            // Renvoyer une erreur générique
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de la génération de la réponse JSON'], JSON_UNESCAPED_UNICODE);
        } else {
            // Envoyer les données JSON
            echo $jsonData;
        }
    } catch (Exception $e) {
        // Journaliser l'erreur
        error_log("Exception dans jsonResponse: " . $e->getMessage());

        // Renvoyer une erreur générique
        http_response_code(500);
        echo json_encode(['error' => 'Erreur interne du serveur'], JSON_UNESCAPED_UNICODE);
    } finally {
        // Restaurer le paramètre d'affichage des erreurs
        ini_set('display_errors', $displayErrors);

        // Terminer l'exécution du script
        exit;
    }
}
