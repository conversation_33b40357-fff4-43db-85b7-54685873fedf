<?php
// Set headers for JSON response first
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Include necessary files after headers
require_once "../controller/ListerAffUECordController.php";

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Handle GET requests
        if (isset($_GET['action'])) {
            $action = $_GET['action'];

            switch ($action) {
                case 'getListerAffUECordByFiliere':
                    if (!isset($_GET['filiere_id'])) {
                        jsonResponse(['error' => 'Filiere ID is required'], 400);
                    }
                    getListerAffUECordByFiliereAPI($_GET['filiere_id']);
                    break;

                default:
                    jsonResponse(['error' => 'Invalid action'], 400);
                    break;
            }
        } else {
            jsonResponse(['error' => 'Action is required'], 400);
        }
        break;

    default:
        jsonResponse(['error' => 'Method not allowed'], 405);
        break;
}
?>