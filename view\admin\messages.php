<?php
// Vérifier l'authentification
require_once '../includes/auth_check_admin.php';

require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../model/messagesModel.php';
require_once __DIR__ . '/../../controller/messagesController.php';

// Handle AJAX requests for receiver data
if (isset($_GET['action']) && $_GET['action'] === 'get_receivers') {
    // Get the type of receivers to fetch
    $type = isset($_GET['type']) ? $_GET['type'] : 'all';

    $response = [
        'success' => true,
        'data' => []
    ];

    // Connect to the database
    $conn = getConnection();

    // Fetch students if requested
    if ($type === 'all' || $type === 'students') {
        $studentsResult = $conn->query("SELECT id_etudiant, nom, prenom FROM etudiant LIMIT 20");
        $students = [];

        if ($studentsResult && $studentsResult->num_rows > 0) {
            while ($student = $studentsResult->fetch_assoc()) {
                $students[] = [
                    'id' => $student['id_etudiant'],
                    'name' => $student['prenom'] . ' ' . $student['nom']
                ];
            }
        }

        $response['data']['students'] = $students;
    }

    // Fetch teachers if requested
    if ($type === 'all' || $type === 'teachers') {
        $teachersResult = $conn->query("SELECT id_enseignant, nom, prenom FROM enseignant LIMIT 20");
        $teachers = [];

        if ($teachersResult && $teachersResult->num_rows > 0) {
            while ($teacher = $teachersResult->fetch_assoc()) {
                $teachers[] = [
                    'id' => $teacher['id_enseignant'],
                    'name' => $teacher['prenom'] . ' ' . $teacher['nom']
                ];
            }
        }

        $response['data']['teachers'] = $teachers;
    }

    // Fetch users if requested
    if ($type === 'all' || $type === 'users') {
        $usersResult = $conn->query("SELECT id_user, username, role FROM users LIMIT 20");
        $users = [];

        if ($usersResult && $usersResult->num_rows > 0) {
            while ($user = $usersResult->fetch_assoc()) {
                $users[] = [
                    'id' => $user['id_user'],
                    'username' => $user['username'] ?? 'N/A',
                    'role' => $user['role'] ?? 'N/A'
                ];
            }
        }

        $response['data']['users'] = $users;
    }

    // Return the data as JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Get unread count (without authentication)
$unreadCount = getUnreadCount();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniAdmin - Messages</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/messages.css">
</head>
<body>
    <div class="dashboards-container">

        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="messages-content">
                <div class="messages-header">
                    <h1>MESSAGES</h1>
                    <div class="messages-actions">
                        <button id="createMessageBtn" class="create-message-btn">Create Message</button>
                        <button id="markAllBtn" class="mark-all-btn">Mark all as read</button>
                    </div>
                </div>



                <div id="notificationsList" class="notifications-list">
                    <!-- Messages will be rendered dynamically via JS -->
                </div>

                <!-- Create Message Modal -->
                <div class="modal fade" id="createMessageModal" tabindex="-1" aria-labelledby="createMessageModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="createMessageModalLabel">Create New Message</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="createMessageForm">
                                    <div class="mb-3">
                                        <label for="messageTitle" class="form-label">Title</label>
                                        <input type="text" class="form-control" id="messageTitle" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="receiverUsername" class="form-label">Receiver</label>
                                        <div class="autocomplete-container">
                                            <input type="text" class="form-control" id="receiverUsername" placeholder="Start typing username...">
                                            <div class="autocomplete-items" id="autocompleteResults" style="display: none;"></div>
                                        </div>
                                        <input type="hidden" id="receiverId">
                                        <small class="form-text text-muted">Start typing to search for users</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="messageContent" class="form-label">Message Content</label>
                                        <textarea class="form-control" id="messageContent" rows="4" required></textarea>
                                    </div>

                                    <!-- Toolbar for media URL and file path -->
                                    <div class="mb-3">
                                        <div class="message-toolbar">
                                            <button type="button" class="btn btn-icon" id="toggleMediaUrlBtn" title="Add Media URL">
                                                <i class="fas fa-link"></i>
                                            </button>
                                            <button type="button" class="btn btn-icon" id="toggleFilePathBtn" title="Add File Path">
                                                <i class="fas fa-paperclip"></i>
                                            </button>
                                        </div>

                                        <!-- Media URL input -->
                                        <div class="collapse mb-2" id="mediaUrlCollapse">
                                            <div class="input-group">
                                                <input type="url" class="form-control" id="mediaUrlInput" placeholder="Enter media URL (https://example.com/media)">
                                                <button class="btn btn-primary" type="button" id="addMediaUrlToMessageBtn"><i class="fas fa-check"></i></button>
                                                <button class="btn btn-light" type="button" id="cancelMediaUrlBtn"><i class="fas fa-times"></i></button>
                                            </div>
                                        </div>

                                        <!-- File Upload input -->
                                        <div class="collapse mb-2" id="filePathCollapse">
                                            <div class="input-group">
                                                <input type="file" class="form-control" id="fileInput">
                                                <button class="btn btn-primary" type="button" id="addFileToMessageBtn"><i class="fas fa-check"></i></button>
                                                <button class="btn btn-light" type="button" id="cancelFilePathBtn"><i class="fas fa-times"></i></button>
                                            </div>
                                        </div>

                                        <!-- Attachment indicators -->
                                        <div id="attachmentIndicators"></div>
                                    </div>

                                    <!-- Hidden fields for media URL and file path -->
                                    <input type="hidden" id="mediaUrl">
                                    <input type="hidden" id="filePath">
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="sendMessageBtn">Send Message</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Le modal "Check Receivers" a été remplacé par l'autocomplétion -->

                <!-- Success Modal -->
                <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-success text-white">
                                <h5 class="modal-title" id="successModalLabel"><i class="fas fa-check-circle me-2"></i>Succès</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                </div>
                                <p class="text-center fs-5" id="successMessage">Message envoyé avec succès!</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-success" data-bs-dismiss="modal">OK</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Confirmation Modal -->
                <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-white">
                                <h5 class="modal-title" id="confirmationModalLabel"><i class="fas fa-exclamation-triangle me-2"></i>Confirmation</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <i class="fas fa-question-circle text-warning" style="font-size: 4rem;"></i>
                                </div>
                                <p class="text-center fs-5" id="confirmationMessage">Êtes-vous sûr de vouloir effectuer cette action?</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-warning" id="confirmActionBtn">Confirmer</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Les modales ont été remplacées par des sections collapsibles dans le formulaire principal -->

    <!-- JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        // Fonction pour initialiser l'autocomplétion des utilisateurs
        function initUserAutocomplete() {
            const receiverUsername = document.getElementById('receiverUsername');
            const receiverId = document.getElementById('receiverId');
            const autocompleteResults = document.getElementById('autocompleteResults');

            let debounceTimer;

            // Ajouter un écouteur d'événement pour la saisie dans le champ de recherche
            receiverUsername.addEventListener('input', function() {
                const query = this.value.trim();

                // Effacer le timer de debounce précédent
                clearTimeout(debounceTimer);

                // Masquer les résultats si le champ est vide
                if (query === '') {
                    autocompleteResults.style.display = 'none';
                    receiverId.value = '';
                    return;
                }

                // Définir un nouveau timer de debounce
                debounceTimer = setTimeout(function() {
                    // Effectuer la recherche
                    console.log('Searching for users with query:', query);
                    fetch(`../../route/userSearchRoute.php?query=${encodeURIComponent(query)}`)
                        .then(response => {
                            console.log('Response status:', response.status);
                            return response.json();
                        })
                        .then(data => {
                            console.log('Response data:', data);
                            if (data.success && data.users && data.users.length > 0) {
                                // Afficher les résultats
                                displayAutocompleteResults(data.users);
                            } else {
                                // Aucun résultat trouvé
                                autocompleteResults.innerHTML = '<div class="autocomplete-item">No users found</div>';
                                autocompleteResults.style.display = 'block';
                            }
                        })
                        .catch(error => {
                            console.error('Error searching users:', error);
                            autocompleteResults.innerHTML = '<div class="autocomplete-item">Error searching users</div>';
                            autocompleteResults.style.display = 'block';
                        });
                }, 300); // Attendre 300ms après la dernière frappe
            });

            // Fonction pour afficher les résultats de l'autocomplétion
            function displayAutocompleteResults(users) {
                // Vider les résultats précédents
                autocompleteResults.innerHTML = '';

                // Ajouter chaque utilisateur aux résultats
                users.forEach(user => {
                    const item = document.createElement('div');
                    item.className = 'autocomplete-item';
                    item.innerHTML = `
                        <div class="username">${user.username}</div>
                        <div class="user-info">ID: ${user.id} | Role: ${user.role}</div>
                    `;

                    // Ajouter un écouteur d'événement pour la sélection d'un utilisateur
                    item.addEventListener('click', function() {
                        receiverUsername.value = user.username;
                        receiverId.value = user.id;
                        autocompleteResults.style.display = 'none';
                    });

                    autocompleteResults.appendChild(item);
                });

                // Afficher les résultats
                autocompleteResults.style.display = 'block';
            }

            // Masquer les résultats lorsque l'utilisateur clique en dehors
            document.addEventListener('click', function(e) {
                if (!receiverUsername.contains(e.target) && !autocompleteResults.contains(e.target)) {
                    autocompleteResults.style.display = 'none';
                }
            });

            // Navigation au clavier dans les résultats
            receiverUsername.addEventListener('keydown', function(e) {
                const items = autocompleteResults.querySelectorAll('.autocomplete-item');
                const activeItem = autocompleteResults.querySelector('.autocomplete-item.active');

                // Si aucun résultat n'est affiché, ne rien faire
                if (autocompleteResults.style.display === 'none' || items.length === 0) {
                    return;
                }

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        if (!activeItem) {
                            // Aucun élément actif, activer le premier
                            items[0].classList.add('active');
                        } else {
                            // Passer à l'élément suivant
                            const nextItem = activeItem.nextElementSibling;
                            if (nextItem) {
                                activeItem.classList.remove('active');
                                nextItem.classList.add('active');
                            }
                        }
                        break;

                    case 'ArrowUp':
                        e.preventDefault();
                        if (activeItem) {
                            // Passer à l'élément précédent
                            const prevItem = activeItem.previousElementSibling;
                            if (prevItem) {
                                activeItem.classList.remove('active');
                                prevItem.classList.add('active');
                            }
                        }
                        break;

                    case 'Enter':
                        e.preventDefault();
                        if (activeItem) {
                            // Simuler un clic sur l'élément actif
                            activeItem.click();
                        }
                        break;

                    case 'Escape':
                        e.preventDefault();
                        autocompleteResults.style.display = 'none';
                        break;
                }
            });
        }

        // Initialisation des sections collapsibles
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initialisation des sections collapsibles...');

            // Créer des instances de collapse
            var mediaUrlCollapse = new bootstrap.Collapse(document.getElementById('mediaUrlCollapse'), {
                toggle: false
            });

            var filePathCollapse = new bootstrap.Collapse(document.getElementById('filePathCollapse'), {
                toggle: false
            });

            // Ajouter des écouteurs d'événements pour les boutons d'icônes
            document.getElementById('toggleMediaUrlBtn').addEventListener('click', function() {
                console.log('Bouton URL cliqué');
                // Fermer l'autre section si elle est ouverte
                if (document.getElementById('filePathCollapse').classList.contains('show')) {
                    filePathCollapse.hide();
                }
                // Basculer la section d'URL
                mediaUrlCollapse.toggle();
            });

            document.getElementById('toggleFilePathBtn').addEventListener('click', function() {
                console.log('Bouton fichier cliqué');
                // Fermer l'autre section si elle est ouverte
                if (document.getElementById('mediaUrlCollapse').classList.contains('show')) {
                    mediaUrlCollapse.hide();
                }
                // Basculer la section de fichier
                filePathCollapse.toggle();
            });

            // Gérer les boutons d'annulation
            document.getElementById('cancelMediaUrlBtn').addEventListener('click', function() {
                mediaUrlCollapse.hide();
                document.getElementById('mediaUrlInput').value = '';
            });

            document.getElementById('cancelFilePathBtn').addEventListener('click', function() {
                filePathCollapse.hide();
                document.getElementById('filePathInput').value = '';
            });

            // Ajouter des écouteurs pour les boutons d'ajout
            document.getElementById('addMediaUrlToMessageBtn').addEventListener('click', function() {
                console.log('Ajout URL');
                const mediaUrlInput = document.getElementById('mediaUrlInput');
                const mediaUrl = document.getElementById('mediaUrl');

                if (mediaUrlInput && mediaUrl && mediaUrlInput.value.trim()) {
                    mediaUrl.value = mediaUrlInput.value;

                    // Afficher l'indicateur dans la zone de contenu du message
                    showAttachmentIndicator('mediaUrl', mediaUrlInput.value);

                    // Masquer la section
                    mediaUrlCollapse.hide();

                    // Réinitialiser le champ
                    mediaUrlInput.value = '';
                }
            });

            document.getElementById('addFileToMessageBtn').addEventListener('click', function() {
                console.log('Ajout fichier');
                const fileInput = document.getElementById('fileInput');

                if (fileInput && fileInput.files.length > 0) {
                    const file = fileInput.files[0];

                    // Stocker le fichier pour l'upload ultérieur
                    window.selectedFile = file;

                    // Afficher l'indicateur dans la zone de contenu du message
                    showAttachmentIndicator('filePath', file.name);

                    // Masquer la section
                    filePathCollapse.hide();

                    // Réinitialiser le champ
                    fileInput.value = '';
                } else {
                    // Utiliser la fonction showErrorModal du fichier messages.js
                    if (typeof showErrorModal === 'function') {
                        showErrorModal('Veuillez sélectionner un fichier à télécharger');
                    } else {
                        // Fallback si la fonction n'est pas encore chargée
                        console.error('Veuillez sélectionner un fichier à télécharger');
                    }
                }
            });

            // Ajouter un écouteur pour le bouton d'envoi de message
            document.getElementById('sendMessageBtn').addEventListener('click', function() {
                console.log('Envoi du message');
                createNewMessage();
            });

            // Initialiser l'autocomplétion pour le champ de recherche d'utilisateurs
            initUserAutocomplete();
        });

        // Fonction pour afficher l'indicateur de pièce jointe
        function showAttachmentIndicator(type, value) {
            // Remove existing indicator if any
            const existingIndicator = document.querySelector(`.message-attachment-indicator[data-type="${type}"]`);
            if (existingIndicator) {
                existingIndicator.remove();
            }

            if (!value) return;

            const attachmentIndicators = document.getElementById('attachmentIndicators');
            if (!attachmentIndicators) return;

            const indicator = document.createElement('span');
            indicator.className = 'message-attachment-indicator';
            indicator.setAttribute('data-type', type);

            // Tronquer la valeur si elle est trop longue
            const displayValue = value.length > 25 ? value.substring(0, 22) + '...' : value;

            if (type === 'mediaUrl') {
                indicator.innerHTML = `<i class="fas fa-link"></i> ${displayValue} <button type="button" class="btn btn-sm" onclick="removeAttachment('mediaUrl')"><i class="fas fa-times"></i></button>`;
            } else {
                indicator.innerHTML = `<i class="fas fa-paperclip"></i> ${displayValue} <button type="button" class="btn btn-sm" onclick="removeAttachment('filePath')"><i class="fas fa-times"></i></button>`;
            }

            // Ajouter l'indicateur au conteneur
            attachmentIndicators.appendChild(indicator);
        }

        // Fonction pour supprimer une pièce jointe
        function removeAttachment(type) {
            const field = document.getElementById(type);

            // Si c'est un fichier uploadé, on réinitialise également la variable globale
            if (type === 'filePath' && window.selectedFile) {
                window.selectedFile = null;
            }

            if (field) {
                field.value = '';
            }

            const indicator = document.querySelector(`.message-attachment-indicator[data-type="${type}"]`);
            if (indicator) {
                indicator.remove();
            }
        }
    </script>
    <script src="../assets/js/messages.js"></script>
    <script src="../assets/js/sidebar.js"></script>
</body>
</html>
