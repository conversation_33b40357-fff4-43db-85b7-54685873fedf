<?php
// Route file for descriptif functionality

// Set error handling
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("Error in descriptifRoute.php: [$errno] $errstr in $errfile on line $errline");
    header("Content-Type: application/json");
    http_response_code(500);
    echo json_encode(['error' => 'Server error occurred. Please check the logs.']);
    exit();
});

// Set exception handling
set_exception_handler(function($exception) {
    error_log("Uncaught exception in descriptifRoute.php: " . $exception->getMessage());
    header("Content-Type: application/json");
    http_response_code(500);
    echo json_encode(['error' => 'Server error occurred. Please check the logs.']);
    exit();
});

// Set headers for CORS and JSON response
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header("Content-Type: application/json");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include the controller
require_once __DIR__ . "/../controller/descriptifController.php";

// Handle the request based on the action parameter
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getAllUnites':
            getAllUniteEnseignementAPI();
            break;

        case 'getUnitesByModule':
            if (!isset($_GET['module_id'])) {
                jsonResponse(['error' => 'Module ID is required'], 400);
                exit();
            }

            // Validate module_id is numeric
            if (!is_numeric($_GET['module_id'])) {
                jsonResponse(['error' => 'Invalid module ID format'], 400);
                exit();
            }

            try {
                getUniteEnseignementByModuleAPI($_GET['module_id']);
            } catch (Exception $e) {
                error_log("Exception in getUnitesByModule: " . $e->getMessage());
                jsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
            }
            break;

        case 'getUnitesByFiliere':
            if (!isset($_GET['filiere_id'])) {
                jsonResponse(['error' => 'Filiere ID is required'], 400);
                exit();
            }
            getUniteEnseignementByFiliereAPI($_GET['filiere_id']);
            break;

        case 'getUniteById':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Teaching unit ID is required'], 400);
                exit();
            }
            getUniteEnseignementByIdAPI($_GET['id']);
            break;

        case 'createUnite':
            createUniteEnseignementAPI();
            break;

        case 'updateUnite':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Teaching unit ID is required'], 400);
                exit();
            }
            updateUniteEnseignementAPI($_GET['id']);
            break;

        case 'deleteUnite':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Teaching unit ID is required'], 400);
                exit();
            }
            deleteUniteEnseignementAPI($_GET['id']);
            break;

        case 'getAllModules':
            getAllModulesAPI();
            break;

        case 'getModuleById':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Module ID is required'], 400);
                exit();
            }
            getModuleByIdAPI($_GET['id']);
            break;

        case 'createModule':
            createModuleAPI();
            break;

        case 'updateModule':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Module ID is required'], 400);
                exit();
            }
            updateModuleAPI($_GET['id']);
            break;

        case 'deleteModule':
            if (!isset($_GET['id'])) {
                jsonResponse(['error' => 'Module ID is required'], 400);
                exit();
            }
            deleteModuleAPI($_GET['id']);
            break;

        case 'getAllFilieres':
            getAllFilieresAPI();
            break;

        case 'getAllSpecialites':
            getAllSpecialitesAPI();
            break;

        case 'getAllSemestres':
            $niveauId = isset($_GET['niveau_id']) ? $_GET['niveau_id'] : null;
            getAllSemestresAPI($niveauId);
            break;

        case 'getAllNiveaux':
            $filiereId = isset($_GET['filiere_id']) ? $_GET['filiere_id'] : null;
            getAllNiveauxAPI($filiereId);
            break;

        case 'getAllEnseignants':
            getAllEnseignantsAPI();
            break;

        case 'importFromCSV':
            importUniteEnseignementFromCSVAPI();
            break;

        case 'importModules':
            importModulesAPI();
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            exit();
    }
} else {
    jsonResponse(['error' => 'Action parameter is required'], 400);
    exit();
}