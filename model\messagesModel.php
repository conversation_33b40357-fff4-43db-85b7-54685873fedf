<?php
require_once __DIR__ . '/../config/db.php';

function getAllMessages($page = 1, $messagesPerPage = 5) {
    $conn = getConnection();
    $offset = ($page - 1) * $messagesPerPage;

    try {
        // Check if the messages table exists
        $tableCheck = $conn->query("SHOW TABLES LIKE 'messages'");
        if ($tableCheck->num_rows == 0) {
            // Table doesn't exist
            return [
                'messages' => [],
                'total' => 0,
                'pages' => 0,
                'error' => 'Messages table does not exist'
            ];
        }

        // Get the columns in the messages table
        $columnsResult = $conn->query("SHOW COLUMNS FROM messages");
        $columns = [];
        while ($column = $columnsResult->fetch_assoc()) {
            $columns[] = $column['Field'];
        }

        // Build the query based on available columns
        $selectColumns = "id, title";
        if (in_array('sender_id', $columns)) $selectColumns .= ", sender_id";
        if (in_array('receiver_id', $columns)) $selectColumns .= ", receiver_id";
        if (in_array('content', $columns)) $selectColumns .= ", content";
        if (in_array('media_url', $columns)) $selectColumns .= ", media_url";
        if (in_array('file_path', $columns)) $selectColumns .= ", file_path";
        if (in_array('created_at', $columns)) $selectColumns .= ", created_at";
        if (in_array('updated_at', $columns)) $selectColumns .= ", updated_at";
        if (in_array('is_read', $columns)) $selectColumns .= ", is_read";

        $stmt = $conn->prepare("
            SELECT $selectColumns
            FROM messages
            ORDER BY " . (in_array('created_at', $columns) ? "created_at" : "id") . " DESC
            LIMIT ? OFFSET ?
        ");

        if (!$stmt) {
            error_log("Erreur de préparation de la requête: " . $conn->error);
            return [
                'messages' => [],
                'total' => 0,
                'pages' => 0,
                'error' => $conn->error
            ];
        }

        $stmt->bind_param("ii", $messagesPerPage, $offset);
        $result = $stmt->execute();

        if (!$result) {
            error_log("Erreur d'exécution de la requête: " . $stmt->error);
            return [
                'messages' => [],
                'total' => 0,
                'pages' => 0,
                'error' => $stmt->error
            ];
        }

        $result = $stmt->get_result();

        // Get total messages count
        $totalStmt = $conn->prepare("SELECT COUNT(*) as total FROM messages");
        $totalStmt->execute();
        $totalResult = $totalStmt->get_result();
        $totalMessages = $totalResult->fetch_assoc()['total'];

        return [
            'messages' => $result->fetch_all(MYSQLI_ASSOC),
            'total' => $totalMessages,
            'pages' => ceil($totalMessages / $messagesPerPage)
        ];
    } catch (Exception $e) {
        error_log("Exception dans getAllMessages: " . $e->getMessage());
        return [
            'messages' => [],
            'total' => 0,
            'pages' => 0,
            'error' => $e->getMessage()
        ];
    }
}

function markAsRead($id) {
    $conn = getConnection();
    $stmt = $conn->prepare("
        UPDATE messages
        SET is_read = 1
        WHERE id = ?
    ");
    $stmt->bind_param("i", $id);
    return $stmt->execute();
}

function deleteMessage($id) {
    $conn = getConnection();
    $stmt = $conn->prepare("
        DELETE FROM messages
        WHERE id = ?
    ");
    $stmt->bind_param("i", $id);
    return $stmt->execute();
}

function markAllAsRead() {
    $conn = getConnection();
    $stmt = $conn->prepare("
        UPDATE messages
        SET is_read = 1
    ");
    return $stmt->execute();
}

function createMessage($sender_id, $title, $content, $receiver_id = null, $media_url = null, $file_path = null) {
    $conn = getConnection();

    // Préparer la requête avec les champs obligatoires
    $query = "INSERT INTO messages (sender_id, title, content";
    $types = "iss"; // Types pour sender_id, title, content
    $params = [$sender_id, $title, $content];

    // Ajouter receiver_id s'il est fourni
    if ($receiver_id !== null) {
        $query .= ", receiver_id";
        $types .= "i"; // Type pour receiver_id (integer)
        $params[] = $receiver_id;
    }

    // Ajouter media_url s'il est fourni
    if ($media_url !== null) {
        $query .= ", media_url";
        $types .= "s"; // Type pour media_url (string)
        $params[] = $media_url;
    }

    // Ajouter file_path s'il est fourni
    if ($file_path !== null) {
        $query .= ", file_path";
        $types .= "s"; // Type pour file_path (string)
        $params[] = $file_path;
    }

    // Ajouter is_read avec valeur par défaut 0 (non lu)
    $query .= ", is_read";
    $types .= "i";
    $params[] = 0;

    // Fermer la requête
    $query .= ") VALUES (" . str_repeat("?,", count($params) - 1) . "?)";

    // Préparer et exécuter la requête
    $stmt = $conn->prepare($query);

    if (!$stmt) {
        error_log("Erreur de préparation de la requête: " . $conn->error);
        return false;
    }

    // Créer un tableau de références pour bind_param
    $bindParams = array();
    $bindParams[] = $types;
    for ($i = 0; $i < count($params); $i++) {
        $bindParams[] = &$params[$i];
    }

    // Appeler bind_param avec les références
    call_user_func_array(array($stmt, 'bind_param'), $bindParams);

    return $stmt->execute();
}

function getUnreadCount() {
    $conn = getConnection();
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM messages
        WHERE is_read = 0
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    return $row['count'];
}

/**
 * Check if the database needs to be updated with the new columns
 *
 * @return bool True if the database needs to be updated, false otherwise
 */
function checkIfDatabaseNeedsUpdate() {
    $conn = getConnection();

    // Check if the required columns exist in the messages table
    $requiredColumns = [
        'id', 'sender_id', 'receiver_id', 'title', 'content',
        'media_url', 'file_path', 'created_at', 'updated_at', 'is_read'
    ];
    $missingColumns = [];

    $columnsResult = $conn->query("SHOW COLUMNS FROM messages");
    $existingColumns = [];
    while ($column = $columnsResult->fetch_assoc()) {
        $existingColumns[] = $column['Field'];
    }

    foreach ($requiredColumns as $column) {
        if (!in_array($column, $existingColumns)) {
            $missingColumns[] = $column;
        }
    }

    // Return true if any required column is missing
    return !empty($missingColumns);
}

/**
 * Create a message from admin to a specific receiver
 *
 * @param int $sender_id Admin ID
 * @param string $title Message title
 * @param string $content Message content
 * @param int $receiver_id ID of the receiver
 * @param string|null $media_url Optional URL to media content
 * @param string|null $file_path Optional path to a downloadable file
 * @return bool Success status
 */
function createAdminMessage($sender_id, $title, $content, $receiver_id, $media_url = null, $file_path = null) {
    $conn = getConnection();

    // First, check if the sender_id exists in the users table
    // If not, try to find a valid admin user ID
    $checkSender = $conn->prepare("SELECT id_user FROM users WHERE id_user = ? LIMIT 1");
    $checkSender->bind_param("i", $sender_id);
    $checkSender->execute();
    $senderResult = $checkSender->get_result();

    if ($senderResult->num_rows == 0) {
        // Sender ID doesn't exist, try to find an admin user
        $findAdmin = $conn->query("SELECT id_user FROM users WHERE role = 'admin' LIMIT 1");
        if ($findAdmin && $findAdmin->num_rows > 0) {
            $adminRow = $findAdmin->fetch_assoc();
            $sender_id = $adminRow['id_user'];
        } else {
            // If no admin found, try to get any valid user
            $findAnyUser = $conn->query("SELECT id_user FROM users LIMIT 1");
            if ($findAnyUser && $findAnyUser->num_rows > 0) {
                $userRow = $findAnyUser->fetch_assoc();
                $sender_id = $userRow['id_user'];
            } else {
                // No valid users found, cannot proceed
                return false;
            }
        }
    }

    // Validate that the receiver exists in the users table
    $checkReceiver = $conn->prepare("SELECT id_user FROM users WHERE id_user = ? LIMIT 1");
    $checkReceiver->bind_param("i", $receiver_id);
    $checkReceiver->execute();
    $receiverExists = $checkReceiver->get_result()->num_rows > 0;

    if (!$receiverExists) {
        // If not in users table, check in etudiant table
        $checkReceiver = $conn->prepare("SELECT id_etudiant FROM etudiant WHERE id_etudiant = ? LIMIT 1");
        $checkReceiver->bind_param("i", $receiver_id);
        $checkReceiver->execute();
        $receiverExists = $checkReceiver->get_result()->num_rows > 0;

        if (!$receiverExists) {
            // If not in etudiant table, check in enseignant table
            $checkReceiver = $conn->prepare("SELECT id_enseignant FROM enseignant WHERE id_enseignant = ? LIMIT 1");
            $checkReceiver->bind_param("i", $receiver_id);
            $checkReceiver->execute();
            $receiverExists = $checkReceiver->get_result()->num_rows > 0;
        }
    }

    if (!$receiverExists) {
        // Receiver doesn't exist in any table
        return false;
    }

    // Log pour le débogage
    error_log("Inserting message into database with file_path: " . $file_path);

    // Insert the message
    $stmt = $conn->prepare("
        INSERT INTO messages (
            sender_id,
            title,
            content,
            receiver_id,
            media_url,
            file_path,
            is_read
        ) VALUES (?, ?, ?, ?, ?, ?, 0)
    ");

    $stmt->bind_param(
        "ississ",
        $sender_id,
        $title,
        $content,
        $receiver_id,
        $media_url,
        $file_path
    );

    $result = $stmt->execute();

    // Log le résultat
    if ($result) {
        error_log("Message inserted successfully with ID: " . $conn->insert_id);
    } else {
        error_log("Failed to insert message: " . $stmt->error);
    }

    return $result;
}
?>