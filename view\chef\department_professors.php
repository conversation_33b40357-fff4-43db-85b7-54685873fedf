<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Débogage - Afficher la structure de la session
error_log("Structure de la session: " . json_encode($_SESSION));

// Récupérer l'ID du département du chef connecté
$departementId = null;

// Vérifier si le department_id est déjà dans la session
if (isset($_SESSION['user']['department_id'])) {
    $departementId = $_SESSION['user']['department_id'];
    error_log("ID du département récupéré depuis la session: " . $departementId);
}

// Inclure le modèle des visites et enregistrer la visite
require_once '../../model/visitsModel.php';
recordVisit('chef de departement', 'department_professors');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enseignants du Département - Chef de Département</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard-style.css">
    <link rel="stylesheet" href="../assets/css/header-fix.css">

    <!-- Style personnalisé pour cette page -->
    <style>
        :root {
            --primary-color: #6c9bcf;
            --secondary-color: #f8f9fa;
            --accent-color: #a5d8ff;
            --text-color: #495057;
            --light-accent: #e9f2ff;
            --border-color: #dee2e6;
        }

        body {

            color: var(--text-color);
            background-color: #f5f7fa;
            overflow-x: hidden;
            max-width: 100%;
        }

        /* Empêcher le défilement horizontal sur tous les conteneurs */
        .container, .container-fluid, .row, .col, .col-12, .card {
            margin: 0;
            max-width: 100%;
            overflow-x: hidden;
        }

        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border-radius: 10px;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid var(--border-color);
            padding: 15px 20px;
            border-radius: 10px 10px 0 0 !important;
        }

        .card-body {
            padding: 5px;
        }

        .page-title {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .breadcrumb {
            background-color: transparent;
            padding: 0;
            margin-bottom: 1.5rem;
        }

        .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: var(--text-color);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background-color: white;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-outline-success {
            color: #28a745;
            border-color: #28a745;
            background-color: white;
        }

        .btn-outline-success:hover {
            background-color: #28a745;
            color: white;
        }

        .table {
            color: var(--text-color);
            width: 100%;
            table-layout: fixed;
        }

        /* Empêcher le défilement horizontal */
        .dashboard-content {
            overflow-x: hidden;
        }

        /* Gérer les textes longs dans les cellules */
        .table td, .table th {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 0; /* Nécessaire pour que text-overflow fonctionne */
        }

        /* Assurer que les emails longs sont tronqués correctement */
        .table td:nth-child(4) {
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Définir des largeurs spécifiques pour les colonnes */
        .table th:nth-child(1), .table td:nth-child(1) { width: 20%; } /* Nom */
        .table th:nth-child(2), .table td:nth-child(2) { width: 20%; } /* Prénom */
        .table th:nth-child(3), .table td:nth-child(3) { width: 15%; } /* CNI */
        .table th:nth-child(4), .table td:nth-child(4) { width: 25%; } /* Email */
        .table th:nth-child(5), .table td:nth-child(5) { width: 20%; } /* Département/Rôle */

        /* Assurer que les conteneurs de tableaux ne débordent pas */
        .table-responsive {
            overflow-x: hidden;
        }

        .table thead th {
            background-color: var(--light-accent);
            color: var(--primary-color);
            font-weight: 600;
            border-bottom: none;
            padding: 12px 15px;
        }

        .table tbody td {
            padding: 12px 15px;
            vertical-align: middle;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .table-hover tbody tr:hover {
            background-color: var(--light-accent);
            cursor: pointer;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .badge {
            padding: 6px 10px;
            font-weight: 500;
            border-radius: 30px;
        }

        .bg-success {
            background-color: #d1e7dd !important;
            color: #0f5132;
        }

        .bg-danger {
            background-color: #f8d7da !important;
            color: #842029;
        }

        .bg-warning {
            background-color: #fff3cd !important;
            color: #664d03;
        }

        .bg-info {
            background-color: #cff4fc !important;
            color: #055160;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            border-radius: 0.2rem;
        }

        .btn-info {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: #055160;
        }

        .btn-info:hover {
            background-color: #9ecdf8;
            border-color: #9ecdf8;
            color: #055160;
        }

        .alert-warning {
            background-color: #fff8e1;
            border-color: #ffe0b2;
            color: #ff8f00;
        }

        .modal-content {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            max-width: 100%;
            overflow-x: hidden;
        }

        /* Assurer que le contenu du modal s'adapte à la largeur disponible */
        .modal-body {
            overflow-x: hidden;
            word-wrap: break-word;
        }

        /* Gérer les textes longs dans le modal */
        .modal-body .fw-medium {
            word-break: break-word;
            overflow-wrap: break-word;
        }

        .modal-header {
            background-color: var(--light-accent);
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
            border-radius: 10px 10px 0 0;
        }

        .modal-footer {
            border-top: 1px solid var(--border-color);
            border-radius: 0 0 10px 10px;
        }

        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_processing,
        .dataTables_wrapper .dataTables_paginate {
            color: var(--text-color);
            margin-bottom: 10px;
        }

        /* Pagination styling */
        .dataTables_wrapper .dataTables_paginate {
            margin-top: 15px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            margin: 0 5px;
            padding: 8px 12px;
            border-radius: 50px;
            border: none !important;
            background: transparent !important;
            color: var(--text-color) !important;
            transition: all 0.2s ease;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            background: var(--light-accent) !important;
            color: var(--primary-color) !important;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: var(--light-accent) !important;
            color: var(--primary-color) !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
            opacity: 0.5;
        }

        /* Search box styling */
        .dataTables_wrapper .dataTables_filter {
            text-align: right;
            margin-bottom: 2px;
        }

        .dataTables_wrapper .dataTables_filter input {
            width: 300px;
            max-width: 100%;
            border: 1px solid var(--border-color);
            border-radius: 50px;
            padding: 8px 15px 8px 35px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="%236c9bcf" class="bi bi-search" viewBox="0 0 16 16"><path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/></svg>');
            background-repeat: no-repeat;
            background-position: 12px center;
            background-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .dataTables_wrapper .dataTables_filter input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(108, 155, 207, 0.25);
        }

        /* Remove outline on buttons */
        .dataTables_wrapper .dataTables_paginate .paginate_button:focus {
            outline: none !important;
            box-shadow: none !important;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            <div class="container-fluid p-4">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="page-title">Enseignants du Département</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Tableau de bord</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Enseignants du département</li>
                            </ol>
                        </nav>
                    </div>
                </div>

                <?php if ($departementId === null): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0 d-flex align-items-center">
                                        <span class="rounded-circle bg-light p-2 me-2" style="color: var(--primary-color);">
                                            <i class="fas fa-users"></i>
                                        </span>
                                        <span>Enseignants</span>
                                    </h5>
                                    <div>
                                        <button class="btn btn-sm" id="exportBtnAll" title="Exporter en Excel"
                                            style="background-color: var(--light-accent); color: var(--primary-color); border: none; border-radius: 50px; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-file-excel"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-3 mt-3">
                                    <div class="search-container">
                                        <input type="text" id="searchAllEnseignants" class="form-control search-input" placeholder="Rechercher..."
                                            style="border-radius: 50px; padding: 8px 15px 8px 35px; background-image: url('data:image/svg+xml;utf8,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; width=&quot;16&quot; height=&quot;16&quot; fill=&quot;%236c9bcf&quot; class=&quot;bi bi-search&quot; viewBox=&quot;0 0 16 16&quot;><path d=&quot;M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z&quot;/></svg>'); background-repeat: no-repeat; background-position: 12px center; background-size: 16px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);">
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table id="allEnseignantsTable" class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Nom</th>
                                                <th>Prénom</th>
                                                <th>CNI</th>
                                                <th>Email</th>
                                                <th>Département</th>
                                                <th>Rôle</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Les données seront chargées dynamiquement via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0 d-flex align-items-center">
                                        <span class="rounded-circle bg-light p-2 me-2" style="color: var(--primary-color);">
                                            <i class="fas fa-users"></i>
                                        </span>
                                        <span>Enseignants du département</span>
                                    </h5>
                                    <div>
                                        <button class="btn btn-sm" id="exportBtn" title="Exporter en Excel"
                                            style="background-color: var(--light-accent); color: var(--primary-color); border: none; border-radius: 50px; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-file-excel"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-3 mt-3">
                                    <div class="search-container">
                                        <input type="text" id="searchEnseignants" class="form-control search-input" placeholder="Rechercher..."
                                            style="border-radius: 50px; padding: 8px 15px 8px 35px; background-image: url('data:image/svg+xml;utf8,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; width=&quot;16&quot; height=&quot;16&quot; fill=&quot;%236c9bcf&quot; class=&quot;bi bi-search&quot; viewBox=&quot;0 0 16 16&quot;><path d=&quot;M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z&quot;/></svg>'); background-repeat: no-repeat; background-position: 12px center; background-size: 16px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);">
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table id="enseignantsTable" class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Nom</th>
                                                <th>Prénom</th>
                                                <th>CNI</th>
                                                <th>Email</th>
                                                <th>Rôle</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Les données seront chargées dynamiquement via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal pour afficher les détails d'un enseignant -->
    <div class="modal fade" id="enseignantDetailsModal" tabindex="-1" aria-labelledby="enseignantDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="enseignantDetailsModalLabel" style="color: var(--primary-color);">
                        <i class="fas fa-user-circle me-2"></i>Détails de l'enseignant
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                        style="background-color: var(--light-accent); opacity: 1; padding: 8px; border-radius: 50%;"></button>
                </div>
                <div class="modal-body" id="enseignantDetailsContent" style="padding: 20px;">
                    <!-- Le contenu sera chargé dynamiquement -->
                </div>
                <!-- Modal footer supprimé, l'icône de fermeture dans l'en-tête est suffisante -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

    <!-- SheetJS (pour l'export Excel) -->
    <script src="https://cdn.sheetjs.com/xlsx-0.20.0/package/dist/xlsx.full.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/sidebar.js"></script>

    <script>
        $(document).ready(function() {
            // Fonction pour formater le rôle
            function formatRole(role) {
                switch(role) {
                    case 'normal':
                    case 'Normal':
                        return 'Enseignant';
                    case 'chef de departement':
                        return 'Chef de Département';
                    case 'coordinateur':
                    case 'coordinateur de filiere':
                        return 'Coordinateur';
                    case 'vacataire':
                        return 'Vacataire';
                    case 'chef de filiere':
                        return 'Chef de Filière';
                    default:
                        return role.charAt(0).toUpperCase() + role.slice(1);
                }
            }

            <?php if ($departementId === null): ?>
            // Initialiser DataTables pour tous les enseignants
            const allTable = $('#allEnseignantsTable').DataTable({
                responsive: true,
                scrollX: false,
                autoWidth: false,
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                },
                dom: 'rt<"row mt-3"<"col-12 text-center"p>>',
                lengthChange: false,
                pageLength: 10,
                ordering: true,
                info: false,
                searching: true,
                pagingType: "simple_numbers",
                language: {
                    search: "",
                    searchPlaceholder: "Rechercher...",
                    paginate: {
                        previous: "<i class='fas fa-chevron-left'></i>",
                        next: "<i class='fas fa-chevron-right'></i>"
                    },
                    zeroRecords: "Aucun enseignant trouvé",
                    emptyTable: "Aucun enseignant disponible"
                },
                ajax: {
                    url: '../../route/enseignantRoute.php',
                    dataSrc: 'data'
                },
                columns: [
                    { data: 'nom' },
                    { data: 'prenom' },
                    { data: 'CNI' },
                    { data: 'email' },
                    { data: 'departement' },
                    {
                        data: 'role',
                        render: function(data) {
                            let badgeClass = '';
                            switch(data) {
                                case 'chef de departement':
                                    badgeClass = 'bg-danger';
                                    break;
                                case 'coordinateur':
                                case 'coordinateur de filiere':
                                    badgeClass = 'bg-warning';
                                    break;
                                case 'vacataire':
                                    badgeClass = 'bg-info';
                                    break;
                                default:
                                    badgeClass = 'bg-success';
                            }
                            return `<span class="badge ${badgeClass}">${formatRole(data)}</span>`;
                        }
                    }
                ]
            });

            // Ajouter un style de curseur pointer sur les lignes du tableau
            $('#allEnseignantsTable tbody').css('cursor', 'pointer');

            // Gérer le clic sur une ligne du tableau pour tous les enseignants
            $('#allEnseignantsTable tbody').on('click', 'tr', function() {
                const data = allTable.row(this).data();
                if (data) {
                    showEnseignantDetails(data.CNI);
                }
            });

            // Gérer la recherche personnalisée
            $('#searchAllEnseignants').on('keyup', function() {
                allTable.search(this.value).draw();
            });

            // Gérer le clic sur le bouton d'exportation pour tous les enseignants
            $('#exportBtnAll').click(function() {
                // Récupérer les données du tableau
                const data = allTable.data().toArray();

                // Créer un tableau pour l'export
                const exportData = data.map(item => ({
                    'Nom': item.nom,
                    'Prénom': item.prenom,
                    'CNI': item.CNI,
                    'Email': item.email,
                    'Département': item.departement,
                    'Rôle': formatRole(item.role)
                }));

                // Créer un workbook
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(exportData);

                // Ajouter la feuille au workbook
                XLSX.utils.book_append_sheet(wb, ws, "Tous_Enseignants");

                // Générer le fichier Excel et le télécharger
                XLSX.writeFile(wb, "Tous_Enseignants.xlsx");
            });
            <?php else: ?>
            // Initialiser DataTables pour les enseignants du département
            const table = $('#enseignantsTable').DataTable({
                responsive: true,
                scrollX: false,
                autoWidth: false,
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                },
                dom: 'rt<"row mt-3"<"col-12 text-center"p>>',
                lengthChange: false,
                pageLength: 10,
                ordering: true,
                info: false,
                searching: true,
                pagingType: "simple_numbers",
                language: {
                    search: "",
                    searchPlaceholder: "Rechercher...",
                    paginate: {
                        previous: "<i class='fas fa-chevron-left'></i>",
                        next: "<i class='fas fa-chevron-right'></i>"
                    },
                    zeroRecords: "Aucun enseignant trouvé",
                    emptyTable: "Aucun enseignant disponible"
                },
                ajax: {
                    url: '../../route/enseignantRoute.php?departement=<?php echo $departementId; ?>',
                    dataSrc: 'data'
                },
                columns: [
                    { data: 'nom' },
                    { data: 'prenom' },
                    { data: 'CNI' },
                    { data: 'email' },
                    {
                        data: 'role',
                        render: function(data) {
                            let badgeClass = '';
                            switch(data) {
                                case 'chef de departement':
                                    badgeClass = 'bg-danger';
                                    break;
                                case 'coordinateur':
                                case 'coordinateur de filiere':
                                    badgeClass = 'bg-warning';
                                    break;
                                case 'vacataire':
                                    badgeClass = 'bg-info';
                                    break;
                                default:
                                    badgeClass = 'bg-success';
                            }
                            return `<span class="badge ${badgeClass}">${formatRole(data)}</span>`;
                        }
                    }
                ]
            });
            <?php endif; ?>

            // Fonction pour afficher les détails d'un enseignant
            function showEnseignantDetails(cni) {
                // Charger les détails de l'enseignant
                $.ajax({
                    url: '../../route/enseignantRoute.php?cni=' + cni,
                    method: 'GET',
                    success: function(response) {
                        if (response.data) {
                            const enseignant = response.data;

                            // Déterminer la classe du badge pour le rôle
                            let badgeClass = '';
                            switch(enseignant.role) {
                                case 'chef de departement':
                                    badgeClass = 'bg-danger';
                                    break;
                                case 'coordinateur':
                                case 'coordinateur de filiere':
                                    badgeClass = 'bg-warning';
                                    break;
                                case 'vacataire':
                                    badgeClass = 'bg-info';
                                    break;
                                default:
                                    badgeClass = 'bg-success';
                            }

                            // Construire le contenu HTML pour les détails
                            let detailsHTML = `
                                <div class="card mb-4 border-0">
                                    <div class="card-body p-0">
                                        <div class="d-flex align-items-center mb-4">
                                            <div class="rounded-circle bg-light p-3 me-3" style="color: var(--primary-color);">
                                                <i class="fas fa-user-graduate fa-2x"></i>
                                            </div>
                                            <div>
                                                <h4 class="mb-0">${enseignant.nom} ${enseignant.prenom}</h4>
                                                <span class="badge ${badgeClass} mt-1">${formatRole(enseignant.role)}</span>
                                                <span class="text-muted ms-2">${enseignant.departement || 'Non assigné'}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-header bg-white" style="color: var(--primary-color);">
                                                <i class="fas fa-id-card me-2"></i>
                                                Informations personnelles
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-group list-group-flush">
                                                    <li class="list-group-item d-flex justify-content-between px-0">
                                                        <span class="text-muted">CNI</span>
                                                        <span class="fw-medium">${enseignant.CNI}</span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between px-0">
                                                        <span class="text-muted">Date de naissance</span>
                                                        <span class="fw-medium">${enseignant.date_naissance}</span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between px-0">
                                                        <span class="text-muted">Lieu de naissance</span>
                                                        <span class="fw-medium">${enseignant.lieu_naissance || '-'}</span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between px-0">
                                                        <span class="text-muted">Sexe</span>
                                                        <span class="fw-medium">${enseignant.sexe}</span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between px-0">
                                                        <span class="text-muted">Ville</span>
                                                        <span class="fw-medium">${enseignant.ville || '-'}</span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between px-0">
                                                        <span class="text-muted">Pays</span>
                                                        <span class="fw-medium">${enseignant.pays || '-'}</span>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-header bg-white" style="color: var(--primary-color);">
                                                <i class="fas fa-briefcase me-2"></i>
                                                Informations professionnelles
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-group list-group-flush">
                                                    <li class="list-group-item d-flex justify-content-between px-0">
                                                        <span class="text-muted">Email</span>
                                                        <span class="fw-medium">${enseignant.email}</span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between px-0">
                                                        <span class="text-muted">Téléphone</span>
                                                        <span class="fw-medium">${enseignant.tele || '-'}</span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between px-0">
                                                        <span class="text-muted">Date de début</span>
                                                        <span class="fw-medium">${enseignant.date_debut_travail || '-'}</span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between px-0">
                                                        <span class="text-muted">Département</span>
                                                        <span class="fw-medium">${enseignant.departement || 'Non assigné'}</span>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;

                            // Afficher les détails dans le modal
                            $('#enseignantDetailsContent').html(detailsHTML);
                            $('#enseignantDetailsModal').modal('show');
                        } else {
                            alert('Erreur lors du chargement des détails de l\'enseignant.');
                        }
                    },
                    error: function() {
                        alert('Erreur de communication avec le serveur.');
                    }
                });
            }

            <?php if ($departementId !== null): ?>
            // Ajouter un style de curseur pointer sur les lignes du tableau
            $('#enseignantsTable tbody').css('cursor', 'pointer');

            // Gérer le clic sur une ligne du tableau pour les enseignants du département
            $('#enseignantsTable tbody').on('click', 'tr', function() {
                const data = table.row(this).data();
                if (data) {
                    showEnseignantDetails(data.CNI);
                }
            });

            // Gérer la recherche personnalisée
            $('#searchEnseignants').on('keyup', function() {
                table.search(this.value).draw();
            });

            // Gérer le clic sur le bouton d'exportation
            $('#exportBtn').click(function() {
                // Récupérer les données du tableau
                const data = table.data().toArray();

                // Créer un tableau pour l'export
                const exportData = data.map(item => ({
                    'Nom': item.nom,
                    'Prénom': item.prenom,
                    'CNI': item.CNI,
                    'Email': item.email,
                    'Rôle': formatRole(item.role)
                }));

                // Créer un workbook
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(exportData);

                // Ajouter la feuille au workbook
                XLSX.utils.book_append_sheet(wb, ws, "Enseignants");

                // Générer le fichier Excel et le télécharger
                XLSX.writeFile(wb, "Enseignants_Departement.xlsx");
            });
            <?php endif; ?>
        });
    </script>
</body>
</html>
