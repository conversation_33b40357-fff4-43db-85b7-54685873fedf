/* Filter width adjustments to match timetable width */

/* Make the applied filters card match the width of the schedule container */
.card.animate-fade-in.mb-4 {
    margin: 30px auto;
    max-width: 95%;
    transition: all 0.3s ease;
    width: 100%; /* Ensure it takes full width of its container */
}

@media (min-width: 1200px) {
    .card.animate-fade-in.mb-4 {
        max-width: 90%;
    }
}

@media (min-width: 1600px) {
    .card.animate-fade-in.mb-4 {
        max-width: 85%;
    }
}

/* Improve the filter badges display */
.filter-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: flex-start;
}

.filter-badge {
    background-color: #f0f7ff;
    border-radius: 6px;
    padding: 8px 14px;
    font-size: 0.95rem;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
    border-left: 3px solid #1890ff;
    transition: all 0.2s ease;
    margin-bottom: 5px;
}

/* Adjust spacing between page elements */
.timetable-display-container {
    display: flex;
    flex-direction: column;
    gap: 0;
}

/* Remove extra margin from the filter card */
.card.animate-fade-in.mb-4 {
    margin-bottom: 0 !important;
    width: 100%; /* Ensure full width */
}

/* Add margin to the schedule container */
.schedule-container {
    margin-top: 15px !important;
    width: 100%; /* Ensure full width */
}

/* Ensure both filter card and schedule container have the same width */
.timetable-display-container > .card.animate-fade-in.mb-4,
.timetable-display-container > .schedule-container {
    width: 100%;
    max-width: 95%;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box; /* Include padding and border in the width calculation */
}

/* Force exact same width by using calc to account for any borders/padding */
.timetable-display-container > .card.animate-fade-in.mb-4 {
    width: calc(100% - (100% - var(--schedule-width, 100%)));
}

/* Additional specific rule to ensure exact matching */
.timetable-display-container > .card.animate-fade-in.mb-4,
.timetable-display-container > .schedule-container {
    position: relative;
    left: 0;
    right: 0;
}

@media (min-width: 1200px) {
    .timetable-display-container > .card.animate-fade-in.mb-4,
    .timetable-display-container > .schedule-container {
        max-width: 90%;
    }
}

@media (min-width: 1600px) {
    .timetable-display-container > .card.animate-fade-in.mb-4,
    .timetable-display-container > .schedule-container {
        max-width: 85%;
    }
}